{"name": "lmp-modular", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "host": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview --host"}, "dependencies": {"@babel/types": "^7.26.3", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accessible-icon": "^1.1.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.62.10", "@tanstack/react-query-devtools": "^5.62.10", "@tanstack/react-router": "^1.92.1", "@tanstack/react-table": "^8.21.3", "@zxing/browser": "^0.1.5", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "flatpickr": "^4.6.13", "framer-motion": "^11.15.0", "fuse.js": "^7.0.0", "jotai": "^2.11.3", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-flatpickr": "^3.10.13", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-number-format": "^5.4.3", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tanstack/eslint-plugin-query": "^5.62.9", "@tanstack/router-devtools": "^1.92.1", "@tanstack/router-plugin": "^1.91.1", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/react-flatpickr": "^3.8.11", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.1", "vite": "^6.0.3"}}