lmp-frontend/
├── src/
│   ├── layout/
│   │   ├── components/
│   │   ├── data/
│   │   ├── hooks/
│   │   ├── states/
│   │   └── types/
│   │
│   ├── modules/
│   │   └── [nome_modulo]/
│   │       ├── components/
│   │       ├── api/
│   │       ├── dtos/
│   │       ├── hooks/
│   │       ├── services/
│   │       ├── states/
│   │       ├── validators/
│   │       └── lib/
│   │
│   ├── shared/
│   │   ├── assets/
│   │   ├── components/
│   │   ├── data/
│   │   ├── hooks/
│   │   ├── lib/
│   │   ├── providers/
│   │   ├── routes/
│   │   ├── services/
│   │   └── types/
│   │
│   └── main.tsx



Autenticação (segurança) e autorização 
- Sistema de login com tokens JWT
- Gerenciamento de perfis e permissões
- Proteção de rotas por nível de acesso
- Renovação automática de tokens
- Controle de sessão do usuário

Produtos
- Cadastro completo de produtos de limpeza
- Categorização e classificação
- Controle de preços e margens
- Códigos de barras e SKUs
- Fotos e descrições detalhadas

Pessoas ( vendedor e cliente)
- Cadastro unificado de pessoas físicas (CPF) e jurídicas (CNPJ)
- Sistema de validação de documentos (CPF/CNPJ)
- Gerenciamento de endereços e contatos
- Categorização entre vendedor e cliente
- Histórico de interações e transações

Estoque 
- Controle de entrada e saída de produtos
- Monitoramento de níveis de estoque
- Histórico de movimentações
- Alertas de estoque baixo
- Integração com módulo de vendas

Vendas 
- Interface para processamento de vendas
- Leitor de código de barras integrado
- Seleção de cliente/vendedor
- Cálculo automático de totais
- Registro de forma de pagamento
- Atualização automática do estoque

Financeiro
- Registro de transações de entrada e saída
- Controle de contas a pagar e receber
- Relatórios financeiros
- Acompanhamento de fluxo de caixa
- Integração com módulo de vendas

