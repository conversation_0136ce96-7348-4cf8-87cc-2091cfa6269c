import { motion } from "framer-motion";
import { Bell } from "lucide-react";

interface HeaderMobileProps {
	readonly isExpanded: boolean;
	readonly isExpandedDuringScroll: boolean;
	readonly onExpandRequest: () => void;
}

export default function HeaderMobile({ isExpanded, isExpandedDuringScroll, onExpandRequest }: HeaderMobileProps) {
	const headerVariants = {
		expanded: {
			width: "100vw",
			height: "auto",
			position: "fixed" as const,
			top: 0,
			right: 0,
			zIndex: 1000,
			borderRadius: "0px",
			boxShadow: "none",
			transformOrigin: "100% 50%",
			transformBox: "border-box" as const,
			transition: { duration: 0.3 },
		},

		expandedScroll: {
			width: "100vw",
			height: "auto",
			position: "fixed" as const,
			top: 0,
			right: 0,
			zIndex: 1000,
			transformOrigin: "100% 50%",
			transformBox: "border-box" as const,
			transition: { duration: 0.3 },
		},
		pill: {
			width: "160px",
			height: "48px",
			borderRadius: "9999px",
			position: "fixed" as const,
			top: "13px",
			zIndex: 1000,
			right: "13px",
			boxShadow: "0 4px 10px rgba(0, 0, 0, 0.20)",
			transformOrigin: "100% 50%",
			transformBox: "border-box" as const,
			transition: { duration: 0.3 },
		},
	};

	let currentVariant: keyof typeof headerVariants;
	if (isExpandedDuringScroll) {
		currentVariant = "expandedScroll";
	} else if (isExpanded) {
		currentVariant = "expanded";
	} else {
		currentVariant = "pill";
	}

	return (
		<motion.header initial="expanded" animate={currentVariant} variants={headerVariants} className="overflow-hidden">
			{currentVariant === "pill" ? (
				<PillHeaderContent onExpandRequest={onExpandRequest} />
			) : (
				<FullHeaderContent isExpandedScroll={currentVariant === "expandedScroll"} />
			)}
		</motion.header>
	);
}

function FullHeaderContent({ isExpandedScroll }: Readonly<{ isExpandedScroll?: boolean }>) {
	return (
		<div
			className={`flex items-center  justify-between bg-white rounded-lg px-4 py-4 m-1 ${isExpandedScroll ? "shadow-[0_4px_12px_rgba(0,0,0,0.20)]" : "shadow-none"}`}
		>
			<div className="flex items-center space-x-4">
				<h1 className="text-mainColor text-xl font-bold">LMP</h1>
				<div>
					<p className="text-sm text-gray-500">Bem vindo de volta,</p>
					<p className="text-base font-bold text-gray-700">Jheison</p>
				</div>
			</div>
			<div className="flex items-center space-x-4">
				<Bell className="text-gray-500 hover:text-mainColor cursor-pointer" />
				<img
					src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
					alt="Avatar"
					className="w-12 h-12 rounded-full object-cover"
				/>
			</div>
		</div>
	);
}

interface PillProps {
	readonly onExpandRequest: () => void;
}

function PillHeaderContent({ onExpandRequest }: PillProps) {
	return (
		<button type="button" className="w-full h-full bg-white flex items-center justify-center" onClick={onExpandRequest}>
			<div className="flex items-center space-x-2">
				<h1 className="text-mainColor text-md font-bold">LMP</h1>
				<Bell className="w-6 h-6 text-gray-500 hover:text-mainColor cursor-pointer" />
				<img
					src="https://png.pngtree.com/thumb_back/fh260/background/20230615/pngtree-man-wearing-a-pair-of-yellow-sunglasses-in-front-of-a-image_2898170.jpg"
					alt="Avatar"
					className="w-9 h-9 rounded-full object-cover"
				/>
			</div>
		</button>
	);
}
