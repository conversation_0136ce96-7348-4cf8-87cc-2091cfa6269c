import * as AccessibleIcon from "@radix-ui/react-accessible-icon";
import { motion } from "framer-motion";
import { FoldHorizontal, UnfoldHorizontal } from "lucide-react";
import React from "react";

interface SidebarToggleButtonProps {
	currentStateSidebar: "expanded" | "collapsed";
	toggleSidebar: () => void;
}

export const SidebarToggleButton: React.FC<SidebarToggleButtonProps> = ({ currentStateSidebar, toggleSidebar }) => {
	return (
		<motion.button
			aria-label={`${currentStateSidebar === "expanded" ? "Minimizar menu" : "Expandir menu"}`}
			onClick={toggleSidebar}
			className={`flex rounded-[15px] transition-all duration-200hover:shadow-lg hover:border-mainColor/20 ${
				currentStateSidebar === "expanded" ? "w-full justify-between gap-2" : "w-[50px] justify-center"
			} h-[45px] items-center`}
			whileHover={{ scale: 1.03, y: -2 }}
			whileTap={{ scale: 0.97 }}
		>
			<div className="flex justify-center items-center w-[45px] h-[45px] relative">
				<div
					className={`absolute inset-0 rounded-full ${currentStateSidebar === "expanded" ? "bg-blue-100/30" : "bg-mainColor/10"} opacity-30 scale-75`}
				></div>
				<AccessibleIcon.Root label={`${currentStateSidebar === "expanded" ? "Minimizar menu" : "Expandir menu"}`}>
					{currentStateSidebar === "expanded" ? (
						<FoldHorizontal className="text-mainColor cursor-pointer w-4 h-4" />
					) : (
						<UnfoldHorizontal className="text-mainColor cursor-pointer w-4 h-4" />
					)}
				</AccessibleIcon.Root>
			</div>
			<motion.div
				animate={{ width: currentStateSidebar === "expanded" ? "130px" : 0 }}
				className="flex overflow-hidden justify-center items-center whitespace-nowrap"
				initial={false}
			>
				{currentStateSidebar === "expanded" && <span className="text-xs font-medium text-mainColor">Recolher menu</span>}
			</motion.div>
		</motion.button>
	);
};
