// MobileBottomBar.tsx
import { SidebarItems } from "@/layout/hooks/sidebar/get-items.hook";
import { useItemsSidebar } from "@/layout/hooks/sidebar/items-sidebar.hook";
import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { Bell, LayoutGrid, LucideProps, Search } from "lucide-react";
import { FiAlignCenter } from "react-icons/fi";
import { IconType } from "react-icons/lib";

interface MobileBottomBarProps {
	onToggleMenu: () => void;
	getItems: SidebarItems;
}

interface ItemMenuSidebarFooterProps {
	onClick: () => void;
	IconLucide: React.ForwardRefExoticComponent<Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>> | IconType;
}

export function ItemMenuSidebarFooter({ IconLucide: Icon, onClick }: ItemMenuSidebarFooterProps) {
	return (
		<button onClick={onClick} className="flex flex-col items-center justify-center text-gray-500 hover:text-mainColor transition-colors">
			<Icon size={24} />
		</button>
	);
}

export function MobileBottomBar({ onToggleMenu, getItems }: MobileBottomBarProps) {
	const { handleItemActive, menuItems, configItems } = useItemsSidebar(getItems);

	const handleClickItemMobile = ({ id, items }: { id: string; items: IItemSidebar[] }) => {
		const item = items.find(item => item.id === id);
		if (item) {
			handleItemActive(item);
		}
	};

	return (
		<div className="fixed bottom-0 left-0 right-0 m-1 rounded-lg bg-white  shadow-[0_4px_12px_rgba(0,0,0,0.35)] h-16 flex items-center justify-around px-2 z-10">
			<ItemMenuSidebarFooter IconLucide={LayoutGrid} onClick={() => handleClickItemMobile({ id: "dashboard", items: menuItems })} />
			<ItemMenuSidebarFooter IconLucide={Bell} onClick={() => handleClickItemMobile({ id: "notificacoes", items: menuItems })} />
			<ItemMenuSidebarFooter IconLucide={FiAlignCenter} onClick={onToggleMenu} />
			<ItemMenuSidebarFooter IconLucide={Search} onClick={() => {}} />
			<ItemMenuSidebarFooter IconLucide={Search} onClick={() => handleClickItemMobile({ id: "configuracoes", items: configItems })} />
		</div>
	);
}
