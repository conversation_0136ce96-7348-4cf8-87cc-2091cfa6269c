// MenuItemGrid.tsx

import { IItemSidebar, ISubItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { MenuItem } from "./menu-item-mobile";

interface MenuItemGridProps {
	readonly items: IItemSidebar[] | IItemSidebar["subItems"];
	readonly itemActive: IItemSidebar | null;
	readonly onItemClick: (item: IItemSidebar | ISubItemSidebar) => void;
	readonly activeClassName?: string;
	readonly inactiveClassName?: string;
}
export function MenuItemGrid({ items, itemActive, onItemClick, activeClassName, inactiveClassName }: MenuItemGridProps) {
	return (
		<>
			{items?.map(item => (
				<MenuItem
					key={item.id}
					id={item.id}
					label={item.label}
					accessibleDescription={item.accessibleDescription}
					icon={item.Icon}
					isActive={itemActive?.id === item.id}
					onClick={() => onItemClick(item)}
					activeClassName={activeClassName}
					inactiveClassName={inactiveClassName}
				/>
			))}
		</>
	);
}
