// MenuItem.tsx
import { AccessibleIcon } from "@radix-ui/react-accessible-icon";
import { <PERSON><PERSON><PERSON><PERSON>andler } from "react";

interface MenuItemProps {
	readonly id: string | number;
	readonly label: string;
	readonly accessibleDescription: string;
	readonly icon: React.ComponentType<{ className?: string }>;
	readonly isActive?: boolean;
	readonly onClick?: MouseEventHandler<HTMLButtonElement>;
	readonly activeClassName?: string;
	readonly inactiveClassName?: string;
}

export function MenuItem({
	label,
	accessibleDescription,
	icon: IconComponent,
	isActive,
	onClick,
	activeClassName = "bg-mainColor/15 text-mainColor",
	inactiveClassName = "bg-white text-gray-600",
}: MenuItemProps) {
	return (
		<button
			onClick={onClick}
			type="button"
			className={`
        flex flex-col items-center justify-center gap-2
        p-2 w-full h-[100px] rounded-[15px] cursor-pointer transition
        ${isActive ? activeClassName : inactiveClassName}
      `}
		>
			<AccessibleIcon label={accessibleDescription}>
				<IconComponent
					className={`
            ${isActive ? "text-mainColor" : "text-gray-500"}
            hover:text-mainColor/90 
            cursor-pointer 
            h-[30px] w-[30px]   
            flex-shrink-0    
          `}
				/>
			</AccessibleIcon>

			<span
				className="
          text-xs 
          font-medium 
          text-center 
         
          whitespace-normal  
          break-words
        "
			>
				{label}
			</span>
		</button>
	);
}
