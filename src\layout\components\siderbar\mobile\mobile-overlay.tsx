import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { IOverlayContainerProps } from "@/shared/types/containers/overlay-container.type";
import { motion } from "framer-motion";

const containerVariants = {
	hidden: { y: 50, opacity: 0 },
	visible: { y: 0, opacity: 1 },
	exit: { y: 50, opacity: 0 },
};

export function MenuOverlay({ isVisible, onClose, children }: IOverlayContainerProps) {
	return (
		<OverlayContainer isVisible={isVisible} onClose={onClose}>
			<motion.div
				className="relative bg-white bg-opacity-80 w-[90%] max-w-xl rounded-lg shadow-md p-4 overflow-y-auto"
				variants={containerVariants}
				initial="hidden"
				animate="visible"
				exit="exit"
				onClick={e => e.stopPropagation()}
			>
				{children}
			</motion.div>
		</OverlayContainer>
	);
}
