import { IBreadcrumb } from "@/shared/components/container/main-skeleton-container";
import { IItemSidebar, ISubItemSidebar } from "../types/sidebar-active-item.type";

function findBreadcrumbChain(path: string, items: (IItemSidebar | ISubItemSidebar)[]): IBreadcrumb[] | null {
	for (const item of items) {
		if (path === item.path) {
			return [
				{
					label: item.label,
					href: item.path,
					icon: item.Icon,
				},
			];
		}

		if (item.subItems && item.subItems.length > 0) {
			const subChain = findBreadcrumbChain(path, item.subItems);
			if (subChain) {
				return [
					{
						label: item.label,
						href: item.path,
						icon: item.Icon,
					},
					...subChain,
				];
			}
		}
	}

	return null;
}

export function generateBreadcrumbs(path: string, sidebarItems: IItemSidebar[]): IBreadcrumb[] {
	return findBreadcrumb<PERSON>hain(path, sidebarItems) || [];
}
