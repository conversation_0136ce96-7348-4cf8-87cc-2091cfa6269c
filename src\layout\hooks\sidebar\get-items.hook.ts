import { SIDEBAR_ITEMS } from "@/layout/data/sidebar-items.data";
import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";

export interface ISidebarService {
	getSidebarItems(): IItemSidebar[];
}

export class SidebarItems implements ISidebarService {
	public getSidebarItems(): IItemSidebar[] {
		const activeItems = SIDEBAR_ITEMS.filter(item => item.moduleActivated);
		return activeItems.map(item => ({
			...item,
			subItems: (item.subItems ?? []).filter(subItem => subItem.visibleInSidebar !== false),
		}));
	}
}
