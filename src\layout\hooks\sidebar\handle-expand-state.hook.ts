import { sidebarExpandAtom } from "@/layout/states/sidebar-expand.state";
import { useAtom } from "jotai";

export const useSidebarExpansion = () => {
	const [currentStateSidebar, setCurrentStateSidebar] = useAtom(sidebarExpandAtom);

	const toggleSidebar = () => {
		setCurrentStateSidebar(currentStateSidebar === "expanded" ? "collapsed" : "expanded");
	};

	return { currentStateSidebar, toggleSidebar };
};
