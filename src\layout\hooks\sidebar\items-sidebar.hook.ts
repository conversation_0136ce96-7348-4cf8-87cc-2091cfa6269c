import { useLocation, useNavigate } from "@tanstack/react-router";
import { useAtom } from "jotai";
import { useEffect, useMemo } from "react";
import { itemSidebarActiveAtom, subItemSidebarActiveAtom } from "../../states/sidebar-active-item.state";
import { IItemSidebar, ISubItemSidebar, IUseItemsSidebar } from "../../types/sidebar-active-item.type";
import { ISidebarService } from "./get-items.hook";

export const useItemsSidebar = (sidebarService: ISidebarService): IUseItemsSidebar => {
	const [itemActive, setItemActive] = useAtom(itemSidebarActiveAtom);
	const [, setSubItemActive] = useAtom(subItemSidebarActiveAtom);
	const navigate = useNavigate();
	const { pathname } = useLocation();

	const itemsMenuSidebar = useMemo(() => sidebarService.getSidebarItems(), [sidebarService]);

	const handleItemActive = (item: IItemSidebar, subItem?: ISubItemSidebar) => {
		setItemActive(item);
		setSubItemActive(subItem ?? null);
		navigate({ to: subItem ? subItem.path : item.path });
	};

	useEffect(() => {
		const currentItem = itemsMenuSidebar.find(item => {
			if (item.subItems && item.subItems.length > 0) {
				return item.path === pathname || item.subItems.some(sub => sub.path === pathname);
			}
			return item.path === pathname;
		});
		if (currentItem) {
			setItemActive(currentItem);
			if (currentItem.subItems?.some(sub => sub.path === pathname)) {
				const currentSubItem = currentItem.subItems.find(sub => sub.path === pathname);
				setSubItemActive(currentSubItem ?? null);
			} else {
				setSubItemActive(null);
			}
		}
	}, [pathname, itemsMenuSidebar, setItemActive, setSubItemActive]);

	const menuItems = itemsMenuSidebar.filter(item => item.type === "menu");
	const configItems = itemsMenuSidebar.filter(item => item.type === "config");
	const otherItems = itemsMenuSidebar.filter(item => item.type === "other");

	return { itemsMenuSidebar, itemActive, handleItemActive, menuItems, configItems, otherItems };
};
