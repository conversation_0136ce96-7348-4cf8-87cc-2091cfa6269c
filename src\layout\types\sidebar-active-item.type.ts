export type SidebarItemType = "menu" | "config" | "other";

export interface IItemSidebar {
	label: string;
	moduleActivated: boolean;
	id: string;
	Icon: React.ComponentType<{ className?: string }>;
	accessibleDescription: string;
	path: string;
	type?: SidebarItemType;
	subItems?: ISubItemSidebar[];
	rolesAllowed?: string[];
}

export type ISubItemSidebar = Omit<IItemSidebar, "type" | "moduleActivated"> & {
	visibleInSidebar?: boolean;
};

export interface IUseItemsSidebar {
	itemsMenuSidebar: IItemSidebar[];
	itemActive: IItemSidebar;
	handleItemActive: (item: IItemSidebar, subItem?: ISubItemSidebar) => void;
	menuItems: IItemSidebar[];
	configItems: IItemSidebar[];
	otherItems: IItemSidebar[];
}
