import { RouterProvider, createRouter } from "@tanstack/react-router";
import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import "./index.css";

import { ErrorBoundary } from "./shared/components/error-boundary";
import { GlobalProvider } from "./shared/providers/global.provider";
import { routeTree } from "./shared/routes/config";

const router = createRouter({
	routeTree,
});

const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
	const root = ReactDOM.createRoot(rootElement);
	root.render(
		<StrictMode>
			<ErrorBoundary>
				<GlobalProvider>
					<RouterProvider router={router} />
				</GlobalProvider>
			</ErrorBoundary>
		</StrictMode>
	);
}
