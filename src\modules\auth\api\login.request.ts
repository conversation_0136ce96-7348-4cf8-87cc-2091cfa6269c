import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ILoginDTO } from "../dtos/login.dto";
import { AUTH_ROUTES } from "./endpoints";

export interface ILoginSuccessReturn {
	message: string;
	data: {
		access_token: string;
		refresh_token: string;
	};
}

export const loginRequest = async ({ ...items }: ILoginDTO): Promise<ApiResponse<ILoginSuccessReturn>> => {
	return await createRequest({
		path: AUTH_ROUTES.LOGIN,
		method: "POST",
		body: items,
	});
};
