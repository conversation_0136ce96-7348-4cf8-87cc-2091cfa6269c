import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IRefreshTokenDTO, IRefreshTokenSuccessReturn } from "../dtos/refresh.dto";
import { AUTH_ROUTES } from "./endpoints";
import { resolveGlobalErrors } from "@/shared/lib/erros/handle-globals-errors.lib";
import axios from "axios";

export const refreshTokenRequest = async ({ refresh_token }: IRefreshTokenDTO): Promise<ApiResponse<IRefreshTokenSuccessReturn>> => {
	try {
		const { data, status } = await axios.post(
			`/api${AUTH_ROUTES.REFRESH}`,
			{},
			{ withCredentials: true, headers: { Authorization: `Bearer ${refresh_token}` } }
		);

		return {
			success: true,
			data,
			status,
		};
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
