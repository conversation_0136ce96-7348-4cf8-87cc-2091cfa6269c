import { ErrorDisplay } from "@/shared/components/ui/error-display";
import { LoadingState } from "@/shared/components/ui/loading-state";
import { LOADING_MESSAGES } from "@/shared/data/constants/ui/spinner.constants";
import { ReactNode } from "react";
import { useAuthState } from "../../hooks/use-auth-state.hook";
import LoginPage from "../login/login-page";

interface AuthContainerProps {
	children: ReactNode;
}

export const AuthContainer = ({ children }: AuthContainerProps) => {
	const { isLoading, profile, error, isAuthenticated } = useAuthState();

	if (error) {
		return <ErrorDisplay message={error.message} />;
	}

	if (isLoading) {
		return <LoadingState message={LOADING_MESSAGES.authentication} />;
	}

	if (!isAuthenticated) {
		return <LoginPage />;
	}

	if (!profile) {
		return <LoadingState message={LOADING_MESSAGES.userProfile} />;
	}

	return <>{children}</>;
};
