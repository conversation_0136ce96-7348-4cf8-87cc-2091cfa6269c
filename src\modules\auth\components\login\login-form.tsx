import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Checkbox } from "@/shared/components/ui/checkbox";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Link } from "@tanstack/react-router";

import { useAuthProvider } from "../../hooks/auth-provider.hook";
import { useLoginValidator } from "../../validators/login.validators";

export const LoginForm = () => {
	const methods = useLoginValidator();
	const { loginSubmit, isLoading } = useAuthProvider();

	return (
		<form onSubmit={methods.handleSubmit(loginSubmit)}>
			<div className="mb-4">
				<Label htmlFor="user">Usuário</Label>
				<Input {...methods.register("email")} placeholder="<EMAIL>" />
				{methods.formState.errors.email && <span className="text-red-500 text-xs">{methods.formState.errors.email.message}</span>}
			</div>
			<div className="mb-4">
				<Label htmlFor="password">Senha</Label>
				<Input {...methods.register("password")} className="rounded-[10px]" placeholder="*******" />
				{methods.formState.errors.password && <span className="text-red-500 text-xs">{methods.formState.errors.password.message}</span>}
			</div>
			<div className="flex items-center justify-between mb-4">
				<div className="inline-flex items-center  flex-grow space-x-2">
					<Checkbox id="terms2" />
					<Label className="cursor-pointer text-sm" htmlFor="terms2">
						Manter conectado?
					</Label>
				</div>
				<Link className="text-[#0197B2] md:text-base justify-end items-center text-xs text-end flex w-auto" to="/">
					Esqueceu sua senha?
				</Link>
			</div>

			<Button type="submit" variant="primary" className="w-full rounded-[10px]" disabled={isLoading}>
				{isLoading ? "Carregando..." : "Entrar"}
			</Button>
		</form>
	);
};
