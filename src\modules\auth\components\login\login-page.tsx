import { LogoSvg } from "@/shared/assets/logo/logo";
import { LoginForm } from "./login-form";
import { LoginDescription } from "./login.description";

import { WaveShape } from "./wave-shape";

const LoginPage = () => {
	return (
		<>
			<div className="flex md:m-0 m-2 z-10 w-full h-full flex-col md:flex-row max-w-4xl md:mx-auto bg-white rounded-[20px] shadow-lg overflow-hidden">
				<div className="w-full hidden relative md:w-1/2 p-8 bg-gradient-to-b from-[#0197B2] bg-[#0197B2] text-white md:flex flex-col justify-center items-center">
					<div className="flex items-center space-x-4">
						<LogoSvg />
					</div>
					<h2 className="mt-4 text-3xl font-bold">LMP Gestão!</h2>
					<p className="mt-2 text-center text-xs">Produtos de limpeza para sua casa ou negócio.</p>
					<WaveShape />
				</div>
				<div className="w-full z-20 md:w-1/2 p-8">
					<LoginDescription />
					<LoginForm />
				</div>
			</div>
		</>
	);
};

export default LoginPage;
