import { LoginType } from "../validators/login.validators";
import { useLoginMutation } from "./login.mutation.hook";
import { useUserProfile } from "./user-profile.hook";

export const useAuthProvider = () => {
	const { mutateAsync: loginMutation, isPending: isLoading } = useLoginMutation();
	const { refetch } = useUserProfile();

	const loginSubmit = async (data: LoginType) => {
		try {
			const response = await loginMutation(data);
			if (response) {
				refetch();
			}
		} catch (error) {
			console.error("Erro no login:", error);
		}
	};

	return {
		loginSubmit,
		isLoading,
	};
};
