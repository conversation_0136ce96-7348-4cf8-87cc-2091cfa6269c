import { useMutation } from "@tanstack/react-query";
import { useSet<PERSON><PERSON> } from "jotai";
import Cookies from "js-cookie";
import { toast } from "sonner";
import { ILoginDTO } from "../dtos/login.dto";

import { ILoginSuccessReturn, loginRequest } from "../api/login.request";
import { authStateAtom } from "../states/auth.state";

export const useLoginMutation = () => {
	const setAuthState = useSetAtom(authStateAtom);
	const mutation = useMutation<ILoginSuccessReturn, Error, ILoginDTO>({
		mutationFn: async (data: ILoginDTO) => {
			const response = await loginRequest(data);
			if (response.success) return response.data;
			throw new Error(response.data.message);
		},
		onSuccess: res => {
			Cookies.set("access_token", res.data.access_token);
			Cookies.set("refresh_token", res.data.refresh_token);
			setAuthState({
				isAuthenticated: true,
			});
		},
		onError: error => {
			toast.dismiss();
			toast.error(error.message);
		},
		retry: false,
	});

	return mutation;
};
