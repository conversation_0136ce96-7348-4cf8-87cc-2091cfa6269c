import { useSet<PERSON><PERSON> } from "jotai";
import Cookies from "js-cookie";
import { LMPStore } from "@/shared/providers/global.provider";
import { authStateAtom } from "../states/auth.state";
import { userProfileAtom } from "../states/user.state";

export const useLogout = () => {
	const setAuthState = useSetAtom(authStateAtom);
	const setUserProfile = useSetAtom(userProfileAtom);

	const logout = () => {
		Cookies.remove("access_token");
		Cookies.remove("refresh_token");
		LMPStore.set(authStateAtom, { isAuthenticated: false });
		LMPStore.set(userProfileAtom, null);
		localStorage.removeItem("authState");
		localStorage.removeItem("userProfile");

		setAuthState({ isAuthenticated: false });
		setUserProfile(null);
	};

	return { logout };
};
