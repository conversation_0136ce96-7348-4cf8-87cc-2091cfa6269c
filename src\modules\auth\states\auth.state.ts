import { createEncryptedStorage } from "@/shared/lib/create-encrypted-storage.lib";
import { atomWithStorage } from "jotai/utils";

const SECRET_KEY = import.meta.env.VITE_REACT_APP_SECRET_KEY;
const storage = createEncryptedStorage(SECRET_KEY);
export interface IAuthState {
	isAuthenticated: boolean;
	expiresAt?: number;
}

const adapter = {
	getItem: (key: string): IAuthState | null => {
		const data = storage.getItem(key);
		return data ? JSON.parse(data) : null;
	},
	setItem: (key: string, value: IAuthState | null): void => storage.setItem(key, JSON.stringify(value)),
	removeItem: (key: string): void => storage.removeItem(key),
};

export const authStateAtom = atomWithStorage<IAuthState | null>("authState", null, adapter);
