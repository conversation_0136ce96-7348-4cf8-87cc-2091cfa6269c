import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

export const LoginSchema = z.object({
	email: z.string().email({ message: "O e-mail é inválido" }),
	password: z.string().min(1, "A senha é obrigatória"),
});

export type LoginType = z.infer<typeof LoginSchema>;

export const useLoginValidator = () => {
	return useForm<LoginType>({
		resolver: zodResolver(LoginSchema),
		defaultValues: {
			email: "",
			password: "",
		},
	});
};
