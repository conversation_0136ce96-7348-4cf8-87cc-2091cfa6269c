import { buildQueryParams } from "@/shared/lib/build-query-params";
import { IFindAllBillsParamsDto } from "../../dtos/bills/find-all.dto";
import { IFindAllTransactionsParamsDto } from "../../dtos/transactions/find-all.dto";

export const FINANCIAL_ENDPOINTS = {
	CREATE_TRANSACTION: "/financial/create-transaction",
	FIND_TRANSACTIONS: ({ page, limit, ...rest }: IFindAllTransactionsParamsDto) =>
		buildQueryParams(`/financial/find-transactions/${page}/${limit}`, rest),
	CREATE_BILL: "/financial/create-bill",
	UPDATE_BILL: ({ id }: { id: number }) => `/financial/update-bill/${id}`,
	DELETE_BILL: ({ id }: { id: number }) => `/financial/delete-bill/${id}`,
	FIND_BILLS: ({ page, limit, ...rest }: IFindAllBillsParamsDto) => buildQueryParams(`/financial/find-bills/${page}/${limit}`, rest),
	FIND_BILL_BY_ID: ({ billId }: { billId: number }) => `/financial/find-bill-by-id/${billId}`,
	FIND_ACCOUNTS: "/financial/find-accounts",
} as const;
