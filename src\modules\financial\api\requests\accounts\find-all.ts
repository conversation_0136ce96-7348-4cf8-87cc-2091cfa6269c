import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export interface IAccountDto {
	id: number;
	name: string;
	balance: number;
	createdAt: string;
}

export const findAccountsRequest = async (): Promise<ApiResponse<IAccountDto[]>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.FIND_ACCOUNTS,
		method: "GET",
	});
};
