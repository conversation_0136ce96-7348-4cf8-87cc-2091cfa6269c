import { CreateBillForm } from "@/modules/financial/validators/create-bill.form";
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const createBillRequest = async (data: CreateBillForm): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.CREATE_BILL,
		method: "POST",
		body: data,
	});
};
