import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const deleteBillRequest = async ({ id }: { id: number }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.DELETE_BILL({ id }),
		method: "DELETE",
	});
};
