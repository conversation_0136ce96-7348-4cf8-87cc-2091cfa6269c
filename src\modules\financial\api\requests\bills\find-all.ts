import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IBillsDto, IFindAllBillsParamsDto } from "../../../dtos/bills/find-all.dto";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const findAllBuilsRequest = async (params: IFindAllBillsParamsDto): Promise<ApiResponse<IBillsDto>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.FIND_BILLS(params),
		method: "GET",
	});
};
