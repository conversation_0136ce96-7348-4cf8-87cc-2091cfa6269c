import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";

import { IFindBillByIdParamsDto, IFindBillByIdResponseDto } from "../../../dtos/bills/find-by-id.dto";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const findBillByIdRequest = async ({ billId }: IFindBillByIdParamsDto): Promise<ApiResponse<IFindBillByIdResponseDto>> => {
	return createRequest({
		path: FINANCIAL_ENDPOINTS.FIND_BILL_BY_ID({ billId }),
		method: "GET",
	});
};
