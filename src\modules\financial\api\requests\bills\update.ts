import { IUpdateBillParamsDto } from "@/modules/financial/dtos/bills/update.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const updateBillRequest = async ({ id, ...data }: IUpdateBillParamsDto): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.UPDATE_BILL({ id }),
		method: "PATCH",
		body: data,
	});
};
