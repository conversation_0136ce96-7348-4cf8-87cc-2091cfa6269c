import { CreateTransactionForm } from "@/modules/financial/validators/create-transaction.form";
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const createTransactionRequest = async (data: CreateTransactionForm): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.CREATE_TRANSACTION,
		method: "POST",
		body: {
			value: data.value,
			description: data.description,
			accountId: data.accountId,
		},
	});
};
