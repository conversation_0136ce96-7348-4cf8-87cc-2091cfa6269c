import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IFindAllTransactionsParamsDto, ITransactionsDto } from "../../../dtos/transactions/find-all.dto";
import { FINANCIAL_ENDPOINTS } from "../../endpoints";

export const findAllTransactionsRequest = async (params: IFindAllTransactionsParamsDto): Promise<ApiResponse<ITransactionsDto>> => {
	return await createRequest({
		path: FINANCIAL_ENDPOINTS.FIND_TRANSACTIONS(params),
		method: "GET",
	});
};
