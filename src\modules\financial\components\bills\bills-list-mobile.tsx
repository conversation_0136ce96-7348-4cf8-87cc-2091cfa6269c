import { motion } from "framer-motion";
import { Receipt, Timer } from "lucide-react";
import { IBillDto } from "../../dtos/bills/find-all.dto";
import { isPayableType } from "../../utils/bill-type-converter";
import { BillActionsDropdown } from "./bill-actions-dropdown";

interface BillsListMobileProps {
	accounts: IBillDto[];
}

export function BillsListMobile({ accounts }: BillsListMobileProps) {
	// const statusConfig = {
	// 	paid: { classes: "bg-green-50 text-green-700", text: "Pago" },
	// 	pending: { classes: "bg-amber-50 text-amber-700", text: "Pendente" },
	// 	canceled: { classes: "bg-gray-50 text-gray-700", text: "Cancelado" },
	// 	default: { classes: "bg-gray-50 text-gray-700", text: "Cancelado" },
	// };

	return (
		<div className="md:hidden space-y-4">
			{accounts.map(account => {
				return (
					<motion.div
						key={account.id}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						className="w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm"
					>
						<div className="flex items-center justify-between mb-3">
							<div className="flex items-center gap-2">
								<Receipt className="h-4 w-4 text-gray-400" />
								<span className="font-medium text-gray-900">{account.description}</span>
							</div>
							<BillActionsDropdown bill={account} />
						</div>
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<span>Tipo:</span>
								<span className={`font-medium ${isPayableType(account.type) ? "text-red-600" : "text-green-600"}`}>
									{isPayableType(account.type) ? "A pagar" : "A receber"}
								</span>
							</div>
							<div className="flex items-center gap-2 justify-end">
								<span>Valor:</span>
								<span className="font-bold text-gray-900">{account.value}</span>
							</div>
							<div className="flex items-center gap-2">
								<Timer className="h-4 w-4 text-amber-500" />
								<span>Vence em {account.dueDate}</span>
							</div>
							{/* <div className="flex items-center justify-end">
								<span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${config.classes}`}>
									{config.text}
								</span>
							</div> */}
						</div>
					</motion.div>
				);
			})}
		</div>
	);
}
