import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { motion } from "framer-motion";
import { AlertTriangle } from "lucide-react";

interface DeleteBillModalProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	isLoading?: boolean;
}

export const DeleteBillModal = ({ isOpen, onClose, onConfirm, isLoading }: DeleteBillModalProps) => {
	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md px-4 sm:px-6 relative shadow-lg overflow-hidden py-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-red-100 p-2 rounded-full">
						<AlertTriangle size={24} className="text-red-600" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">Excluir Conta</h2>
				</div>

				<div className="mb-6">
					<p className="text-gray-600">Tem certeza que deseja excluir esta conta? Esta ação não pode ser desfeita.</p>
				</div>

				<div className="flex justify-end gap-3">
					<Button type="button" onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
						Cancelar
					</Button>
					<Button type="button" onClick={onConfirm} className="bg-red-600 text-white hover:bg-red-700" disabled={isLoading}>
						{isLoading ? "Excluindo..." : "Excluir"}
					</Button>
				</div>
			</motion.div>
		</OverlayContainer>
	);
};
