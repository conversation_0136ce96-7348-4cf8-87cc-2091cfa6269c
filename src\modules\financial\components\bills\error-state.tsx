import { motion } from "framer-motion";
import { AlertTriangle } from "lucide-react";

const ErrorState = ({ message = "Erro ao carregar contas.", onRetry }: { message?: string; onRetry?: () => void }) => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 10 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0 }}
			className="flex flex-col items-center justify-center py-16 px-4"
		>
			<div className="w-16 h-16 rounded-[20px] bg-red-50 flex items-center justify-center mb-4">
				<AlertTriangle className="w-8 h-8 text-red-400" />
			</div>
			<p className="text-red-500 text-lg font-medium mb-2">{message}</p>
			{onRetry && (
				<button onClick={onRetry} className="mt-2 px-4 py-2 bg-mainColor text-white rounded-lg hover:bg-mainColor/90 transition-colors">
					Tentar novamente
				</button>
			)}
		</motion.div>
	);
};

export default ErrorState;
