import { Spinner } from "@/shared/components/ui/spinner";
import { motion } from "framer-motion";

const LoadingState = () => {
	return (
		<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="flex flex-col items-center justify-center p-8">
			<Spinner size="lg" />
			<p className="text-gray-600 mt-4">Carregando contas...</p>
		</motion.div>
	);
};

export default LoadingState;
