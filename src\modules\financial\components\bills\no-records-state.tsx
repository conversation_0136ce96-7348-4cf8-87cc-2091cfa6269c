import { motion } from "framer-motion";
import { PackageOpen } from "lucide-react";

const NoRecordsState = () => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 10 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0 }}
			className="flex flex-col items-center justify-center py-16 px-4"
		>
			<motion.div
				initial={{ scale: 0.5, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				transition={{ type: "spring", stiffness: 260, damping: 20 }}
				className="relative"
			>
				<div className="w-16 h-16 rounded-[20px] bg-gray-50 flex items-center justify-center mb-4">
					<PackageOpen className="w-8 h-8 text-gray-400" />
				</div>
			</motion.div>
			<p className="text-gray-500 text-lg font-medium">Nenhuma conta encontrada.</p>
		</motion.div>
	);
};

export default NoRecordsState;
