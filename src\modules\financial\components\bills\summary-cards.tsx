import { BadgeDollarSign, Clock, Receipt } from "lucide-react";

export function SummaryCards() {
	return (
		<section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
			<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
				<div className="flex flex-col space-y-2">
					<div className="text-gray-500">Total em Aberto</div>
					<div className="text-2xl text-gray-800 font-bold">R$ 3.289,80</div>
					<div className="text-sm text-gray-500">2 contas pendentes</div>
				</div>
				<div>
					<span className="flex h-14 w-14 bg-red-100 rounded-lg items-center justify-center">
						<Receipt size={28} className="text-red-600" />
					</span>
				</div>
			</div>
			<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
				<div className="flex flex-col space-y-2">
					<div className="text-gray-500">Vencendo Hoje</div>
					<div className="text-2xl text-gray-800 font-bold">R$ 0,00</div>
					<div className="text-sm text-gray-500">Nenhuma conta</div>
				</div>
				<div>
					<span className="flex h-14 w-14 bg-amber-100 rounded-lg items-center justify-center">
						<Clock size={28} className="text-amber-600" />
					</span>
				</div>
			</div>
			<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
				<div className="flex flex-col space-y-2">
					<div className="text-gray-500">Pagas este Mês</div>
					<div className="text-2xl text-gray-800 font-bold">R$ 299,90</div>
					<div className="text-sm text-gray-500">1 conta paga</div>
				</div>
				<div>
					<span className="flex h-14 w-14 bg-green-100 rounded-lg items-center justify-center">
						<BadgeDollarSign size={28} className="text-green-600" />
					</span>
				</div>
			</div>
		</section>
	);
}
