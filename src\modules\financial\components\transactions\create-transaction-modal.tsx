import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Input } from "@/shared/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";

import { motion } from "framer-motion";
import { ArrowDownCircle, ArrowUpCircle, BadgeDollarSign, CreditCard } from "lucide-react";
import { useState } from "react";
import { NumericFormat } from "react-number-format";

import { useFindAccounts } from "@/modules/financial/hooks/accounts/find-all.hook";
import { getBankIcon } from "@/modules/financial/utils/get-bank-icon";
import { Button } from "@/shared/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Controller, useForm } from "react-hook-form";
import { useCreateTransaction } from "../../hooks/transactions/create.hook";
import type { CreateTransactionForm } from "../../validators/create-transaction.form";
import { createTransactionSchema } from "../../validators/create-transaction.form";

interface CreateTransactionModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreateTransactionModal = ({ isOpen, onClose }: CreateTransactionModalProps) => {
	const [loading, setLoading] = useState(false);
	const createTransaction = useCreateTransaction();
	const { accounts, isLoading } = useFindAccounts();

	const form = useForm<CreateTransactionForm>({
		resolver: zodResolver(createTransactionSchema),
		defaultValues: {
			type: "income",
			value: undefined,
			description: "",
			accountId: undefined,
		},
	});

	const { register, handleSubmit, formState, reset, control } = form;
	const { errors } = formState;

	const onSubmit = async (data: CreateTransactionForm) => {
		setLoading(true);
		try {
			const payload = {
				value: data.type === "expense" ? -Math.abs(data.value) : Math.abs(data.value),
				description: data.description,
				accountId: data.accountId,
				type: data.type,
			};

			await createTransaction.mutateAsync(payload);
			reset();
			onClose();
		} finally {
			setLoading(false);
		}
	};

	const handleReset = () => {
		reset();
		onClose();
	};

	return (
		<OverlayContainer
			isVisible={isOpen}
			onClose={() => {
				handleReset();
			}}
		>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md px-4 sm:px-6 relative shadow-lg overflow-hidden py-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-mainColor/10 p-2 rounded-full">
						<BadgeDollarSign size={24} className="text-mainColor" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">Nova Transação</h2>
				</div>
				<form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
					<div>
						<label htmlFor="type" className="block text-sm font-medium text-gray-600 mb-1">
							Tipo
						</label>
						<Controller
							control={control}
							name="type"
							render={({ field }) => (
								<Select value={field.value} onValueChange={field.onChange} open={undefined}>
									<SelectTrigger id="type" className="w-full h-[45px] rounded-[10px]">
										<SelectValue placeholder="Selecione o tipo">
											{field.value && (
												<span className="flex items-center gap-2">
													{field.value === "income" ? (
														<div className="flex items-center gap-2 text-green-600">
															<ArrowUpCircle size={18} />
															<span>Receita</span>
														</div>
													) : (
														<div className="flex items-center gap-2 text-red-600">
															<ArrowDownCircle size={18} />
															<span>Despesa</span>
														</div>
													)}
												</span>
											)}
										</SelectValue>
									</SelectTrigger>
									<SelectContent className="z-[100000]">
										<SelectItem value="income">
											<div className="flex items-center gap-2 text-green-600">
												<ArrowUpCircle size={18} />
												<span>Receita</span>
												<div className="ml-2 px-2 py-0.5 rounded-full bg-green-100 text-green-600 text-xs">Entrada</div>
											</div>
										</SelectItem>
										<SelectItem value="expense">
											<div className="flex items-center gap-2 text-red-600">
												<ArrowDownCircle size={18} />
												<span>Despesa</span>
												<div className="ml-2 px-2 py-0.5 rounded-full bg-red-100 text-red-600 text-xs">Saída</div>
											</div>
										</SelectItem>
									</SelectContent>
								</Select>
							)}
						/>
						{errors.type && <span className="text-xs text-red-500 mt-1 block">{errors.type.message}</span>}
					</div>
					<div>
						<label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">
							Descrição
						</label>
						<Input
							id="description"
							type="text"
							placeholder="Ex: Pagamento de fornecedor"
							{...register("description")}
							className="w-full h-[45px] rounded-[10px]"
							autoFocus
						/>
						{errors.description && <span className="text-xs text-red-500 mt-1 block">{errors.description.message}</span>}
					</div>
					<div>
						<label htmlFor="value" className="block text-sm font-medium text-gray-600 mb-1">
							Valor
						</label>
						<div className="relative">
							<Controller
								control={control}
								name="value"
								render={({ field }) => (
									<NumericFormat
										value={field.value === undefined ? "" : field.value}
										onValueChange={values => field.onChange(values.floatValue ?? undefined)}
										thousandSeparator="."
										decimalSeparator=","
										prefix="R$ "
										decimalScale={2}
										fixedDecimalScale
										placeholder="R$ 0,00"
										customInput={Input}
										className={`w-full h-[45px] rounded-[10px] pl-10 ${
											control._formValues.type === "expense" ? "text-red-600" : "text-green-700"
										}`}
									/>
								)}
							/>
							<BadgeDollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
							{errors.value && <span className="text-xs text-red-500 mt-1 block">{errors.value.message}</span>}
						</div>
					</div>
					<div>
						<label htmlFor="account-select" className="block text-sm font-medium text-gray-600 mb-1">
							Conta
						</label>
						<div className="relative">
							<Controller
								control={control}
								name="accountId"
								render={({ field }) => (
									<Select
										value={field.value ? String(field.value) : undefined}
										onValueChange={value => field.onChange(Number(value))}
										disabled={isLoading || !accounts}
									>
										<SelectTrigger id="account-select" className="w-full h-[45px] rounded-[10px] pl-10">
											<SelectValue placeholder={isLoading ? "Carregando contas..." : "Selecione uma conta"} />
										</SelectTrigger>
										<SelectContent className="z-[100000]">
											{accounts?.map(account => (
												<SelectItem key={account.id} value={String(account.id)}>
													<span className="flex items-center gap-2">
														{getBankIcon(account.name)}
														{account.name}
													</span>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								)}
							/>
							<CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
							{errors.accountId && <span className="text-xs text-red-500 mt-1 block">{errors.accountId.message}</span>}
						</div>
					</div>
					<div className="flex justify-end gap-3 mt-2">
						<Button type="button" onClick={handleReset} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
							Cancelar
						</Button>
						<Button
							type="submit"
							className="bg-mainColor text-white hover:bg-mainColor/90"
							disabled={loading || createTransaction.isPending}
						>
							{loading || createTransaction.isPending ? "Salvando..." : "Salvar"}
						</Button>
					</div>
				</form>
			</motion.div>
		</OverlayContainer>
	);
};
