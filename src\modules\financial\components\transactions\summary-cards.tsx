import { DollarSign, Receipt, WalletCards } from "lucide-react";

export const SummaryCards = () => (
	<section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Total Recebido</div>
				<div className="text-2xl text-gray-800 font-bold">R$ 4.600,00</div>
				<div className="text-sm text-gray-500">3 transações</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-green-100 rounded-lg items-center justify-center">
					<DollarSign size={28} className="text-green-600" />
				</span>
			</div>
		</div>
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Contas Ativas</div>
				<div className="text-2xl text-gray-800 font-bold">2</div>
				<div className="text-sm text-gray-500">Em operação</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-blue-100 rounded-lg items-center justify-center">
					<WalletCards size={28} className="text-blue-600" />
				</span>
			</div>
		</div>
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Média por Transação</div>
				<div className="text-2xl text-gray-800 font-bold">R$ 1.533,33</div>
				<div className="text-sm text-gray-500">Último período</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-purple-100 rounded-lg items-center justify-center">
					<Receipt size={28} className="text-purple-600" />
				</span>
			</div>
		</div>
	</section>
);
