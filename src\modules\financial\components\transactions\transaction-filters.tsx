import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Calendar, Filter, RefreshCw, Search, WalletCards } from "lucide-react";
import { Controller, FormProvider, UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";

import { useFindAccounts } from "@/modules/financial/hooks/accounts/find-all.hook";
import { getBankIcon } from "@/modules/financial/utils/get-bank-icon";

interface TransactionFilters {
	description: string;
	date: string;
	accountId: string;
}

interface TransactionFiltersProps {
	methods: UseFormReturn<TransactionFilters>;
	onNewTransaction?: () => void;
}

export const TransactionFilters = ({ methods, onNewTransaction }: TransactionFiltersProps) => {
	const { accounts, isLoading } = useFindAccounts();

	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para refinar sua busca."
			actions={
				<Button
					className="hidden md:flex items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
					onClick={onNewTransaction}
				>
					<IoAdd size={18} />
					Nova Transação
				</Button>
			}
		>
			<FormProvider {...methods}>
				<form className="flex flex-col gap-3 md:flex-row md:items-end justify-between w-full">
					<div className="flex-1 min-w-0 md:w-[35%]">
						<label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">
							Descrição
						</label>
						<div className="relative">
							<Input
								id="description"
								type="text"
								placeholder="Buscar por descrição..."
								className="w-full h-[45px] rounded-[10px] pl-10"
								{...methods.register("description")}
							/>
							<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="flex-1 min-w-0 md:w-[25%]">
						<div className="relative">
							<Controller
								control={methods.control}
								name="date"
								render={({ field }) => (
									<DatePickerInput
										className="w-full"
										field={field}
										inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
										placeholder="Selecione uma data"
										label="Data"
									/>
								)}
							/>
							<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="flex-1 min-w-0 md:w-[25%]">
						<label htmlFor="accountId" className="block text-sm font-medium text-gray-600 mb-1">
							Conta
						</label>
						<div className="relative">
							<Controller
								control={methods.control}
								name="accountId"
								render={({ field }) => (
									<Select value={field.value ?? undefined} onValueChange={field.onChange} disabled={isLoading || !accounts}>
										<SelectTrigger id="accountId" className="w-full h-[45px] rounded-[10px] pl-10">
											<SelectValue placeholder={isLoading ? "Carregando contas..." : "Todas as contas"} />
										</SelectTrigger>
										<SelectContent>
											{accounts?.map(account => (
												<SelectItem key={account.id} value={String(account.id)}>
													<span className="flex items-center gap-2">
														{getBankIcon(account.name)}
														{account.name}
													</span>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								)}
							/>
							<WalletCards className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="md:w-auto">
						<Button
							type="button"
							variant="outline"
							className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2 whitespace-nowrap"
							onClick={() => methods.reset()}
						>
							<RefreshCw size={18} className="text-mainColor" />
							<span>Resetar Filtros</span>
						</Button>
					</div>
				</form>
			</FormProvider>
		</CardLMPContainer>
	);
};
