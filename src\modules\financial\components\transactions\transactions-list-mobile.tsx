import { motion } from "framer-motion";
import { BadgeDollarSign } from "lucide-react";
import { getBankIcon } from "../../utils/get-bank-icon";

interface Transaction {
	id: number;
	value: string;
	description: string;
	date: string;
	account: string;
}

interface TransactionsListMobileProps {
	transactions: Transaction[];
}

export const TransactionsListMobile = ({ transactions }: TransactionsListMobileProps) => {
	const formatCurrency = (value: string) => value || "R$ 0,00";

	const isNegative = (value: string) => {
		return value.startsWith("-") || value.startsWith("(") || value.includes("-R$");
	};

	const getValueColor = (value: string) => {
		if (isNegative(value)) {
			return "text-red-600";
		}
		return "text-green-600";
	};

	return (
		<div className="md:hidden space-y-4">
			{transactions.map(transaction => (
				<motion.div
					key={transaction.id}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					className="w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm"
				>
					<div className="flex items-center justify-between mb-2">
						<span className="font-medium text-gray-900">{transaction.description}</span>
						<span className={`font-bold ${getValueColor(transaction.value)} flex items-center`}>
							{!isNegative(transaction.value) && <BadgeDollarSign size={16} className="mr-1" />}
							{formatCurrency(transaction.value)}
						</span>
					</div>
					<div className="flex items-center justify-between text-sm text-gray-600">
						<span>{transaction.date}</span>
						<span className="flex items-center">
							{getBankIcon(transaction.account)}
							{transaction.account}
						</span>
					</div>
				</motion.div>
			))}
		</div>
	);
};
