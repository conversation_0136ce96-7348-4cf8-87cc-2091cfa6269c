import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ColumnDef, flexRender, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from "@tanstack/react-table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUpDown, BadgeDollarSign } from "lucide-react";
import { useState } from "react";
import { ITransactionDto } from "../../dtos/transactions/find-all.dto";
import { getBankIcon } from "../../utils/get-bank-icon";

interface TransactionsTableProps {
	transactions: ITransactionDto[];
}

export const TransactionsTable = ({ transactions }: TransactionsTableProps) => {
	const [sorting, setSorting] = useState<SortingState>([]);

	const formatCurrency = (value: string) => value || "R$ 0,00";

	const isNegative = (value: string) => {
		return value.startsWith("-") || value.startsWith("(") || value.includes("-R$");
	};

	const getValueColor = (value: string) => {
		if (isNegative(value)) {
			return "text-red-600";
		}
		return "text-green-600";
	};

	const columns: ColumnDef<ITransactionDto>[] = [
		{
			accessorKey: "description",
			header: ({ column }) => {
				const isSorted = column.getIsSorted() === "asc";
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						role="button"
						tabIndex={0}
						aria-pressed={isSorted}
						onClick={() => column.toggleSorting(isSorted)}
						onKeyDown={e => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								column.toggleSorting(isSorted);
							}
						}}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Descrição</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => <div className="text-center">{row.getValue("description")}</div>,
		},
		{
			accessorKey: "value",
			header: ({ column }) => {
				const isSorted = column.getIsSorted() === "asc";
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						role="button"
						tabIndex={0}
						aria-pressed={isSorted}
						onClick={() => column.toggleSorting(isSorted)}
						onKeyDown={e => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								column.toggleSorting(isSorted);
							}
						}}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Valor</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => {
				const value = row.getValue("value") as string;
				const formattedValue = formatCurrency(value);
				const colorClass = getValueColor(value);

				return (
					<div className={`text-center font-medium ${colorClass} flex items-center justify-center`}>
						{isNegative(value) ? null : <BadgeDollarSign size={16} className="mr-1" />}
						{formattedValue}
					</div>
				);
			},
		},
		{
			accessorKey: "date",
			header: ({ column }) => {
				const isSorted = column.getIsSorted() === "asc";
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						role="button"
						tabIndex={0}
						aria-pressed={isSorted}
						onClick={() => column.toggleSorting(isSorted)}
						onKeyDown={e => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								column.toggleSorting(isSorted);
							}
						}}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Data</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => <div className="text-center text-gray-600">{row.getValue("date")}</div>,
		},
		{
			accessorKey: "account",
			header: ({ column }) => {
				const isSorted = column.getIsSorted() === "asc";
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						role="button"
						tabIndex={0}
						aria-pressed={isSorted}
						onClick={() => column.toggleSorting(isSorted)}
						onKeyDown={e => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								column.toggleSorting(isSorted);
							}
						}}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Conta</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => {
				const account = row.getValue("account") as string;
				return (
					<div className="text-center text-gray-600 flex items-center justify-center">
						{getBankIcon(account)}
						{account}
					</div>
				);
			},
		},
	];

	const table = useReactTable({
		data: transactions,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		state: {
			sorting,
		},
	});

	return (
		<div className="hidden md:block">
			<AnimatePresence mode="wait">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: -20 }}
					transition={{ duration: 0.2 }}
					className="border rounded-[15px] border-gray-200 overflow-hidden"
				>
					<Table className="w-full rounded-[15px] text-sm">
						<TableHeader className="sticky top-0 bg-gradient-to-r from-gray-50 to-white z-10 border-b border-gray-200">
							{table.getHeaderGroups().map(headerGroup => (
								<TableRow key={headerGroup.id} className="hover:bg-transparent">
									{headerGroup.headers.map(header => (
										<TableHead key={header.id} className="py-4 px-2">
											{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
										</TableHead>
									))}
								</TableRow>
							))}
						</TableHeader>
						<TableBody className="[&>tr:nth-child(even)]:bg-[#f9fbfc] [&>tr:hover]:bg-[#f1faff] [&>tr:hover]:cursor-pointer">
							{table.getRowModel().rows.map(row => (
								<TableRow key={row.id} className="hover:bg-gray-50 transition-colors">
									{row.getVisibleCells().map(cell => (
										<TableCell key={cell.id} className="py-3 px-2">
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))}
						</TableBody>
					</Table>
				</motion.div>
			</AnimatePresence>
		</div>
	);
};
