import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { BanknoteIcon, CreditCard, ListIcon } from "lucide-react";
import { FINANCIAL_PATHS } from "./routes-paths.data";

export const FINANCIAL_SUBITEM_IDS = {
	TRANSACOES: "transacoes",
	CONTAS: "contas",
} as const;

export const FINANCIAL_CONFIG: IItemSidebar = {
	id: "financial-group",
	moduleActivated: true,
	label: "Financeiro",
	accessibleDescription: "ícone seção financeira",
	Icon: BanknoteIcon,
	type: "menu",
	path: FINANCIAL_PATHS.TRANSACOES,
	subItems: [
		{
			id: FINANCIAL_SUBITEM_IDS.TRANSACOES,
			label: "Transações",
			Icon: ListIcon,
			accessibleDescription: "ícone de transações financeiras",
			path: FINANCIAL_PATHS.TRANSACOES,
		},
		{
			id: FINANCIAL_SUBITEM_IDS.CONTAS,
			label: "Contas",
			Icon: CreditCard,
			accessibleDescription: "ícone de contas financeiras",
			path: FINANCIAL_PATHS.CONTAS,
		},
	],
};
