import { IFindAllBillsParamsDto } from "../dtos/bills/find-all.dto";
import { IFindAllTransactionsParamsDto } from "../dtos/transactions/find-all.dto";

export const FINANCIAL_QUERY_KEYS = {
	FIND_ALL_BILLS: (params: IFindAllBillsParamsDto) => ["find-all-bills", params],
	FIND_BILL_BY_ID: (billId: number) => ["find-bill-by-id", billId],
	FIND_ALL_TRANSACTIONS: (params: IFindAllTransactionsParamsDto) => ["find-all-transactions", params],
	FIND_ACCOUNTS: ["find-accounts"],
} as const;

export const FINANCIAL_MUTATIONS_KEYS = {
	CREATE_BILL: ["create-bill"],
	UPDATE_BILL: ["update-bill"],
	DELETE_BILL: ["delete-bill"],
	CREATE_TRANSACTION: ["create-transaction"],
} as const;
