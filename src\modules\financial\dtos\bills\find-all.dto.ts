export interface IFindAllBillsParamsDto {
	page: number;
	limit: number;
	type?: number;
	description?: string;
	dueDate?: string;
	paymentDate?: string;
	personId?: number;
	accountId?: number;
}

export interface IBillsDto {
	data: IBillDto[];
	total: 0;
}

export interface IBillDto {
	id: number;
	type: string;
	description: string;
	value: string;
	dueDate: string;
	paymentDate: string;
	person: string;
	// status: string;
	account: string;
	createdAt: string;
}
