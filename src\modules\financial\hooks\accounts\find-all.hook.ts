import { findAccountsRequest } from "@/modules/financial/api/requests/accounts/find-all";
import { useQuery } from "@tanstack/react-query";
import { FINANCIAL_QUERY_KEYS } from "../../data/query.keys";

export const useFindAccounts = () => {
	const query = useQuery({
		queryKey: FINANCIAL_QUERY_KEYS.FIND_ACCOUNTS,
		queryFn: async () => {
			const response = await findAccountsRequest();
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
	});

	return {
		accounts: query.data,
		isLoading: query.isPending,
		error: query.error,
	};
};
