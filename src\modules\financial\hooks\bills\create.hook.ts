import { createBillRequest } from "@/modules/financial/api/requests/bills/create";
import { CreateBillForm } from "@/modules/financial/validators/create-bill.form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { FINANCIAL_MUTATIONS_KEYS } from "../../data/query.keys";

export const useCreateBill = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: FINANCIAL_MUTATIONS_KEYS.CREATE_BILL,
		mutationFn: async (data: CreateBillForm) => {
			const res = await createBillRequest(data);
			if (!res.success) throw new Error(res.data.message);
			return res;
		},
		onSuccess: ({ data }) => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({
				queryKey: ["find-all-bills"],
				exact: false,
			});
		},
		onError: error => {
			toast.error(error?.message || "Erro ao criar conta.");
		},
	});

	return {
		onCreate: (data: CreateBillForm) => mutation.mutate(data),
		...mutation,
	};
};
