import { findAllBuilsRequest } from "@/modules/financial/api/requests/bills/find-all";
import { IFindAllBillsParamsDto } from "@/modules/financial/dtos/bills/find-all.dto";
import { useQuery } from "@tanstack/react-query";
import { FINANCIAL_QUERY_KEYS } from "../../data/query.keys";

export const useFindAllBills = (params: IFindAllBillsParamsDto) => {
	const query = useQuery({
		queryKey: FINANCIAL_QUERY_KEYS.FIND_ALL_BILLS(params),
		queryFn: async () => {
			const response = await findAllBuilsRequest(params);
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
	});

	return { bills: query.data, isLoading: query.isPending, error: query.error };
};
