import { useQuery } from "@tanstack/react-query";
import { findBillByIdRequest } from "../../api/requests/bills/find-bill-by-id";
import { FINANCIAL_QUERY_KEYS } from "../../data/query.keys";

interface IFindBillByIdHookParams {
	billId: number;
	enabled?: boolean;
}

export const useFindBillById = ({ billId, enabled = true }: IFindBillByIdHookParams) => {
	const { data, error, isLoading } = useQuery({
		queryKey: FINANCIAL_QUERY_KEYS.FIND_BILL_BY_ID(billId),
		queryFn: () => findBillByIdRequest({ billId }),
		enabled,
	});

	return {
		data,
		error,
		isLoading,
	};
};
