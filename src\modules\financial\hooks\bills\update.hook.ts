import { updateBillRequest } from "@/modules/financial/api/requests/bills/update";
import { IUpdateBillParamsDto } from "@/modules/financial/dtos/bills/update.dto";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { FINANCIAL_MUTATIONS_KEYS } from "../../data/query.keys";

export const useUpdateBill = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: FINANCIAL_MUTATIONS_KEYS.UPDATE_BILL,
		mutationFn: async (params: IUpdateBillParamsDto) => {
			const res = await updateBillRequest(params);
			if (!res.success) throw new Error(res.data.message);
			return res;
		},
		onSuccess: ({ data }) => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({ queryKey: ["find-all-bills"], exact: false });
		},
		onError: error => {
			toast.error(error?.message || "Erro ao atualizar conta.");
		},
	});

	return {
		onUpdate: (params: IUpdateBillParamsDto) => mutation.mutate(params),
		...mutation,
	};
};
