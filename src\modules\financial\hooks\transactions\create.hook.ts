import { createTransactionRequest } from "@/modules/financial/api/requests/transactions/create";
import { CreateTransactionForm } from "@/modules/financial/validators/create-transaction.form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { FINANCIAL_MUTATIONS_KEYS } from "../../data/query.keys";

export const useCreateTransaction = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: FINANCIAL_MUTATIONS_KEYS.CREATE_TRANSACTION,
		mutationFn: async (data: CreateTransactionForm) => {
			const res = await createTransactionRequest(data);
			if (!res.success) throw new Error(res.data.message);
			return res;
		},
		onSuccess: ({ data }) => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({ queryKey: ["find-all-transactions"], exact: false });
		},
		onError: error => {
			toast.error(error?.message || "Erro ao criar transação.");
		},
	});

	return {
		onCreate: (data: CreateTransactionForm) => mutation.mutate(data),
		...mutation,
	};
};
