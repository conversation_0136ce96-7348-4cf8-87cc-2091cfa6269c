import { findAllTransactionsRequest } from "@/modules/financial/api/requests/transactions/find-all";
import { IFindAllTransactionsParamsDto } from "@/modules/financial/dtos/transactions/find-all.dto";
import { useQuery } from "@tanstack/react-query";
import { FINANCIAL_QUERY_KEYS } from "../../data/query.keys";

export const useFindAllTransactions = (params: IFindAllTransactionsParamsDto) => {
	const query = useQuery({
		queryKey: FINANCIAL_QUERY_KEYS.FIND_ALL_TRANSACTIONS(params),
		queryFn: async () => {
			const response = await findAllTransactionsRequest(params);
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
	});

	return { transactions: query.data, isLoading: query.isPending };
};
