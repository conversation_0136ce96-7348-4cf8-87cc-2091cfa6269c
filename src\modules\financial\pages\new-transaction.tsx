// import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
// import { Home } from "lucide-react";
// import { FINANCIAL_CONFIG, FINANCIAL_SUBITEM_IDS } from "../data/financial-config";

// export const NewTransactionPage = () => {
// 	return (
// 		<MainSkeletonContainer
// 			pageTitle="Nova Transação"
// 			iconTitle={FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.NOVA_TRANSACAO)?.Icon}
// 			itemsBreadcrumb={[
// 				{ href: "/", label: "Página inicial", icon: Home },
// 				{
// 					href: FINANCIAL_CONFIG.path,
// 					label: "Financeiro",
// 					icon: FINANCIAL_CONFIG.Icon,
// 				},
// 				{
// 					href: FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.TRANSACOES)?.path || "",
// 					label: "Transações",
// 					icon: FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.TRANSACOES)?.Icon,
// 				},
// 			]}
// 			currentBreadcrumb={{
// 				href: FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.NOVA_TRANSACAO)?.path || "",
// 				label: "Nova Transação",
// 				icon: FINANCIAL_CONFIG.subItems?.find(item => item.id === FINANCIAL_SUBITEM_IDS.NOVA_TRANSACAO)?.Icon,
// 			}}
// 		>
// 			<div>
// 				<h1>Nova Transação</h1>
// 			</div>
// 		</MainSkeletonContainer>
// 	);
// };
