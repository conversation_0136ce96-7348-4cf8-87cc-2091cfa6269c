import { home } from "@/shared/routes/dashboard.route";
import { ModuleSubRoute, createModuleRoutes } from "@/shared/routes/module-routes.factory";
import { FINANCIAL_CONFIG, FINANCIAL_SUBITEM_IDS } from "../data/financial-config";

import { ContasPage } from "../pages/contas";
import { TransactionsListPage } from "../pages/transactions";

const financialSubRoutes: ModuleSubRoute<NonNullable<typeof FINANCIAL_CONFIG.subItems>[number]>[] = [
	{ id: FINANCIAL_SUBITEM_IDS.TRANSACOES, component: TransactionsListPage },
	{ id: FINANCIAL_SUBITEM_IDS.CONTAS, component: ContasPage },
];

export const financialRoutes = createModuleRoutes(
	{
		...FINANCIAL_CONFIG,
		subItems: FINANCIAL_CONFIG.subItems ?? [],
	},
	financialSubRoutes,
	() => home
);
