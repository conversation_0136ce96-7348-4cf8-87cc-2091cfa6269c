import { Credit<PERSON>ard, Landmark, Wallet } from "lucide-react";

export function getBankIcon(bankName: string) {
	switch (bankName.toLowerCase()) {
		case "santander":
			return <Landmark size={16} className="text-red-500 mr-1" />;
		case "nubank":
			return <CreditCard size={16} className="text-purple-500 mr-1" />;
		case "inter":
			return <CreditCard size={16} className="text-orange-500 mr-1" />;
		case "itaú":
		case "itau":
			return <Landmark size={16} className="text-blue-500 mr-1" />;
		case "banco do brasil":
			return <Landmark size={16} className="text-yellow-500 mr-1" />;
		case "caixa":
		case "caixa econômica":
			return <Landmark size={16} className="text-blue-700 mr-1" />;
		default:
			return <Wallet size={16} className="text-gray-500 mr-1" />;
	}
}
