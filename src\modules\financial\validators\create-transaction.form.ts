import { z } from "zod";

export const createTransactionSchema = z.object({
	value: z.number().min(0, { message: "O valor deve ser maior que 0" }),
	description: z.string().min(1, { message: "A descrição é obrigatória" }),
	accountId: z.number().min(1, { message: "O ID da conta é obrigatório" }),
	type: z.enum(["income", "expense"], { required_error: "Selecione o tipo da transação" }),
});

export type CreateTransactionForm = z.infer<typeof createTransactionSchema>;
