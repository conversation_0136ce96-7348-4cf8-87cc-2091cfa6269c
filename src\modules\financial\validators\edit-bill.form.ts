import { z } from "zod";

export const editBillSchema = z
	.object({
		type: z.number().optional(),
		description: z.string().optional(),
		value: z.number().min(0.01, "O valor deve ser maior que zero"),
		dueDate: z.string().optional(),
		paymentDate: z.string().optional(),
		personId: z.number().optional(),
		accountId: z.number().optional(),
	})
	.refine(
		data => {
			if (data.paymentDate && !data.accountId) {
				return false;
			}
			return true;
		},
		{
			message: "A conta é obrigatória quando há data de pagamento",
			path: ["accountId"],
		}
	);

export type EditBillForm = z.infer<typeof editBillSchema>;
