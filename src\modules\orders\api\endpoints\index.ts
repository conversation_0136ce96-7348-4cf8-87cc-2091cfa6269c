import { IFindAllOrdersParams } from "../../dtos/find-all-orders.dto";

export const ORDERS_ENDPOINTS = {
	FIND_ALL: (params: IFindAllOrdersParams) => {
		const { page, limit, orderId, customer, orderStatus } = params;
		const query = `/order/find-all/${page}/${limit}`;
		const searchParams = new URLSearchParams();
		
		if (orderId) searchParams.append("orderId", orderId);
		if (customer) searchParams.append("customer", customer);
		if (orderStatus !== undefined) searchParams.append("orderStatus", orderStatus.toString());
		
		const queryString = searchParams.toString();
		return queryString ? `${query}?${queryString}` : query;
	},
};
