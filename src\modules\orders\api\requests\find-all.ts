import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IFindAllOrdersParams, IFindAllOrdersRequest } from "../../dtos/find-all-orders.dto";
import { ORDERS_ENDPOINTS } from "../endpoints";

export const findAllOrdersRequest = async (params: IFindAllOrdersParams): Promise<ApiResponse<IFindAllOrdersRequest>> => {
	return createRequest({
		path: ORDERS_ENDPOINTS.FIND_ALL(params),
		method: "GET",
	});
};
