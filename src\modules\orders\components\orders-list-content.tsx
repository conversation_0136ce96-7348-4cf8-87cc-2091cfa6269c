import { IOrderDto } from "../dtos/order.dto";
import { OrdersListMobile } from "./orders-list-mobile";
import { OrdersTable } from "./orders-table";

interface OrdersListContentProps {
	orders: IOrderDto[];
	isLoading: boolean;
	queryError: unknown;
	tableKey: string;
}

export function OrdersListContent({ orders, isLoading }: OrdersListContentProps) {
	return (
		<>
			<div className="hidden md:block">
				<OrdersTable orders={orders || []} isLoading={isLoading} />
			</div>

			<OrdersListMobile orders={orders || []} isLoading={isLoading} />
		</>
	);
}
