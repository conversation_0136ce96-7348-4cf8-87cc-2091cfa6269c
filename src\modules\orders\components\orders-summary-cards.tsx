import { CheckCir<PERSON>, Clock, ShoppingCart } from "lucide-react";

export const OrdersSummaryCards = () => (
	<section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Total de Pedidos</div>
				<div className="text-2xl text-gray-800 font-bold">215</div>
				<div className="text-sm text-gray-500">Registrados</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-blue-100 rounded-lg items-center justify-center">
					<ShoppingCart size={28} className="text-blue-600" />
				</span>
			</div>
		</div>
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Pedidos Pendentes</div>
				<div className="text-2xl text-gray-800 font-bold">42</div>
				<div className="text-sm text-gray-500">Em processamento</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-yellow-100 rounded-lg items-center justify-center">
					<Clock size={28} className="text-yellow-600" />
				</span>
			</div>
		</div>
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Pedidos Entregues</div>
				<div className="text-2xl text-gray-800 font-bold">173</div>
				<div className="text-sm text-gray-500">Concluídos</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-green-100 rounded-lg items-center justify-center">
					<CheckCircle size={28} className="text-green-600" />
				</span>
			</div>
		</div>
	</section>
);
