import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ArrowUpDown, ShoppingCart, User } from "lucide-react";
import { useState } from "react";

import { formatCurrency } from "@/shared/lib/format-currency";
import { formatDateTime } from "@/shared/lib/format-date";
import { getOrderStatusConfig } from "@/shared/utils/order-status-config";
import { IOrderDto } from "../dtos/order.dto";

interface OrdersTableProps {
	orders: IOrderDto[];
	isLoading?: boolean;
}

export function OrdersTable({ orders, isLoading }: OrdersTableProps) {
	const [sortField, setSortField] = useState<"id" | "customer" | "status" | "total" | "createdAt">("id");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

	function handleSort(field: typeof sortField) {
		if (sortField === field) {
			setSortOrder(sortOrder === "asc" ? "desc" : "asc");
		} else {
			setSortField(field);
			setSortOrder("asc");
		}
	}

	const sortedOrders = [...orders].sort((a, b) => {
		let aValue = a[sortField];
		let bValue = b[sortField];
		if (typeof aValue === "string" && typeof bValue === "string") {
			aValue = aValue.toLowerCase();
			bValue = bValue.toLowerCase();
		}
		if (aValue < bValue) return sortOrder === "asc" ? -1 : 1;
		if (aValue > bValue) return sortOrder === "asc" ? 1 : -1;
		return 0;
	});

	if (isLoading) {
		return (
			<div className="flex items-center justify-center h-32">
				<div className="text-gray-500">Carregando pedidos...</div>
			</div>
		);
	}

	if (!orders || orders.length === 0) {
		return (
			<div className="flex items-center justify-center h-32">
				<div className="text-gray-500">Nenhum pedido encontrado</div>
			</div>
		);
	}

	return (
		<div className="rounded-md border overflow-x-auto">
			<Table className="min-w-[700px] text-sm">
				<TableHeader>
					<TableRow>
						<TableHead onClick={() => handleSort("id")} className="cursor-pointer select-none group">
							<span className="flex items-center gap-1">
								<ShoppingCart size={14} /> ID
								<ArrowUpDown
									size={14}
									className={`transition-transform ${sortField === "id" ? "text-mainColor" : "text-gray-400"} ${sortField === "id" && sortOrder === "desc" ? "rotate-180" : ""}`}
								/>
							</span>
						</TableHead>
						<TableHead onClick={() => handleSort("customer")} className="cursor-pointer select-none group">
							<span className="flex items-center gap-1">
								<User size={14} /> Cliente
								<ArrowUpDown
									size={14}
									className={`transition-transform ${sortField === "customer" ? "text-mainColor" : "text-gray-400"} ${sortField === "customer" && sortOrder === "desc" ? "rotate-180" : ""}`}
								/>
							</span>
						</TableHead>
						<TableHead onClick={() => handleSort("status")} className="cursor-pointer select-none group">
							<span className="flex items-center gap-1">
								Status
								<ArrowUpDown
									size={14}
									className={`transition-transform ${sortField === "status" ? "text-mainColor" : "text-gray-400"} ${sortField === "status" && sortOrder === "desc" ? "rotate-180" : ""}`}
								/>
							</span>
						</TableHead>
						<TableHead onClick={() => handleSort("total")} className="cursor-pointer select-none group">
							<span className="flex items-center gap-1">
								Total
								<ArrowUpDown
									size={14}
									className={`transition-transform ${sortField === "total" ? "text-mainColor" : "text-gray-400"} ${sortField === "total" && sortOrder === "desc" ? "rotate-180" : ""}`}
								/>
							</span>
						</TableHead>
						<TableHead onClick={() => handleSort("createdAt")} className="cursor-pointer select-none group">
							<span className="flex items-center gap-1">
								Data de Criação
								<ArrowUpDown
									size={14}
									className={`transition-transform ${sortField === "createdAt" ? "text-mainColor" : "text-gray-400"} ${sortField === "createdAt" && sortOrder === "desc" ? "rotate-180" : ""}`}
								/>
							</span>
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{sortedOrders.map(order => {
						const config = getOrderStatusConfig(order.status) || {
							label: order.status,
							color: "bg-gray-100 text-gray-500",
							icon: () => null,
						};
						const StatusIcon = config.icon;
						return (
							<TableRow key={order.id}>
								<TableCell className="font-medium">#{order.id}</TableCell>
								<TableCell>{order.customer}</TableCell>
								<TableCell>
									<span className={`inline-flex items-center gap-1 px-2 py-1 rounded-xl text-xs font-semibold ${config.color}`}>
										<StatusIcon size={14} /> {config.label}
									</span>
								</TableCell>
								<TableCell className="font-bold text-green-600">{formatCurrency(order.total)}</TableCell>
								<TableCell className="text-xs text-gray-500">{formatDateTime(order.createdAt)}</TableCell>
							</TableRow>
						);
					})}
				</TableBody>
			</Table>
		</div>
	);
}
