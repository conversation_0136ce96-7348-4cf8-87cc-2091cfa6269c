import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { ShoppingCart } from "lucide-react";
import { ORDERS_PATHS } from "./routes-paths.data";

export const ORDERS_SUBITEM_IDS = {
	ALL_ORDERS: "all-orders",
} as const;

export const ORDERS_CONFIG: IItemSidebar = {
	id: "orders-group",
	moduleActivated: true,
	label: "Pedidos",
	accessibleDescription: "ícone seção de pedidos",
	Icon: ShoppingCart,
	type: "menu",
	path: ORDERS_PATHS.ALL_ORDERS,
	subItems: [
		{
			id: ORDERS_SUBITEM_IDS.ALL_ORDERS,
			label: "Pedidos",
			Icon: ShoppingCart,
			accessibleDescription: "ícone de listagem de pedidos",
			path: ORDERS_PATHS.ALL_ORDERS,
		},
	],
};
