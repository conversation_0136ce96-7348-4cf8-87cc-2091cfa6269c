import { useState } from "react";

export interface UseOrderModalProps {
	initialState?: boolean;
}

export interface UseOrderModalReturn {
	isModalOpen: boolean;
	openModal: () => void;
	closeModal: () => void;
	toggleModal: () => void;
}

export function useOrderModal({ initialState = false }: UseOrderModalProps = {}): UseOrderModalReturn {
	const [isModalOpen, setIsModalOpen] = useState(initialState);

	const openModal = () => setIsModalOpen(true);
	const closeModal = () => setIsModalOpen(false);
	const toggleModal = () => setIsModalOpen(prev => !prev);

	return {
		isModalOpen,
		openModal,
		closeModal,
		toggleModal,
	};
}
