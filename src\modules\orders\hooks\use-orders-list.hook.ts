import { UsePaginationReturn } from "@/shared/hooks/utils/use-pagination.hook";
import { useMemo } from "react";
import { UseFormReturn } from "react-hook-form";
import { IOrderDto } from "../dtos/order.dto";
import { OrderStatusEnum } from "../enums/order-status.enum";
import { useOrdersFindAll } from "./find-all-orders.hook";
export interface OrderFiltersForm {
	orderId: string;
	customer: string;
	orderStatus: string;
}

export interface UseOrdersListProps {
	methods: UseFormReturn<OrderFiltersForm>;
	pagination: UsePaginationReturn;
}

export interface UseOrdersListReturn {
	orders: IOrderDto[];
	isLoading: boolean;
	queryError: unknown;
	totalPages: number;
	tableKey: string;
}

export function useOrdersList({ methods, pagination }: UseOrdersListProps): UseOrdersListReturn {
	const { page, itemsPerPage } = pagination;
	const filters = methods.watch();

	const {
		data: orders,
		isLoading,
		error: queryError,
	} = useOrdersFindAll({
		page,
		limit: itemsPerPage,
		orderId: filters.orderId || undefined,
		customer: filters.customer || undefined,
		orderStatus: filters.orderStatus !== "all" ? (Number(filters.orderStatus) as OrderStatusEnum) : undefined,
	});

	const totalPages = useMemo(() => {
		if (!orders?.success) return 1;

		if (!orders?.data?.total) return 1;
		return Math.ceil(orders.data.total / itemsPerPage);
	}, [orders, itemsPerPage]);

	const tableKey = useMemo(() => {
		return `${page}-${itemsPerPage}-${filters.orderId}-${filters.customer}-${filters.orderStatus}`;
	}, [page, itemsPerPage, filters]);

	return {
		orders: orders?.success ? orders.data.data : [],
		isLoading,
		queryError,
		totalPages,
		tableKey,
	};
}
