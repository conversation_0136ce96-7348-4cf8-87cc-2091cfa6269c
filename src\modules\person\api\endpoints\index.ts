import { buildQueryParams } from "@/shared/lib/build-query-params";
import { IFindAllParams } from "../requests/find-all";

export const PERSON_ENDPOINTS = {
	CREATE: "/person/create",
	FIND_ALL_CUSTOMERS: ({ page, limit, name, document }: { page: number; limit: number; name?: string; document?: string }) => {
		const query = `/person/find-all-customers/${page}/${limit}`;
		const params = new URLSearchParams();
		if (name) params.append("name", name);
		if (document) params.append("document", document);
		const queryString = params.toString();
		return queryString ? `${query}?${queryString}` : query;
	},
	find_ALL_SUPPLIERS: ({ page, limit, filter }: { page: number; limit: number; filter?: string }) => {
		const query = `/person/find-all-suppliers/${page}/${limit}`;
		const params = new URLSearchParams();
		if (filter) params.append("filter", filter);
		const queryString = params.toString();
		return queryString ? `${query}?${queryString}` : query;
	},
	FIND_ALL: ({ page, limit, search, classification }: IFindAllParams) =>
		buildQueryParams(`/person/find-all/${page}/${limit}`, {
			search,
			classification,
		}),
} as const;
