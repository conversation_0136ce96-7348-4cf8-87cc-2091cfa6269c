import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IGetAllCustomersDto } from "../../dtos/person.dto";
import { PERSON_ENDPOINTS } from "../endpoints";

interface FindAllCustomersParams {
	page: number;
	limit: number;
	name?: string;
	document?: string;
}

export const findAllCustomers = (params: FindAllCustomersParams): Promise<ApiResponse<IGetAllCustomersDto>> => {
	return createRequest({
		path: PERSON_ENDPOINTS.FIND_ALL_CUSTOMERS(params),
		method: "GET",
	});
};
