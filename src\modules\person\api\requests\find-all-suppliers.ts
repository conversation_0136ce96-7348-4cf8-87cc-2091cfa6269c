import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IFindAllSuppliersResponse } from "../../dtos/find-all-suppliers.dto";
import { PERSON_ENDPOINTS } from "../endpoints";

export const findAllSuppliers = async ({
	page,
	limit,
	filter = "",
}: {
	page: number;
	limit: number;
	filter?: string;
}): Promise<ApiResponse<IFindAllSuppliersResponse>> => {
	return createRequest<IFindAllSuppliersResponse>({
		path: PERSON_ENDPOINTS.find_ALL_SUPPLIERS({
			page,
			limit,
			filter,
		}),
		method: "GET",
	});
};
