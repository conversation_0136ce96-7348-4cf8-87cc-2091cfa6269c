import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";

import { IPersonFindAllRequest } from "../../dtos/find-all.dto";

import { PersonClassificationEnum } from "../../enums/person-classification.enum";
import { PERSON_ENDPOINTS } from "../endpoints";
export interface IFindAllParams {
	page: number;
	limit: number;
	search?: string;
	classification?: PersonClassificationEnum;
}
export const findAllRequest = async (params: IFindAllParams): Promise<ApiResponse<IPersonFindAllRequest>> => {
	return createRequest({
		path: PERSON_ENDPOINTS.FIND_ALL(params),
		method: "GET",
	});
};
