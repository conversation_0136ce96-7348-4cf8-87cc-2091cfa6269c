import { CreatePersonSchemaType } from "@/modules/person/validators/create-person.validator";
import { FormInputWithIcon } from "@/shared/components/custom/input-label-icon";
import { Building2, Hash, Landmark, Mail, Map, MapPin } from "lucide-react";
import { Control, useFormContext } from "react-hook-form";

interface AddressSectionProps {
	control: Control<CreatePersonSchemaType>;
}

export const AddressSection = ({ control }: AddressSectionProps) => {
	const {
		formState: { errors },
	} = useFormContext<CreatePersonSchemaType>();
	const address = useFormContext<CreatePersonSchemaType>().watch("address");

	type AddressKeys = keyof NonNullable<CreatePersonSchemaType["address"]>;
	const requiredFields: AddressKeys[] = ["postalCode", "street", "number", "neighborhood", "city", "state"];
	const hasAnyField = requiredFields.some(field => !!address?.[field]?.trim());

	return (
		<section className="flex flex-col gap-2 mt-6">
			<h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
				<MapPin size={20} />
				<span>Endereço</span>
				<div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
			</h4>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div className="w-full">
					<label htmlFor="address.postalCode" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						CEP{hasAnyField && <span className="text-red-500">*</span>}
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.postalCode"
						label=""
						placeholder="00000-000"
						icon={Mail}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{hasAnyField && !address?.postalCode?.trim() && <span className="text-xs text-red-500 mt-1 block">CEP é obrigatório</span>}
					{errors.address?.postalCode && (
						<span className="text-xs text-red-500 mt-1 block">{errors.address.postalCode.message as string}</span>
					)}
				</div>
				<div className="w-full">
					<label htmlFor="address.street" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Rua{hasAnyField && <span className="text-red-500">*</span>}
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.street"
						label=""
						placeholder="Nome da rua"
						icon={MapPin}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{hasAnyField && !address?.street?.trim() && <span className="text-xs text-red-500 mt-1 block">Rua é obrigatória</span>}
					{errors.address?.street && <span className="text-xs text-red-500 mt-1 block">{errors.address.street.message as string}</span>}
				</div>
				<div className="w-full">
					<label htmlFor="address.number" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Número{hasAnyField && <span className="text-red-500">*</span>}
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.number"
						label=""
						placeholder="Número"
						icon={Hash}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{hasAnyField && !address?.number?.trim() && <span className="text-xs text-red-500 mt-1 block">Número é obrigatório</span>}
					{errors.address?.number && <span className="text-xs text-red-500 mt-1 block">{errors.address.number.message as string}</span>}
				</div>
				<div className="w-full">
					<label htmlFor="address.complement" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Complemento
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.complement"
						label=""
						placeholder="Apto, bloco, etc."
						icon={Building2}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{/* Complemento não é obrigatório */}
				</div>
				<div className="w-full">
					<label htmlFor="address.neighborhood" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Bairro{hasAnyField && <span className="text-red-500">*</span>}
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.neighborhood"
						label=""
						placeholder="Nome do bairro"
						icon={Landmark}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{hasAnyField && !address?.neighborhood?.trim() && <span className="text-xs text-red-500 mt-1 block">Bairro é obrigatório</span>}
					{errors.address?.neighborhood && (
						<span className="text-xs text-red-500 mt-1 block">{errors.address.neighborhood.message as string}</span>
					)}
				</div>
				<div className="w-full">
					<label htmlFor="address.city" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Cidade{hasAnyField && <span className="text-red-500">*</span>}
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.city"
						label=""
						placeholder="Nome da cidade"
						icon={Building2}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{hasAnyField && !address?.city?.trim() && <span className="text-xs text-red-500 mt-1 block">Cidade é obrigatória</span>}
					{errors.address?.city && <span className="text-xs text-red-500 mt-1 block">{errors.address.city.message as string}</span>}
				</div>
				<div className="md:col-span-2">
					<label htmlFor="address.state" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Estado{hasAnyField && <span className="text-red-500">*</span>}
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="address.state"
						label=""
						placeholder="UF"
						icon={Map}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{hasAnyField && !address?.state?.trim() && <span className="text-xs text-red-500 mt-1 block">Estado é obrigatório</span>}
					{errors.address?.state && <span className="text-xs text-red-500 mt-1 block">{errors.address.state.message as string}</span>}
				</div>
			</div>
		</section>
	);
};
