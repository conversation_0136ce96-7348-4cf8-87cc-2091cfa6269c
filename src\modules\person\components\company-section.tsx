import { CreatePersonSchemaType } from "@/modules/person/validators/create-person.validator";
import { FormInputWithIcon } from "@/shared/components/custom/input-label-icon";
import { Briefcase, FileText } from "lucide-react";
import { Control, useFormContext } from "react-hook-form";
import { PersonTypeEnum } from "../enums/person-classification.enum";

interface CompanySectionProps {
	control: Control<CreatePersonSchemaType>;
	type: number;
}

export const CompanySection = ({ control, type }: CompanySectionProps) => {
	const {
		formState: { errors },
	} = useFormContext<CreatePersonSchemaType>();

	if (type !== PersonTypeEnum.Legal) return null;
	return (
		<section className="flex flex-col gap-2 mt-6">
			<h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
				<Briefcase size={20} />
				<span>Dados Empresariais</span>
				<div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
			</h4>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div className="w-full">
					<label htmlFor="tradeName" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Nome Empresarial <span className="text-red-500">*</span>
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="tradeName"
						label=""
						placeholder="Nome da empresa/Razão social"
						icon={Briefcase}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{!errors.tradeName && <span className="text-xs text-red-500 mt-1 block">Nome Empresarial é obrigatório</span>}
					{errors.tradeName && <span className="text-xs text-red-500 mt-1 block">{errors.tradeName.message as string}</span>}
				</div>
				<div className="w-full">
					<label htmlFor="ie" className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Inscrição Estadual <span className="text-red-500">*</span>
					</label>
					<FormInputWithIcon<CreatePersonSchemaType>
						name="ie"
						label=""
						placeholder="Número da Inscrição Estadual"
						icon={FileText}
						control={control}
						errors={errors}
						className="w-full"
					/>
					{!errors.ie && <span className="text-xs text-red-500 mt-1 block">Inscrição Estadual é obrigatória</span>}
					{errors.ie && <span className="text-xs text-red-500 mt-1 block">{errors.ie.message as string}</span>}
				</div>
			</div>
		</section>
	);
};
