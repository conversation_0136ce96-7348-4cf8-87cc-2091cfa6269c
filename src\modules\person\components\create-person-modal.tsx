import { useCreatePersonMutation } from "@/modules/person/hooks/create-person-mutation.hook";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowLeft, ArrowRight, Building, CheckCircle2, ChevronsRight, Clipboard, Home, User2, UserCircle, Users } from "lucide-react";
import { useState } from "react";
import { FormProvider } from "react-hook-form";
import { PersonTypeEnum } from "../enums/person-classification.enum";
import { useCreatePerson } from "../hooks/use-create-person.hook";
import { AddressSection } from "./address-section";
import { CompanySection } from "./company-section";
import { MainDataSection } from "./main-data-section";

interface CreatePersonModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreatePersonModal = ({ isOpen, onClose }: CreatePersonModalProps) => {
	const [currentStep, setCurrentStep] = useState(0);
	const steps = [
		{ id: 0, title: "Tipo de Pessoa", icon: <Users size={18} /> },
		{ id: 1, title: "Dados Principais", icon: <User2 size={18} /> },
		{ id: 2, title: "Empresa", icon: <Building size={18} /> },
		{ id: 3, title: "Endereço", icon: <Home size={18} /> },
		{ id: 4, title: "Confirmação", icon: <CheckCircle2 size={18} /> },
	];

	const { createPerson, isLoading } = useCreatePersonMutation({ closeModal: onClose });
	const { form, onSubmit } = useCreatePerson({ createPerson });
	const { control, watch, register } = form;

	const type = watch("type");

	// Observa os campos relevantes para validação dinâmica
	const watchedName = watch("name");
	const watchedCpfCnpj = watch("cnpjCpf");
	const watchedEmail = watch("email");
	const watchedPhone = watch("phone");
	const watchedTradeName = watch("tradeName");
	const watchedIe = watch("ie");
	const watchedAddress = watch("address");

	const handleReset = () => {
		form.reset();
		setCurrentStep(0);
		onClose();
	};
	const nextStep = () => {
		if (currentStep < steps.length - 1) {
			setCurrentStep(prev => prev + 1);
			setTimeout(() => {
				const contentDiv = document.querySelector(".modal-content-scroll");
				if (contentDiv) {
					contentDiv.scrollTop = 0;
				}
			}, 100);
		}
	};

	const prevStep = () => {
		if (currentStep > 0) {
			setCurrentStep(prev => prev - 1);
			setTimeout(() => {
				const contentDiv = document.querySelector(".modal-content-scroll");
				if (contentDiv) {
					contentDiv.scrollTop = 0;
				}
			}, 100);
		}
	};

	// Função para checar se os campos da etapa atual estão válidos
	const isStepValid = () => {
		if (currentStep === 0) {
			return true;
		}
		if (currentStep === 1) {
			const isNameValid = !!watchedName?.trim();
			const isCpfCnpjValid = !!watchedCpfCnpj?.trim();
			if (!isNameValid || !isCpfCnpjValid) return false;
			if (type === PersonTypeEnum.Legal) {
				return !!watchedEmail?.trim() && !!watchedPhone?.trim();
			}
			return true;
		}
		if (currentStep === 2) {
			if (type === PersonTypeEnum.Legal) {
				return !!watchedTradeName?.trim() && !!watchedIe?.trim();
			}
			return true;
		}
		if (currentStep === 3) {
			const address = watchedAddress;
			if (!address) return true;
			const requiredFields = ["street", "number", "neighborhood", "city", "state", "postalCode"] as const;
			const hasAnyField = requiredFields.some(field => !!address[field]?.trim());
			// Se nenhum campo foi preenchido, endereço é opcional
			if (!hasAnyField) return true;
			// Se algum campo foi preenchido, todos são obrigatórios (exceto complemento)
			return requiredFields.every(field => !!address[field]?.trim());
		}
		return true;
	};

	const handleNext = () => {
		if (isStepValid()) {
			if (currentStep === steps.length - 1) {
				form.handleSubmit(onSubmit)();
			} else {
				nextStep();
			}
		}
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<div className="space-y-6">
						<div className="text-center mb-8">
							<motion.div
								initial={{ scale: 0.8, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								transition={{ duration: 0.3 }}
								className="inline-flex p-3 rounded-full bg-cyan-100 mb-4"
							>
								<Users size={28} className="text-mainColor" />
							</motion.div>
							<h3 className="text-xl font-medium text-gray-700">Tipo de Pessoa</h3>
							<p className="text-gray-500 text-sm mt-2">Escolha entre Pessoa Física ou Pessoa Jurídica para continuar</p>
						</div>
						<div className="grid grid-cols-1 gap-5 max-w-lg mx-auto">
							<button
								type="button"
								onClick={() => {
									form.setValue("type", PersonTypeEnum.Physical);
									nextStep();
								}}
								className={`flex items-center p-4 border rounded-xl transition-all ${
									type === PersonTypeEnum.Physical
										? "border-mainColor bg-cyan-50/50"
										: "border-gray-100 hover:border-cyan-200 hover:bg-cyan-50/30"
								}`}
							>
								<div className="p-3 rounded-full bg-cyan-100 mr-4">
									<User2 size={22} className="text-mainColor" />
								</div>
								<div className="text-left">
									<h4 className="font-medium text-gray-700">Pessoa Física</h4>
									<p className="text-sm text-gray-500">Cadastro para indivíduos e autônomos</p>
								</div>
								{type === PersonTypeEnum.Physical && <CheckCircle2 size={22} className="text-mainColor ml-auto" />}
							</button>

							<button
								type="button"
								onClick={() => {
									form.setValue("type", PersonTypeEnum.Legal);
									nextStep();
								}}
								className={`flex items-center p-4 border rounded-xl transition-all ${
									type === PersonTypeEnum.Legal
										? "border-mainColor bg-cyan-50/50"
										: "border-gray-100 hover:border-cyan-200 hover:bg-cyan-50/30"
								}`}
							>
								<div className="p-3 rounded-full bg-cyan-100 mr-4">
									<Building size={22} className="text-mainColor" />
								</div>
								<div className="text-left">
									<h4 className="font-medium text-gray-700">Pessoa Jurídica</h4>
									<p className="text-sm text-gray-500">Cadastro para empresas e organizações</p>
								</div>
								{type === PersonTypeEnum.Legal && <CheckCircle2 size={22} className="text-mainColor ml-auto" />}
							</button>
						</div>
					</div>
				);
			case 1:
				return (
					<>
						<MainDataSection register={register} control={control} onCnpjChange={() => {}} />
					</>
				);
			case 2:
				return type === PersonTypeEnum.Legal ? (
					<CompanySection control={control} type={type} />
				) : (
					<div className="text-center py-12">
						<motion.div
							initial={{ scale: 0.8, opacity: 0 }}
							animate={{ scale: 1, opacity: 1 }}
							className="inline-flex p-4 rounded-full bg-cyan-100 mb-4"
						>
							<ArrowRight size={28} className="text-mainColor" />
						</motion.div>
						<h3 className="text-xl font-medium text-gray-700">Etapa não necessária</h3>
						<p className="text-gray-500 mt-3 max-w-md mx-auto">
							Esta etapa é aplicável apenas para Pessoa Jurídica. Você pode continuar para a próxima etapa.
						</p>
						<Button onClick={nextStep} className="mt-6 bg-mainColor text-white hover:bg-mainColor/90 px-6">
							Continuar <ChevronsRight size={16} className="ml-2" />
						</Button>
					</div>
				);
			case 3:
				return <AddressSection control={control} />;
			case 4:
				return (
					<div className="space-y-5">
						<div className="text-center mb-6">
							<motion.div
								initial={{ scale: 0.8, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								className="inline-flex p-3 rounded-full bg-cyan-100 mb-3"
							>
								<CheckCircle2 size={26} className="text-mainColor" />
							</motion.div>
							<h3 className="text-lg font-medium text-gray-700">Confirmação</h3>
							<p className="text-gray-500 text-xs mt-1">Verifique os dados antes de finalizar</p>
						</div>
						<div className="space-y-3 max-w-lg mx-auto">
							<div className="bg-gray-50/70 p-3 rounded-lg">
								<h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1.5 text-sm">
									<User2 size={16} /> Informações gerais
								</h4>
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Tipo</p>
										<p className="font-medium text-xs sm:text-sm">
											{type === PersonTypeEnum.Physical ? "Pessoa Física" : "Pessoa Jurídica"}
										</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Nome</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("name") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">CPF/CNPJ</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("cnpjCpf") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Email</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("email") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Telefone</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("phone") || "Não informado"}</p>
									</div>
								</div>
							</div>
							{type === PersonTypeEnum.Legal && (
								<div className="bg-gray-50/70 p-3 rounded-lg">
									<h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1.5 text-sm">
										<Building size={16} /> Dados da empresa
									</h4>
									<div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
										<div className="flex justify-between sm:block">
											<p className="text-gray-500 text-xs">Nome Empresarial</p>
											<p className="font-medium text-xs sm:text-sm">{form.getValues("tradeName") || "Não informado"}</p>
										</div>
										<div className="flex justify-between sm:block">
											<p className="text-gray-500 text-xs">Inscrição Estadual</p>
											<p className="font-medium text-xs sm:text-sm">{form.getValues("ie") || "Não informado"}</p>
										</div>
									</div>
								</div>
							)}
							<div className="bg-gray-50/70 p-3 rounded-lg">
								<h4 className="font-medium text-gray-700 mb-2 flex items-center gap-1.5 text-sm">
									<Home size={16} /> Endereço
								</h4>
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Rua</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("address.street") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Número</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("address.number") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Complemento</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("address.complement") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Bairro</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("address.neighborhood") || "Não informado"}</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">Cidade/Estado</p>
										<p className="font-medium text-xs sm:text-sm">
											{form.getValues("address.city") || "Não informado"} - {form.getValues("address.state") || "N/I"}
										</p>
									</div>
									<div className="flex justify-between sm:block">
										<p className="text-gray-500 text-xs">CEP</p>
										<p className="font-medium text-xs sm:text-sm">{form.getValues("address.postalCode") || "Não informado"}</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<OverlayContainer isVisible={isOpen} onClose={handleReset}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[25px] w-full max-w-4xl md:max-h-[90vh] overflow-hidden shadow-xl flex flex-col md:flex-row"
			>
				<div className="hidden md:flex md:w-1/4 bg-gradient-to-b from-blue-50/80 to-cyan-50/80 p-6 md:py-8 flex-col">
					<div className="flex flex-col items-start">
						<div className="flex items-center gap-3 mb-8">
							<div className="w-11 h-11 bg-cyan-100 rounded-full flex items-center justify-center">
								<UserCircle size={26} className="text-mainColor" />
							</div>
							<h2 className="text-lg font-semibold text-gray-800">Nova Pessoa</h2>
						</div>
					</div>
					<div className="space-y-1.5 mt-2">
						{steps.map((step, index) => (
							<div key={step.id} className="relative">
								{index > 0 && (
									<div className={`absolute left-7 -top-3 w-0.5 h-6 ${currentStep >= index ? "bg-mainColor" : "bg-gray-200"}`} />
								)}
								<div
									className={`flex items-center px-3 py-2.5 rounded-lg cursor-pointer transition-all ${
										currentStep === index
											? "bg-mainColor/10 text-mainColor"
											: currentStep > index
												? "text-mainColor hover:bg-cyan-50"
												: "text-gray-500 hover:bg-gray-50"
									}`}
									onClick={() => {
										if (index <= currentStep) {
											setCurrentStep(index);
										}
									}}
								>
									<div
										className={`
										flex items-center justify-center w-9 h-9 rounded-full mr-3
										${currentStep >= index ? (currentStep === index ? "bg-mainColor text-white" : "bg-cyan-100 text-mainColor") : "bg-gray-100 text-gray-500"}
										transition-colors
									`}
									>
										{currentStep > index ? <CheckCircle2 size={18} /> : step.icon}
									</div>
									<span className="text-sm font-medium">{step.title}</span>
								</div>
							</div>
						))}
					</div>
					<div className="mt-auto pt-12">
						<div className="bg-blue-50/80 p-4 rounded-xl">
							<div className="flex items-center gap-2 mb-2 text-mainColor">
								<Clipboard size={20} />
								<span className="font-medium">Dica</span>
							</div>
							<p className="text-xs text-gray-600 leading-relaxed">
								Preencha todos os campos corretamente para garantir uma melhor gestão das suas pessoas e contatos.
							</p>
						</div>
					</div>
				</div>
				<div className="w-full md:w-3/4 p-4 sm:p-8 overflow-y-auto max-h-[80vh] md:max-h-[90vh] modal-content-scroll">
					<FormProvider {...form}>
						<form onSubmit={e => e.preventDefault()} className="flex flex-col h-full">
							<div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between mb-8">
								<div>
									<h3 className="text-xl font-medium text-gray-700">{steps[currentStep].title}</h3>
									<p className="text-sm text-gray-500">
										Etapa {currentStep + 1} de {steps.length}
									</p>
								</div>
								<div className="w-full sm:w-48 bg-gray-100 rounded-full h-1.5 mt-2 sm:mt-0">
									<motion.div
										className="bg-mainColor h-1.5 rounded-full"
										initial={{ width: 0 }}
										animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
										transition={{ duration: 0.3 }}
									/>
								</div>
							</div>
							<AnimatePresence mode="wait">
								<motion.div
									key={currentStep}
									initial={{ opacity: 0, x: 20 }}
									animate={{ opacity: 1, x: 0 }}
									exit={{ opacity: 0, x: -20 }}
									transition={{ duration: 0.2 }}
									className="flex-grow"
								>
									{renderStepContent()}
								</motion.div>
							</AnimatePresence>
							<div className="flex justify-between mt-6 pt-4 border-t border-gray-100">
								<Button
									type="button"
									onClick={prevStep}
									disabled={currentStep === 0}
									className={`flex items-center h-11 ${currentStep === 0 ? "invisible" : "bg-gray-100 hover:bg-gray-200 text-gray-700"}`}
									variant="outline"
								>
									<ArrowLeft size={16} className="mr-2" /> Voltar
								</Button>
								<Button
									type="button"
									onClick={handleNext}
									disabled={isLoading || !isStepValid()}
									className={`bg-mainColor text-white hover:bg-mainColor/90 flex items-center px-6 h-11 transition-opacity ${!isStepValid() ? "opacity-50 cursor-not-allowed" : ""}`}
								>
									{currentStep === steps.length - 1 ? (
										isLoading ? (
											"Salvando..."
										) : (
											"Finalizar"
										)
									) : (
										<>
											Continuar <ArrowRight size={16} className="ml-2" />
										</>
									)}
								</Button>
							</div>
						</form>
					</FormProvider>
				</div>
			</motion.div>
		</OverlayContainer>
	);
};
