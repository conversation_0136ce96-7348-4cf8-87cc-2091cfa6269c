import { CreatePersonSchemaType } from "@/modules/person";
import { Checkbox } from "@/shared/components/ui/checkbox";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
import { CreditCard, Mail, Phone, User, UserPen } from "lucide-react";
import { Control, Controller, UseFormRegister, useFormContext } from "react-hook-form";
import { PersonClassificationEnum } from "../enums/person-classification.enum";

interface MainDataSectionProps {
	register: UseFormRegister<CreatePersonSchemaType>;
	control: Control<CreatePersonSchemaType>;
	onCnpjChange: (isCnpj: boolean) => void;
}

export const MainDataSection = ({ register, control, onCnpjChange }: MainDataSectionProps) => {
	const {
		formState: { errors },
		watch,
	} = useFormContext<CreatePersonSchemaType>();
	const type = watch("type");
	const name = watch("name");
	const cnpjCpf = watch("cnpjCpf");
	const phone = watch("phone");
	const email = watch("email");

	return (
		<section className="flex flex-col gap-2">
			<h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
				<UserPen size={20} />
				<span>Dados Principais</span>
				<div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
			</h4>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div className="w-full">
					<label htmlFor="name" className="block text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Nome <span className="text-red-500">*</span>
					</label>
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<User size={18} className="text-gray-400" />
						</div>
						<Input
							{...register("name")}
							id="name"
							placeholder="Informe o nome completo"
							className="w-full h-[45px] rounded-[10px] pl-10"
						/>
					</div>
					{(!name || !name.trim()) && <span className="text-xs text-red-500 mt-1 block">Nome é obrigatório</span>}
					{errors.name && <span className="text-xs text-red-500 mt-1 block">{errors.name.message}</span>}
				</div>

				<div className="w-full">
					<label htmlFor="cnpjCpf" className="block text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						CPF/CNPJ <span className="text-red-500">*</span>
					</label>
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<CreditCard size={18} className="text-gray-400" />
						</div>
						<Controller
							name="cnpjCpf"
							control={control}
							render={({ field }) => (
								<Input
									{...field}
									onChange={e => {
										const numericValue = e.target.value.replace(/\D/g, "");
										const maskedValue = cpfCnpjMask(numericValue);
										field.onChange(maskedValue);
										onCnpjChange(numericValue.length > 11);
									}}
									id="cnpjCpf"
									placeholder="Digite o CPF ou CNPJ"
									className="w-full h-[45px] rounded-[10px] pl-10"
								/>
							)}
						/>
					</div>
					{(!cnpjCpf || !cnpjCpf.trim()) && <span className="text-xs text-red-500 mt-1 block">CPF/CNPJ é obrigatório</span>}
					{errors.cnpjCpf && <span className="text-xs text-red-500 mt-1 block">{errors.cnpjCpf.message}</span>}
				</div>

				<div className="w-full">
					<label htmlFor="phone" className="block text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						Telefone{type === 1 && <span className="text-red-500">*</span>}
					</label>
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<Phone size={18} className="text-gray-400" />
						</div>
						<Input {...register("phone")} id="phone" placeholder="(XX) 9XXXX-XXXX" className="w-full h-[45px] rounded-[10px] pl-10" />
					</div>
					{type === 1 && (!phone || !phone.trim()) && (
						<span className="text-xs text-red-500 mt-1 block">Telefone é obrigatório para pessoa jurídica</span>
					)}
					{errors.phone && <span className="text-xs text-red-500 mt-1 block">{errors.phone.message}</span>}
				</div>

				<div className="w-full">
					<label htmlFor="email" className="block text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
						E-mail{type === 1 && <span className="text-red-500">*</span>}
					</label>
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<Mail size={18} className="text-gray-400" />
						</div>
						<Input
							{...register("email")}
							id="email"
							placeholder="<EMAIL>"
							className="w-full h-[45px] rounded-[10px] pl-10"
						/>
					</div>
					{type === 1 && (!email || !email.trim()) && (
						<span className="text-xs text-red-500 mt-1 block">E-mail é obrigatório para pessoa jurídica</span>
					)}
					{errors.email && <span className="text-xs text-red-500 mt-1 block">{errors.email.message}</span>}
				</div>
			</div>
			<div className="flex justify-center items-center gap-6 mt-4">
				<div className="flex items-center space-x-2">
					<Controller
						name="classifications"
						control={control}
						render={({ field }) => (
							<Checkbox
								id="customer"
								checked={field.value.includes(PersonClassificationEnum.CUSTOMER)}
								onCheckedChange={checked => {
									if (checked) {
										field.onChange([...field.value, PersonClassificationEnum.CUSTOMER]);
									} else {
										field.onChange(
											field.value.filter(
												(classification: PersonClassificationEnum) => classification !== PersonClassificationEnum.CUSTOMER
											)
										);
									}
								}}
							/>
						)}
					/>
					<Label htmlFor="customer" className="cursor-pointer">
						Cliente
					</Label>
				</div>
				<div className="flex cursor-pointer items-center space-x-2">
					<Controller
						name="classifications"
						control={control}
						render={({ field }) => (
							<Checkbox
								id="supplier"
								checked={field.value.includes(PersonClassificationEnum.SUPPLIER)}
								onCheckedChange={checked => {
									if (checked) {
										field.onChange([...field.value, PersonClassificationEnum.SUPPLIER]);
									} else {
										field.onChange(
											field.value.filter(
												(classification: PersonClassificationEnum) => classification !== PersonClassificationEnum.SUPPLIER
											)
										);
									}
								}}
							/>
						)}
					/>
					<Label htmlFor="supplier" className="cursor-pointer">
						Fornecedor
					</Label>
				</div>
			</div>
		</section>
	);
};
