import { CardLMPContainer } from "@/shared/components/custom/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Filter, RefreshCw, Search, Tag } from "lucide-react";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { PersonClassificationEnum } from "../enums/person-classification.enum";

interface PersonFilters {
	name: string;
	classification: string;
}

interface PersonFiltersProps {
	methods: UseFormReturn<PersonFilters>;
	onNewPerson?: () => void;
}

export const PersonFilters = ({ methods, onNewPerson }: PersonFiltersProps) => {
	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para refinar sua busca."
			actions={
				<Button
					className="hidden md:flex items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
					onClick={onNewPerson}
				>
					<IoAdd size={18} />
					Nova Pessoa
				</Button>
			}
		>
			<FormProvider {...methods}>
				<form className="flex flex-col gap-3 md:flex-row md:items-end justify-between w-full">
					<div className="flex-1 min-w-0 md:w-[60%]">
						<label htmlFor="name" className="block text-sm font-medium text-gray-600 mb-1">
							Nome
						</label>
						<div className="relative">
							<Input
								id="name"
								type="text"
								placeholder="Buscar por nome..."
								className="w-full h-[45px] rounded-[10px] pl-10"
								{...methods.register("name")}
							/>
							<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>
					<div className="flex-1 min-w-0 md:w-[30%]">
						<label htmlFor="classification" className="block text-sm font-medium text-gray-600 mb-1">
							Classificação
						</label>
						<div className="relative">
							{" "}
							<Select
								value={methods.watch("classification") || undefined}
								onValueChange={value => methods.setValue("classification", value)}
							>
								<SelectTrigger id="classification" className="w-full h-[45px] rounded-[10px] pl-10">
									<SelectValue placeholder="Todos" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">Todos</SelectItem>
									<SelectItem value={String(PersonClassificationEnum.CUSTOMER)}>Cliente</SelectItem>
									<SelectItem value={String(PersonClassificationEnum.SUPPLIER)}>Fornecedor</SelectItem>
								</SelectContent>
							</Select>
							<Tag className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>{" "}
					<div className="md:w-auto">
						<Button
							type="button"
							variant="outline"
							className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2 whitespace-nowrap"
							onClick={() => {
								methods.reset();
								methods.setValue("classification", "all");
							}}
						>
							<RefreshCw size={18} className="text-mainColor" />
							<span>Resetar Filtros</span>
						</Button>
					</div>
				</form>
			</FormProvider>
		</CardLMPContainer>
	);
};
