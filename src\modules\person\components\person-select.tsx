import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Check, ChevronsUpDown, Search } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

import { PersonClassificationEnum } from "../enums/person-classification.enum";
import { usePersonFindAll } from "../hooks/find-all.hook";

interface Customer {
	id: number;
	name: string;
}

interface PersonsSelectProps {
	value?: string;
	onChange: (value: string) => void;
	error?: string;
	required?: boolean;
	label?: string;
	icon?: React.ReactNode;
	classification?: PersonClassificationEnum;
}

export const PersonsSelect: React.FC<PersonsSelectProps> = ({ value, onChange, error, classification }) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [isSearching, setIsSearching] = useState(false);
	const { data, isLoading } = usePersonFindAll({
		page: 1,
		limit: 50,
		search: searchTerm,
		classification,
	});

	useEffect(() => {
		if (searchTerm) {
			setIsSearching(true);
		}
	}, [searchTerm]);

	useEffect(() => {
		if (data) {
			setIsSearching(false);
		}
	}, [data]);

	const selectedCustomer = useMemo(() => {
		if (!data?.success || !value) return null;
		return data.data.data.find((customer: Customer) => String(customer.id) === String(value)) || null;
	}, [value, data]);

	const getButtonText = () => {
		if (selectedCustomer) return selectedCustomer.name;
		if (!isLoading) {
			if (classification === (PersonClassificationEnum.SUPPLIER as unknown as PersonClassificationEnum)) return "Selecione um fornecedor";
			if (classification === (PersonClassificationEnum.CUSTOMER as unknown as PersonClassificationEnum)) return "Selecione um cliente";
			return "Selecione uma pessoa";
		}
		return "Carregando...";
	};

	return (
		<div className="relative">
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<motion.button
						type="button"
						whileHover={{ scale: 1.01 }}
						whileTap={{ scale: 0.99 }}
						transition={{ type: "spring", stiffness: 400, damping: 17 }}
						className={cn(
							"w-full px-4 py-2.5 text-sm transition-all duration-200",
							"border rounded-lg bg-white/50",
							"focus:outline-none focus:ring-2 focus:ring-mainColor/50",
							"flex items-center justify-between",
							error ? "border-red-300 focus:border-red-500 focus:ring-red-500/30" : "border-gray-200 hover:border-mainColor/40",
							isLoading && "animate-pulse"
						)}
						disabled={isLoading}
					>
						<span className="truncate">{getButtonText()}</span>
						<motion.div animate={{ rotate: open ? 180 : 0 }} transition={{ duration: 0.2 }}>
							<ChevronsUpDown size={16} className="text-gray-500" />
						</motion.div>
					</motion.button>
				</PopoverTrigger>
				<PopoverContent className="w-[--radix-popover-trigger-width] z-[100000] p-0" align="start">
					<div className="flex flex-col h-full">
						<div className="relative">
							<span className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								<Search size={16} />
							</span>
							<input
								placeholder="Buscar pessoa..."
								value={searchTerm}
								onChange={e => setSearchTerm(e.target.value)}
								className="h-9 pl-8 pr-2 text-sm border-b border-gray-200 focus:outline-none focus:ring-0 w-full"
							/>
						</div>
						<div className="overflow-y-auto max-h-[260px]">
							{(isSearching || isLoading) && <div className="py-2 px-2 text-sm text-gray-500">Buscando...</div>}
							{!isSearching && !isLoading && !data?.data && <div className="py-2 px-2 text-sm text-gray-500">Carregando...</div>}
							{!isSearching && !isLoading && data?.data && data?.success && data.data.data.length === 0 && (
								<div className="py-2 px-2 text-sm text-gray-500">Nenhuma pessoa encontrada.</div>
							)}
							<AnimatePresence>
								{data?.data &&
									data.success &&
									data.data.data.map((customer: Customer) => (
										<motion.li
											key={customer.id + customer.name}
											initial={{ opacity: 0, y: 10 }}
											animate={{ opacity: 1, y: 0 }}
											exit={{ opacity: 0, y: -10 }}
											transition={{ duration: 0.2 }}
											className="flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-mainColor/5"
											onClick={() => {
												onChange(String(customer.id));
												setOpen(false);
												setSearchTerm("");
											}}
										>
											<span>{customer.name}</span>
											{String(customer.id) === String(value) && <Check size={16} className="text-mainColor" />}
										</motion.li>
									))}
							</AnimatePresence>
						</div>
					</div>
				</PopoverContent>
			</Popover>
			<AnimatePresence>
				{error && (
					<motion.div
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -10 }}
						className="absolute mt-1 text-xs text-red-500 flex items-center gap-1"
					>
						<AlertCircle size={12} />
						<span className="font-medium">{error}</span>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};
