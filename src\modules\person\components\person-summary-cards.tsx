import { ShoppingCart, UserCircle, Users } from "lucide-react";

export const PersonSummaryCards = () => (
	<section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Total de Pessoas</div>
				<div className="text-2xl text-gray-800 font-bold">124</div>
				<div className="text-sm text-gray-500">Cadastradas</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-blue-100 rounded-lg items-center justify-center">
					<Users size={28} className="text-blue-600" />
				</span>
			</div>
		</div>
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Clientes</div>
				<div className="text-2xl text-gray-800 font-bold">98</div>
				<div className="text-sm text-gray-500">Ativos</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-green-100 rounded-lg items-center justify-center">
					<UserCircle size={28} className="text-green-600" />
				</span>
			</div>
		</div>
		<div className="bg-white p-4 md:p-6 justify-between shadow-sm rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">Fornecedores</div>
				<div className="text-2xl text-gray-800 font-bold">26</div>
				<div className="text-sm text-gray-500">Cadastrados</div>
			</div>
			<div>
				<span className="flex h-14 w-14 bg-purple-100 rounded-lg items-center justify-center">
					<ShoppingCart size={28} className="text-purple-600" />
				</span>
			</div>
		</div>
	</section>
);
