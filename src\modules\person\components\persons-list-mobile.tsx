import { motion } from "framer-motion";
import { Tag, User } from "lucide-react";
import { IPerson } from "../dtos/find-all.dto";

interface PersonsListMobileProps {
	persons: IPerson[];
}

export const PersonsListMobile = ({ persons }: PersonsListMobileProps) => {
	const getClassificationBadge = (classification: string) => {
		switch (classification.toLowerCase()) {
			case "cliente":
				return (
					<span
						key={classification}
						className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-1"
					>
						<User size={12} className="mr-1" />
						{classification}
					</span>
				);
			case "fornecedor":
				return (
					<span
						key={classification}
						className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-1"
					>
						<Tag size={12} className="mr-1" />
						{classification}
					</span>
				);
			default:
				return (
					<span
						key={classification}
						className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-1"
					>
						{classification}
					</span>
				);
		}
	};

	return (
		<div className="md:hidden space-y-4">
			{persons.map((person, index) => (
				<motion.div
					key={person.id}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.2, delay: index * 0.05 }}
					className="w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm"
				>
					<div className="flex items-center justify-between mb-2">
						<span className="font-medium text-gray-900">{person.name}</span>
					</div>
					<div className="flex flex-wrap mt-2">
						{person.classifications?.length ? (
							person.classifications.map(classification => getClassificationBadge(classification))
						) : (
							<span className="text-gray-500 text-sm">Sem classificação</span>
						)}
					</div>
				</motion.div>
			))}
		</div>
	);
};
