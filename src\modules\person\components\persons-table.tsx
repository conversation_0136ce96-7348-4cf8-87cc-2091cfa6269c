import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ColumnDef, flexRender, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from "@tanstack/react-table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUpDown, Tag, User } from "lucide-react";
import { useState } from "react";
import { IPerson } from "../dtos/find-all.dto";

interface PersonsTableProps {
	persons: IPerson[];
}

export const PersonsTable = ({ persons }: PersonsTableProps) => {
	const [sorting, setSorting] = useState<SortingState>([]);
	const getClassificationBadge = (classification: string) => {
		switch (classification.toLowerCase()) {
			case "cliente":
				return (
					<span
						key={classification}
						className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2 mb-1"
					>
						<User size={14} className="mr-1.5" />
						{classification}
					</span>
				);
			case "fornecedor":
				return (
					<span
						key={classification}
						className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-2 mb-1"
					>
						<Tag size={14} className="mr-1.5" />
						{classification}
					</span>
				);
			default:
				return (
					<span
						key={classification}
						className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2 mb-1"
					>
						{classification}
					</span>
				);
		}
	};
	const getClassification = (classifications?: string[]) => {
		if (!classifications || classifications.length === 0) {
			return <div className="text-gray-500 px-1">Não definido</div>;
		}

		return <div className="flex flex-wrap gap-2">{classifications.map(classification => getClassificationBadge(classification))}</div>;
	};

	const columns: ColumnDef<IPerson>[] = [
		{
			accessorKey: "id",
			header: () => <div className="text-left font-semibold pl-4">ID</div>,
			size: 90,
			cell: ({ row }) => <div className="text-gray-700 text-left pl-4">{row.original.id}</div>,
		},
		{
			accessorKey: "name",
			header: ({ column }) => {
				const isSorted = column.getIsSorted() === "asc";
				return (
					<div
						className="flex items-center gap-1 cursor-pointer group text-left"
						role="button"
						tabIndex={0}
						aria-pressed={isSorted}
						onClick={() => column.toggleSorting(isSorted)}
						onKeyDown={e => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								column.toggleSorting(isSorted);
							}
						}}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Nome</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => <div className="font-medium text-gray-900 text-left">{row.original.name}</div>,
		},
		{
			accessorKey: "classifications",
			header: () => <div className="text-left">Classificações</div>,
			cell: ({ row }) => <div className="text-left">{getClassification(row.original.classifications)}</div>,
		},
	];

	const table = useReactTable({
		data: persons,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		state: {
			sorting,
		},
	});

	return (
		<div className="hidden md:block">
			<div className="rounded-lg border overflow-hidden">
				<Table>
					<TableHeader>
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => (
									<TableHead key={header.id} className="align-middle text-left py-3 px-2 bg-gray-50">
										{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{table.getRowModel().rows?.length ? (
							<AnimatePresence>
								{table.getRowModel().rows.map((row, index) => (
									<motion.tr
										key={row.id}
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.2, delay: index * 0.05 }}
										className="border-b bg-white hover:bg-gray-50 transition-colors"
									>
										{row.getVisibleCells().map(cell => (
											<TableCell key={cell.id} className="align-middle py-3 px-2">
												{flexRender(cell.column.columnDef.cell, cell.getContext())}
											</TableCell>
										))}
									</motion.tr>
								))}
							</AnimatePresence>
						) : (
							<TableRow>
								<TableCell colSpan={columns.length} className="h-24 text-center">
									Nenhum registro encontrado.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
		</div>
	);
};
