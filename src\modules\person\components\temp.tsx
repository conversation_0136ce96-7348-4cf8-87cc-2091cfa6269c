// import { CreatePersonSchemaType, PersonClassificationEnum } from "@/modules/person";
// import { Checkbox } from "@/shared/components/ui/checkbox";
// import { Input } from "@/shared/components/ui/input";
// import { Label } from "@/shared/components/ui/label";
// import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
// import { UserPen } from "lucide-react";
// import { Control, Controller, UseFormRegister } from "react-hook-form";

// interface MainDataSectionProps {
//     register: UseFormRegister<CreatePersonSchemaType>;
//     control: Control<CreatePersonSchemaType>;
//     onCnpjChange: (isCnpj: boolean) => void;
// }

// export const MainDataSection = ({ register, control, onCnpjChange }: MainDataSectionProps) => {
//     return (
//         <section className="flex flex-col gap-2">
//             <h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
//                 <UserPen size={20} />
//                 <span>Dados Principais</span>
//                 <div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
//             </h4>
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="name" className="absolute top-[-5px] bg-white px-3 left-3">
//                         Nome
//                     </Label>
//                     <Input
//                         {...register("name")}
//                         id="name"
//                         placeholder="Informe o nome completo"
//                         className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                     />
//                 </div>

//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="cnpjCpf" className="absolute top-[-5px] bg-white px-3 left-3">
//                         CPF/CNPJ
//                     </Label>
//                     <Controller
//                         name="cnpjCpf"
//                         control={control}
//                         render={({ field }) => (
//                             <Input
//                                 {...field}
//                                 onChange={e => {
//                                     const numericValue = e.target.value.replace(/\D/g, "");
//                                     const maskedValue = cpfCnpjMask(numericValue);
//                                     field.onChange(maskedValue);
//                                     onCnpjChange(numericValue.length > 11);
//                                 }}
//                                 id="cnpjCpf"
//                                 placeholder="Digite o CPF ou CNPJ"
//                                 className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                             />
//                         )}
//                     />
//                 </div>

//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="phone" className="absolute top-[-5px] bg-white px-3 left-3">
//                         Telefone
//                     </Label>
//                     <Input
//                         {...register("phone")}
//                         id="phone"
//                         placeholder="(XX) 9XXXX-XXXX"
//                         className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                     />
//                 </div>

//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="email" className="absolute top-[-5px] bg-white px-3 left-3">
//                         E-mail
//                     </Label>
//                     <Input
//                         {...register("email")}
//                         id="email"
//                         placeholder="<EMAIL>"
//                         className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                     />
//                 </div>
//             </div>
//             <div className="flex justify-center items-center gap-6 mt-4">
//                 <div className="flex items-center space-x-2">
//                     <Controller
//                         name="classifications"
//                         control={control}
//                         render={({ field }) => (
//                             <Checkbox
//                                 id="customer"
//                                 checked={field.value.includes(PersonClassificationEnum.CUSTOMER)}
//                                 onCheckedChange={checked => {
//                                     if (checked) {
//                                         field.onChange([...field.value, PersonClassificationEnum.CUSTOMER]);
//                                     } else {
//                                         field.onChange(
//                                             field.value.filter(
//                                                 (classification: PersonClassificationEnum) => classification !== PersonClassificationEnum.CUSTOMER
//                                             )
//                                         );
//                                     }
//                                 }}
//                             />
//                         )}
//                     />
//                     <Label htmlFor="customer" className="cursor-pointer">
//                         Cliente
//                     </Label>
//                 </div>
//                 <div className="flex cursor-pointer items-center space-x-2">
//                     <Controller
//                         name="classifications"
//                         control={control}
//                         render={({ field }) => (
//                             <Checkbox
//                                 id="supplier"
//                                 checked={field.value.includes(PersonClassificationEnum.SUPPLIER)}
//                                 onCheckedChange={checked => {
//                                     if (checked) {
//                                         field.onChange([...field.value, PersonClassificationEnum.SUPPLIER]);
//                                     } else {
//                                         field.onChange(
//                                             field.value.filter(
//                                                 (classification: PersonClassificationEnum) => classification !== PersonClassificationEnum.SUPPLIER
//                                             )
//                                         );
//                                     }
//                                 }}
//                             />
//                         )}
//                     />
//                     <Label htmlFor="supplier" className="cursor-pointer">
//                         Fornecedor
//                     </Label>
//                 </div>
//             </div>
//         </section>
//     );
// };

// import { CreatePersonSchemaType, PersonClassificationEnum } from "@/modules/person";
// import { Checkbox } from "@/shared/components/ui/checkbox";
// import { Input } from "@/shared/components/ui/input";
// import { Label } from "@/shared/components/ui/label";
// import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
// import { UserPen } from "lucide-react";
// import { Control, Controller, UseFormRegister } from "react-hook-form";

// interface MainDataSectionProps {
//     register: UseFormRegister<CreatePersonSchemaType>;
//     control: Control<CreatePersonSchemaType>;
//     onCnpjChange: (isCnpj: boolean) => void;
// }

// export const MainDataSection = ({ register, control, onCnpjChange }: MainDataSectionProps) => {
//     return (
//         <section className="flex flex-col gap-2">
//             <h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
//                 <UserPen size={20} />
//                 <span>Dados Principais</span>
//                 <div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
//             </h4>
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="name" className="absolute top-[-5px] bg-white px-3 left-3">
//                         Nome
//                     </Label>
//                     <Input
//                         {...register("name")}
//                         id="name"
//                         placeholder="Informe o nome completo"
//                         className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                     />
//                 </div>

//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="cnpjCpf" className="absolute top-[-5px] bg-white px-3 left-3">
//                         CPF/CNPJ
//                     </Label>
//                     <Controller
//                         name="cnpjCpf"
//                         control={control}
//                         render={({ field }) => (
//                             <Input
//                                 {...field}
//                                 onChange={e => {
//                                     const numericValue = e.target.value.replace(/\D/g, "");
//                                     const maskedValue = cpfCnpjMask(numericValue);
//                                     field.onChange(maskedValue);
//                                     onCnpjChange(numericValue.length > 11);
//                                 }}
//                                 id="cnpjCpf"
//                                 placeholder="Digite o CPF ou CNPJ"
//                                 className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                             />
//                         )}
//                     />
//                 </div>

//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="phone" className="absolute top-[-5px] bg-white px-3 left-3">
//                         Telefone
//                     </Label>
//                     <Input
//                         {...register("phone")}
//                         id="phone"
//                         placeholder="(XX) 9XXXX-XXXX"
//                         className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                     />
//                 </div>

//                 <div className="lg:min-w-[150px] lg:w-auto w-full relative">
//                     <Label htmlFor="email" className="absolute top-[-5px] bg-white px-3 left-3">
//                         E-mail
//                     </Label>
//                     <Input
//                         {...register("email")}
//                         id="email"
//                         placeholder="<EMAIL>"
//                         className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] w-full px-3"
//                     />
//                 </div>
//             </div>
//             <div className="flex justify-center items-center gap-6 mt-4">
//                 <div className="flex items-center space-x-2">
//                     <Controller
//                         name="classifications"
//                         control={control}
//                         render={({ field }) => (
//                             <Checkbox
//                                 id="customer"
//                                 checked={field.value.includes(PersonClassificationEnum.CUSTOMER)}
//                                 onCheckedChange={checked => {
//                                     if (checked) {
//                                         field.onChange([...field.value, PersonClassificationEnum.CUSTOMER]);
//                                     } else {
//                                         field.onChange(
//                                             field.value.filter(
//                                                 (classification: PersonClassificationEnum) => classification !== PersonClassificationEnum.CUSTOMER
//                                             )
//                                         );
//                                     }
//                                 }}
//                             />
//                         )}
//                     />
//                     <Label htmlFor="customer" className="cursor-pointer">
//                         Cliente
//                     </Label>
//                 </div>
//                 <div className="flex cursor-pointer items-center space-x-2">
//                     <Controller
//                         name="classifications"
//                         control={control}
//                         render={({ field }) => (
//                             <Checkbox
//                                 id="supplier"
//                                 checked={field.value.includes(PersonClassificationEnum.SUPPLIER)}
//                                 onCheckedChange={checked => {
//                                     if (checked) {
//                                         field.onChange([...field.value, PersonClassificationEnum.SUPPLIER]);
//                                     } else {
//                                         field.onChange(
//                                             field.value.filter(
//                                                 (classification: PersonClassificationEnum) => classification !== PersonClassificationEnum.SUPPLIER
//                                             )
//                                         );
//                                     }
//                                 }}
//                             />
//                         )}
//                     />
//                     <Label htmlFor="supplier" className="cursor-pointer">
//                         Fornecedor
//                     </Label>
//                 </div>
//             </div>
//         </section>
//     );
// };
