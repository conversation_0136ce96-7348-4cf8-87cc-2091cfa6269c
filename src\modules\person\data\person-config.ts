import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { UsersIcon } from "lucide-react";
import { PERSON_PATHS } from "./routes-paths.data";

export const PERSON_SUBITEM_IDS = {
	ALL_PERSONS: "all-persons",
} as const;

export const PERSON_CONFIG: IItemSidebar = {
	id: "person-group",
	moduleActivated: true,
	label: "Pessoas",
	accessibleDescription: "ícone seção de pessoas",
	Icon: UsersIcon,
	type: "menu",
	path: PERSON_PATHS.ALL_PERSONS,
	subItems: [
		{
			id: PERSON_SUBITEM_IDS.ALL_PERSONS,
			label: "Pessoa<PERSON>",
			Icon: UsersIcon,
			accessibleDescription: "ícone de listagem de pessoas",
			path: PERSON_PATHS.ALL_PERSONS,
		},
	],
};
