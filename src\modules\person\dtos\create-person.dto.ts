import { PersonClassificationEnum, PersonTypeEnum } from "../enums/person-classification.enum";

export interface ICreatePersonDTO {
	type: PersonTypeEnum;
	name: string;
	cnpjCpf: string;
	email?: string;
	phone?: string;
	classifications: PersonClassificationEnum[];
	address?: {
		street?: string;
		number?: string;
		complement?: string;
		neighborhood?: string;
		city?: string;
		state?: string;
		postalCode?: string;
	};
	tradeName?: string;
	ie?: string;
}
