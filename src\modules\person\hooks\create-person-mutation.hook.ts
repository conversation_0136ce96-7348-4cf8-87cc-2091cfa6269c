import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { createPersonRequest } from "../api/requests/create";
import { ICreatePersonDTO } from "../dtos/create-person.dto";

export const useCreatePersonMutation = ({ closeModal }: { closeModal: () => void }) => {
	const createPersonMutation = useMutation({
		mutationKey: ["createPerson"],
		mutationFn: async (data: ICreatePersonDTO) => {
			const response = await createPersonRequest(data);
			if (!response.success) {
				throw new Error(response.data.message || "Erro ao criar pessoa");
			}
			return response.data;
		},
		onSuccess: data => {
			toast.dismiss();
			toast.success(data.message);
			closeModal();
		},
		onError: error => {
			toast.dismiss();
			toast.error(error.message || "Erro ao criar pessoa");
		},
		onMutate: () => {
			toast.loading("Criando pessoa...");
		},
	});

	return { createPerson: createPersonMutation.mutateAsync, isLoading: createPersonMutation.isPending };
};
