import { useQuery } from "@tanstack/react-query";
import { findAllCustomers } from "../api/requests/find-all-customers";
import { PERSON_QUERY_KEYS } from "../data/query-keys";

export const useFindAllCustomers = ({
	page = 1,
	limit = 10,
	name = "",
	document = "",
}: {
	page?: number;
	limit?: number;
	name?: string;
	document?: string;
}) => {
	const { data, error, isLoading } = useQuery({
		queryKey: PERSON_QUERY_KEYS.FIND_ALL_CUSTOMERS(page, limit, name, document),
		queryFn: () => findAllCustomers({ page, limit, name, document }),
	});

	return {
		data,
		error,
		isLoading,
	};
};
