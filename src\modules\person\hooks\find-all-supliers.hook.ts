import { useQuery } from "@tanstack/react-query";
import { findAllSuppliers } from "../api/requests/find-all-suppliers";
import { PERSON_QUERY_KEYS } from "../data/query-keys";

export const useSuppliersFindAll = ({ page = 1, limit = 50, filter = "" }: { page?: number; limit?: number; filter?: string }) => {
	const { data, error, isLoading } = useQuery({
		queryKey: PERSON_QUERY_KEYS.FIND_ALL_SUPPLIERS(page, limit, filter),
		queryFn: () => findAllSuppliers({ page, limit, filter }),
	});

	return {
		data,
		error,
		isLoading,
	};
};
