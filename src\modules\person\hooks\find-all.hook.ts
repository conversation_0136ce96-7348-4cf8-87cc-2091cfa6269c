import { useQuery } from "@tanstack/react-query";
import { findAllRequest, IFindAllParams } from "../api/requests/find-all";
import { PERSON_QUERY_KEYS } from "../data/query-keys";

export const usePersonFindAll = (params: IFindAllParams) => {
	const { page = 1, limit = 50, search = "", classification } = params;

	const { data, error, isLoading } = useQuery({
		queryKey: PERSON_QUERY_KEYS.FIND_ALL_PERSONS(page, limit, search, classification),
		queryFn: () => findAllRequest({ page, limit, search, classification }),
	});

	return {
		data,
		error,
		isLoading,
	};
};
