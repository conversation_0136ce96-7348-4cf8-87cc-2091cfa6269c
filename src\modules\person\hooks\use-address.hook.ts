import { useState } from "react";
import { UseFormSetValue } from "react-hook-form";
import { CreatePersonSchemaType } from "../validators/create-person.validator";

interface AddressData {
	logradouro: string;
	bairro: string;
	localidade: string;
	uf: string;
	erro?: boolean;
}

export const useAddress = (setValue: UseFormSetValue<CreatePersonSchemaType>) => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchAddressByCep = async (cep: string) => {
		if (!cep || cep.length !== 8) return;

		setIsLoading(true);
		setError(null);

		try {
			const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
			const data: AddressData = await response.json();

			if (data.erro) {
				setError("CEP não encontrado");
				return;
			}

			await Promise.all([
				setValue("address.street", data.logradouro || "", { shouldValidate: true }),
				setValue("address.neighborhood", data.bairro || "", { shouldValidate: true }),
				setValue("address.city", data.localidade || "", { shouldValidate: true }),
				setValue("address.state", data.uf || "", { shouldValidate: true }),
			]);
		} catch (err) {
			setError("Erro ao buscar CEP");
		} finally {
			setIsLoading(false);
		}
	};

	return { fetchAddressByCep, isLoading, error };
};
