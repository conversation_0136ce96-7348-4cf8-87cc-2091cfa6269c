import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { zodResolver } from "@hookform/resolvers/zod";
import { UseMutateAsyncFunction } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { ICreatePersonDTO } from "../dtos/create-person.dto";

import { PersonTypeEnum } from "../enums/person-classification.enum";
import { cleanPersonPayload } from "../utils/clean-person-payload.util";
import { cleanPhone } from "../utils/clean-phone.util";
import { CreatePersonSchemaType, createPersonSchema } from "../validators/create-person.validator";

interface IUseCreatePersonProps {
	createPerson: UseMutateAsyncFunction<IGlobalMessageReturn, Error, ICreatePersonDTO, void>;
}

export const useCreatePerson = ({ createPerson }: IUseCreatePersonProps) => {
	const form = useForm<CreatePersonSchemaType>({
		resolver: zodResolver(createPersonSchema),
		defaultValues: {
			type: PersonTypeEnum.Physical,
			name: "",
			cnpjCpf: "",
			email: "",
			phone: "",
			classifications: [1],
			address: undefined,
			tradeName: "",
			ie: "",
		},
	});

	const onSubmit = async (data: CreatePersonSchemaType) => {
		const cleanedData = {
			...data,
			phone: data.phone ? cleanPhone(data.phone) : undefined,
		};
		await createPerson(cleanPersonPayload(cleanedData) as ICreatePersonDTO);
	};

	return {
		form,
		onSubmit,
	};
};
