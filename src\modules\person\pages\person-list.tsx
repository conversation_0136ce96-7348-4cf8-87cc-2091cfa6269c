import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { AnimatePresence, motion } from "framer-motion";
import { Home, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { IoAdd } from "react-icons/io5";

import { CreatePersonModal } from "../components/create-person-modal";
import { PersonFilters } from "../components/person-filters";
import { PersonSummaryCards } from "../components/person-summary-cards";
import { PersonsListMobile } from "../components/persons-list-mobile";
import { PersonsTable } from "../components/persons-table";
import { PERSON_CONFIG, PERSON_SUBITEM_IDS } from "../data/person-config";

import { PersonClassificationEnum } from "../enums/person-classification.enum";
import { usePersonFindAll } from "../hooks/find-all.hook";

interface PersonFiltersForm {
	name: string;
	classification: string;
}

export function PersonListPage() {
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const methods = useForm<PersonFiltersForm>({
		defaultValues: {
			name: "",
			classification: "all",
		},
	});

	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = usePagination(50);

	const filters = methods.watch();
	const {
		data: persons,
		isLoading,
		error: queryError,
	} = usePersonFindAll({
		page,
		limit: itemsPerPage,
		search: filters.name || "",
		classification: filters.classification !== "all" ? (Number(filters.classification) as PersonClassificationEnum) : undefined,
	});

	const totalPages = persons?.success && persons?.data?.total ? Math.ceil(persons.data.total / itemsPerPage) : 1;

	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		setError(null);
	}, [page, itemsPerPage, filters]);

	useEffect(() => {
		if (!isLoading && !persons && !queryError) {
			setError("Erro ao carregar pessoas");
		}
	}, [isLoading, persons, queryError]);

	let tableKey = "table";
	if (isLoading) {
		tableKey = "loading";
	} else if (error) {
		tableKey = "error";
	} else if (persons?.success === true && persons?.data && persons.data.data?.length === 0) {
		tableKey = "notfound";
	}

	const renderTableContent = () => {
		if (isLoading) {
			return (
				<div className="flex justify-center items-center py-10 animate-pulse">
					<span className="text-gray-400 text-lg">Carregando pessoas...</span>
				</div>
			);
		}
		if (error) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-red-500 text-lg font-semibold mb-2">Erro ao carregar pessoas</span>
					<span className="text-gray-500 text-sm">Tente novamente mais tarde.</span>
				</div>
			);
		}
		if (!(persons?.success === true && persons.data?.data?.length)) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-gray-500 text-lg font-semibold mb-2">Nenhuma pessoa encontrada</span>
					<span className="text-gray-400 text-sm">Ajuste os filtros ou tente novamente.</span>
				</div>
			);
		}
		return (
			<>
				<PersonsTable persons={persons.data.data} />
				<PersonsListMobile persons={persons.data.data} />
			</>
		);
	};

	return (
		<MainSkeletonContainer
			pageTitle="Lista de Pessoas"
			iconTitle={PERSON_CONFIG.subItems?.find(item => item.id === PERSON_SUBITEM_IDS.ALL_PERSONS)?.Icon}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{
					href: PERSON_CONFIG.path,
					label: "Pessoas",
					icon: PERSON_CONFIG.Icon,
				},
			]}
			currentBreadcrumb={{
				href: PERSON_CONFIG.subItems?.find(item => item.id === PERSON_SUBITEM_IDS.ALL_PERSONS)?.path ?? "",
				label: "Lista de Pessoas",
				icon: PERSON_CONFIG.subItems?.find(item => item.id === PERSON_SUBITEM_IDS.ALL_PERSONS)?.Icon,
			}}
		>
			<PersonSummaryCards />
			<PersonFilters methods={methods} onNewPerson={() => setIsCreateModalOpen(true)} />
			<CardLMPContainer
				title="Lista de Pessoas"
				description="Gerencie todas as pessoas cadastradas no sistema"
				icon={<Users size={22} className="text-gray-500" />}
			>
				<AnimatePresence mode="wait">
					<motion.div
						key={tableKey}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2 }}
					>
						{renderTableContent()}
					</motion.div>
				</AnimatePresence>
				<Pagination
					totalPages={totalPages}
					itemsPerPage={itemsPerPage}
					page={page}
					onPageChange={handlePageChange}
					onItemsPerPageChange={handleItemsPerPageChange}
				/>
				{/* Botão flutuante para criação de pessoa em dispositivos móveis */}
				<button
					onClick={() => setIsCreateModalOpen(true)}
					className="md:hidden fixed bottom-6 right-6 z-50 bg-mainColor text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg"
				>
					<IoAdd size={24} />
				</button>
				<CreatePersonModal isOpen={isCreateModalOpen} onClose={() => setIsCreateModalOpen(false)} />
			</CardLMPContainer>
		</MainSkeletonContainer>
	);
}
