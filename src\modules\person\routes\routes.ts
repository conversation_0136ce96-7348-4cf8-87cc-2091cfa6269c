import { home } from "@/shared/routes/dashboard.route";
import { ModuleSubRoute, createModuleRoutes } from "@/shared/routes/module-routes.factory";
import { PERSON_CONFIG, PERSON_SUBITEM_IDS } from "../data/person-config";
import { PersonListPage } from "../pages/person-list";

const personSubRoutes: ModuleSubRoute<NonNullable<typeof PERSON_CONFIG.subItems>[number]>[] = [
	{ id: PERSON_SUBITEM_IDS.ALL_PERSONS, component: PersonListPage },
];

export const personRoutes = createModuleRoutes(
	{
		...PERSON_CONFIG,
		subItems: PERSON_CONFIG.subItems ?? [],
	},
	personSubRoutes,
	() => home
);
