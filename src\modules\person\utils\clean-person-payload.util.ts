// Remove campos vazios do payload antes de enviar para o backend
import { ICreatePersonDTO } from "@/modules/person/dtos/create-person.dto";
import { PersonTypeEnum } from "../enums/person-classification.enum";

export function cleanPersonPayload(data: ICreatePersonDTO): Partial<ICreatePersonDTO> {
	const cleaned: Partial<ICreatePersonDTO> = { ...data };

	if (cleaned.address) {
		const addressFields: (keyof NonNullable<ICreatePersonDTO["address"]>)[] = [
			"street",
			"number",
			"complement",
			"neighborhood",
			"city",
			"state",
			"postalCode",
		];
		let hasAny = false;
		for (const field of addressFields) {
			if (cleaned.address && cleaned.address[field]) {
				hasAny = true;
				break;
			}
		}
		if (!hasAny) {
			delete cleaned.address;
		}
	}

	(["email", "phone", "tradeName", "ie"] as (keyof ICreatePersonDTO)[]).forEach(field => {
		if (cleaned[field] === "" || cleaned[field] === undefined) {
			delete cleaned[field];
		}
	});

	if (cleaned.type === PersonTypeEnum.Physical) {
		delete cleaned.tradeName;
		delete cleaned.ie;
	}

	return cleaned;
}
