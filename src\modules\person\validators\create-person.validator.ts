import { PersonClassificationEnum } from "@/modules/sales-panel/dto/create-person.dto";
import { z } from "zod";
import { PersonTypeEnum } from "../enums/person-classification.enum";

const addressSchema = z
	.object({
		street: z.string().optional(),
		number: z.string().optional(),
		complement: z.string().optional(),
		neighborhood: z.string().optional(),
		city: z.string().optional(),
		state: z.string().optional(),
		postalCode: z.string().optional(),
	})
	.refine(
		data => {
			const requiredFields = ["street", "number", "neighborhood", "city", "state", "postalCode"];
			const hasAnyField = requiredFields.some(field => !!data[field as keyof typeof data]);
			if (!hasAnyField) return true;
			return requiredFields.every(field => !!data[field as keyof typeof data]);
		},
		{
			message: "Se qualquer informação de endereço for fornecida, todas as outras são obrigatórias (exceto complemento)",
		}
	)
	.optional();

export const createPersonSchema = z
	.object({
		type: z.nativeEnum(PersonTypeEnum),
		name: z.string().min(1, "Nome é obrigatório"),
		cnpjCpf: z.string().min(1, "CPF/CNPJ é obrigatório"),
		classifications: z.array(z.nativeEnum(PersonClassificationEnum)).optional().default([]),
		address: addressSchema,
	})
	.and(
		z.discriminatedUnion("type", [
			z.object({
				type: z.literal(PersonTypeEnum.Physical),
				email: z.string().optional(),
				phone: z.string().optional(),
				tradeName: z.string().optional(),
				ie: z.string().optional(),
			}),
			z.object({
				type: z.literal(PersonTypeEnum.Legal),
				email: z.string().min(1, "Email é obrigatório"),
				phone: z.string().min(1, "Telefone é obrigatório"),
				tradeName: z.string().min(1, "Nome empresarial é obrigatório"),
				ie: z.string().min(1, "Inscrição estadual é obrigatória"),
			}),
		])
	);

export type CreatePersonSchemaType = z.infer<typeof createPersonSchema>;
