import { buildQueryParams } from "@/shared/lib/build-query-params";

export const PRODUCT_ROUTES = {
	FIND_ALL: ({ page, limit, filter }: { page: number; limit: number; filter?: string }) =>
		`/product/find-all/${page}/${limit}?filter=${filter ?? ""}`,
	CREATE: "/product/create",
	UPDATE: ({ id }: { id: number }) => `/product/update/${id}`,
	DELETE: ({ id }: { id: number }) => `/product/delete/${id}`,
	FIND_BY_ID: ({ id }: { id: number }) => `/product/find-by-id/${id}`,
	REMOVE_CATEGORY_WITH_PRODUCT: ({ productId, categoryId }: { productId: number; categoryId: number }) =>
		`/product/${productId}/remove-category/${categoryId}`,
	LINK_CATEGORY: ({ id, categoryId }: { id: number; categoryId: number }) => `/product/${id}/link-category/${categoryId}`,
	CREATE_IMAGE: ({ id }: { id: number }) => `/product/${id}/create-image`,
	DELETE_IMAGE: ({ id }: { id: number }) => `/product/${id}/delete-image`,
} as const;

export const PRODUCT_CATEGORY_ROUTES = {
	FIND_ALL: ({ page, limit, search, groupId }: { page: number; limit: number; search?: string; groupId?: string }) => {
		const path = `/product/category/find-all/${page}/${limit}`;
		return buildQueryParams(path, {
			search,
			groupId,
		});
	},
	CREATE: "/product/category/create",
	DELETE: ({ id }: { id: number }) => `/product/category/delete/${id}`,
} as const;

export const PRODUCT_GROUP_ROUTES = {
	FIND_ALL: ({ page, limit, search }: { page: number; limit: number; search?: string }) =>
		`/product/group/find-all/${page}/${limit}?search=${search ?? ""}`,
	CREATE: "/product/group/create",
	DELETE: ({ id }: { id: number }) => `/product/group/delete/${id}`,
} as const;
