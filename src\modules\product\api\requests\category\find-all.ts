import { ICategoryFindAllDto } from "@/modules/product/dtos/category/find-all.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_CATEGORY_ROUTES } from "../../endpoints";

export const productCategoryFindAllRequest = async ({
	page,
	limit,
	filter,
	groupId,
}: {
	page: number;
	limit: number;
	filter?: string;
	groupId?: string;
}): Promise<ApiResponse<ICategoryFindAllDto>> => {
	return await createRequest<ICategoryFindAllDto>({
		path: `${PRODUCT_CATEGORY_ROUTES.FIND_ALL({ page, limit, search: filter, groupId })}`,
		method: "GET",
	});
};
