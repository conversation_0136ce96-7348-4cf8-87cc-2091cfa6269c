import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ICreateGroupDto } from "../../../dtos/group/create-group.dto";
import { PRODUCT_GROUP_ROUTES } from "../../endpoints";

export const productGroupCreateRequest = async (data: ICreateGroupDto): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest({
		path: PRODUCT_GROUP_ROUTES.CREATE,
		method: "POST",
		body: data,
	});
};
