import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_GROUP_ROUTES } from "../../endpoints";

export const productGroupDeleteRequest = async (id: number): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest({
		path: PRODUCT_GROUP_ROUTES.DELETE({ id }),
		method: "DELETE",
	});
};
