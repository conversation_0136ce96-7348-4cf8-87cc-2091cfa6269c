// filepath: c:\Users\<USER>\Documents\projects\lmp-frontend\src\modules\product\api\requests\product\create-image.ts
import { resolveGlobalErrors } from "@/shared/lib/erros/handle-globals-errors.lib";
import apiInstance from "@/shared/services/config/api-intance.service";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productCreateImageRequest = async ({
	productId,
	image,
}: {
	productId: number;
	image: File;
}): Promise<ApiResponse<IGlobalMessageReturn>> => {
	try {
		const formData = new FormData();
		formData.append("image", image);

		const response = await apiInstance.postForm(PRODUCT_ROUTES.CREATE_IMAGE({ id: productId }), formData);

		return {
			success: true,
			data: response.data,
			status: response.status,
		};
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
