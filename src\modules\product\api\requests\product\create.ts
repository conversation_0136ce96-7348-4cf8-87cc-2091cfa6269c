import { type ICreateProductDto } from "@/modules/product/dtos/product/create-product.dto";
import { resolveGlobalErrors } from "@/shared/lib/erros/handle-globals-errors.lib";
import apiInstance from "@/shared/services/config/api-intance.service";
import { type IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { type ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productCreateRequest = async (items: Readonly<ICreateProductDto>): Promise<ApiResponse<IGlobalMessageReturn>> => {
	try {
		const { data, status } = await apiInstance.postForm(PRODUCT_ROUTES.CREATE, items);
		return { success: true, data, status };
	} catch (error) {
		return resolveGlobalErrors(error);
	}
};
