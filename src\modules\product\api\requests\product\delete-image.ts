// filepath: c:\Users\<USER>\Documents\projects\lmp-frontend\src\modules\product\api\requests\product\delete-image.ts
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productDeleteImageRequest = async ({ productId }: { productId: number }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const response = await createRequest<IGlobalMessageReturn>({
		path: PRODUCT_ROUTES.DELETE_IMAGE({ id: productId }),
		method: "DELETE",
	});
	return response;
};
