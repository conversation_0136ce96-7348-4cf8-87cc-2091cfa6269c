import { IProductFindByIdDto } from "@/modules/product/dtos/product/find-by-id.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productFindByIdRequest = async ({ id }: { id: number }): Promise<ApiResponse<IProductFindByIdDto>> => {
	const response = await createRequest<IProductFindByIdDto>({
		path: `${PRODUCT_ROUTES.FIND_BY_ID({ id })}`,
		method: "GET",
	});
	return response;
};
