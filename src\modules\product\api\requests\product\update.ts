import { IUpdateProductDto } from "@/modules/product/dtos/product/update-product.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productUpdateRequest = async ({
	items,
	productId,
}: {
	productId: number;
	items: IUpdateProductDto;
}): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const response = await createRequest<IGlobalMessageReturn>({
		path: `${PRODUCT_ROUTES.UPDATE({ id: productId })}`,
		method: "PATCH",
		body: items,
	});
	return response;
};
