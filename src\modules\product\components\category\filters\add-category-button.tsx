import { useCategoryCreateForm } from "@/modules/product/hooks/category/create-form.hook";
import { useCategoryCreate } from "@/modules/product/hooks/category/create.hook";
import { SheetModal } from "@/shared/components/custom/sheet-modal";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { TbTagPlus } from "react-icons/tb";

export function AddCategoryButton() {
	const methods = useCategoryCreateForm();
	const { mutate } = useCategoryCreate();

	return (
		<SheetModal
			trigger={
				<Button
					className="
			hidden
			md:flex
			items-center
			gap-2
			bg-mainColor
			text-white
			px-4
			py-2
			rounded-md
			text-sm
			font-medium
			hover:bg-mainColor/90
			transition-colors
			shadow-sm
		  "
				>
					<TbTagPlus size={18} />
					Adicionar
				</Button>
			}
			title="Adicionar Categoria"
			description="Preencha as informações para adicionar uma nova categoria."
			// footer={
			// 	<div className="mt-6 flex justify-end">
			// 		{/* <SheetClose asChild> */}
			// 		<Button
			// 			type="submit"
			// 			className="
			// 	flex
			// 	items-center
			// 	gap-2
			// 	bg-mainColor
			// 	text-white
			// 	px-4
			// 	py-2
			// 	rounded-md
			// 	text-sm
			// 	font-medium
			// 	hover:bg-mainColor/90
			// 	transition-colors
			//   "
			// 		>
			// 			Salvar
			// 		</Button>
			// 		{/* </SheetClose> */}
			// 	</div>
			// }
		>
			<form onSubmit={methods.handleSubmit(data => mutate(data))} className="flex flex-col gap-4">
				<div className="grid w-full max-w-sm items-center gap-2">
					<Label htmlFor="category-name" className="text-gray-700 font-medium">
						Nome da Categoria
					</Label>
					<Input {...methods.register("name")} id="category-name" placeholder="Ex.: Eletrônicos" className="border rounded-md px-3 py-2" />
					{methods.formState.errors.name && <span className="text-red-500">{methods.formState.errors.name.message}</span>}
				</div>
				<div className="mt-6 flex justify-end">
					{/* <SheetClose asChild> */}
					<Button
						type="submit"
						className="
				flex
				items-center
				gap-2
				bg-mainColor
				text-white
				px-4
				py-2
				rounded-md
				text-sm
				font-medium
				hover:bg-mainColor/90
				transition-colors
			  "
					>
						Salvar
					</Button>
					{/* </SheetClose> */}
				</div>
			</form>
		</SheetModal>
	);
}
