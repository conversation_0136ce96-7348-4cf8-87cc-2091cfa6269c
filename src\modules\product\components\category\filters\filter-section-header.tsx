import { Filter } from "lucide-react";
import { AddCategoryButton } from "./add-category-button";

interface FilterSectionHeaderProps {
	title: string;
}

export function FilterSectionHeader({ title }: FilterSectionHeaderProps) {
	return (
		<div className="flex items-center justify-between">
			<div className="flex items-center gap-2">
				<div className="bg-gray-200 border border-gray-200 p-2 rounded-lg">
					<Filter size={22} className="text-gray-500" />
				</div>
				<span className="text-gray-600 font-medium text-lg">{title}</span>
			</div>
			<AddCategoryButton />
		</div>
	);
}
