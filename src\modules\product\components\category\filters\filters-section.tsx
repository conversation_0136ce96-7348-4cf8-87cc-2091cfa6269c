import { IProductCategoryFilters } from "@/modules/product/validators/category/filter.form";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Filter, RefreshCw, Search, Users } from "lucide-react";
import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { IoAdd } from "react-icons/io5";
import { CreateCategoryModal } from "../table/create-category-modal";
import { CreateGroupModal } from "../../group/create-group-modal";

export const FiltersSection = ({ methods }: { methods: UseFormReturn<IProductCategoryFilters> }) => {
	const { reset } = methods;
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const [isCreateGroupModalOpen, setIsCreateGroupModalOpen] = useState(false);

	const handleReset = () => {
		reset({
			filter: "",
		});
	};

	const handleOpenCreateModal = () => setIsCreateModalOpen(true);
	const handleCloseCreateModal = () => setIsCreateModalOpen(false);

	const handleOpenCreateGroupModal = () => setIsCreateGroupModalOpen(true);
	const handleCloseCreateGroupModal = () => setIsCreateGroupModalOpen(false);

	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para refinar sua busca."
			actions={
				<div className="hidden md:flex items-center gap-2">
					<Button
						className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-blue-700 transition-colors shadow-sm"
						onClick={handleOpenCreateGroupModal}
					>
						<Users size={18} />
						Grupo
					</Button>
					<Button
						className="flex items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
						onClick={handleOpenCreateModal}
					>
						<IoAdd size={18} />
						Categoria
					</Button>
				</div>
			}
		>
			<form className="flex flex-col gap-3 md:flex-row md:items-end">
				<div className="w-full">
					<label className="block text-sm font-medium text-gray-600 mb-1">Buscar</label>
					<div className="relative">
						<Input
							type="text"
							placeholder="Digite o nome ou código de barras do produto..."
							className="w-full h-[45px] rounded-[10px] pl-10"
							{...methods.register("filter")}
						/>
						<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>

				<div className="w-full md:w-auto">
					<Button
						type="button"
						variant="outline"
						onClick={handleReset}
						className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2"
					>
						<RefreshCw size={18} className="text-mainColor" />
						<span>Resetar Filtros</span>
					</Button>
				</div>

				<div className="flex md:hidden flex-col gap-2">
					<Button
						type="button"
						className="flex w-full items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-blue-700 transition-colors shadow-sm"
						onClick={handleOpenCreateGroupModal}
					>
						<Users size={18} />
						Adicionar Grupo
					</Button>
					<Button
						type="button"
						className="flex w-full items-center gap-2 bg-mainColor text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-mainColor/90 transition-colors shadow-sm"
						onClick={handleOpenCreateModal}
					>
						<IoAdd size={18} />
						Adicionar Categoria
					</Button>
				</div>
			</form>
			<CreateCategoryModal isOpen={isCreateModalOpen} onClose={handleCloseCreateModal} />
			<CreateGroupModal isOpen={isCreateGroupModalOpen} onClose={handleCloseCreateGroupModal} />
		</CardLMPContainer>
	);
};
