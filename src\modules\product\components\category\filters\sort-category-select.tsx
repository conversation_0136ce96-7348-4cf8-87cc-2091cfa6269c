import { Label } from "@/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";

interface SortCategorySelectProps {
	label?: string;
	name?: string;
	onValueChange?: (value: string) => void;
}

export function SortCategorySelect({ label = "Ordenar por:", name = "order-category", onValueChange }: SortCategorySelectProps) {
	return (
		<div className="flex items-center gap-2">
			<Label htmlFor={name} className="text-sm text-gray-600 hidden md:block">
				{label}
			</Label>
			<Select name={name} onValueChange={onValueChange}>
				<SelectTrigger className="w-full md:w-[180px]">
					<SelectValue placeholder="Ordenar por" />
				</SelectTrigger>
				<SelectContent>
					<SelectItem value="recentes">Mai<PERSON></SelectItem>
					<SelectItem value="antigos">Mai<PERSON></SelectItem>
					<SelectItem value="nome-asc">Nome (A-Z)</SelectItem>
					<SelectItem value="nome-desc">Nome (Z-A)</SelectItem>
				</SelectContent>
			</Select>
		</div>
	);
}
