import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import React from "react";
import { CategoryRow } from "./category-item";

interface CategoryData {
	id: number;
	name: string;
	totalProducts: number;
	totalSales: number;
	status: "Ativo" | "Inativo";
	icon: React.ElementType;
}

interface CategoryTableProps {
	categories: CategoryData[];
}

export function CategoryTable({ categories }: CategoryTableProps) {
	return (
		<div className="w-full flex flex-col">
			<Table className="w-full table-auto text-sm">
				<TableHeader className="border-b border-gray-200 ">
					<TableRow className="text-left text-gray-700 font-semibold">
						<TableHead className="py-3 px-2">Categoria</TableHead>
						<TableHead className="py-3 px-2 text-center">Total de Produtos</TableHead>
						<TableHead className="py-3 px-2 text-center">Total de Vendas</TableHead>
						<TableHead className="py-3 px-2 text-center">Status</TableHead>
						<TableHead className="py-3 px-2 text-right">Ações</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody className="divide-y divide-gray-100">
					{categories.map(category => (
						<CategoryRow key={category.id} category={category} />
					))}
				</TableBody>
			</Table>
		</div>
	);
}
