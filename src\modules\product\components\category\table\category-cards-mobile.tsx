import { ISuccessResponse } from "@/shared/types/requests/requests.type";
import { motion } from "framer-motion";
import { Tag, X } from "lucide-react";
import { ICategoryFindDto } from "../../../dtos/category/find";
import { ICategoryFindAllDto } from "../../../dtos/category/find-all.dto";

interface CategoryCardsMobileProps {
	data: ISuccessResponse<ICategoryFindAllDto> | undefined;
	onDelete?: (category: ICategoryFindDto) => void;
}

export const CategoryCardsMobile = ({ data, onDelete }: CategoryCardsMobileProps) => {
	const categories = data?.success ? data.data.data : [];

	return (
		<div className="grid grid-cols-1 gap-4">
			{categories.map((category: ICategoryFindDto) => (
				<motion.div
					key={category.id}
					initial={{ y: 20, opacity: 0 }}
					animate={{ y: 0, opacity: 1 }}
					className="bg-white border border-gray-200 rounded-[15px] p-4 shadow-sm"
				>
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-2">
							<div className="h-9 w-9 rounded-full bg-gray-100 flex items-center justify-center">
								<Tag className="h-4 w-4 text-gray-600" />
							</div>
							<h3 className="font-medium text-gray-800 text-lg">{category.name}</h3>
						</div>
					</div>

					<div className="border-t border-gray-100 pt-3 mt-2">
						<div className="flex justify-between text-sm">
							<div className="flex justify-end  w-full gap-2">
								<button
									type="button"
									className="text-red-500/70 transition-colors hover:text-red-500 bg-white border border-gray-200 p-1.5 rounded-full"
									onClick={() => onDelete && onDelete(category)}
								>
									<motion.div className="flex gap-2 items-center" whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
										<X className="h-4 w-4" />
										<span className="text-gray-500">Remover</span>
									</motion.div>
								</button>
							</div>
						</div>
					</div>
				</motion.div>
			))}
		</div>
	);
};
