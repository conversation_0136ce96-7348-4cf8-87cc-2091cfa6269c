import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { motion } from "framer-motion";
import { IoAdd } from "react-icons/io5";
import { useCategoryCreateForm } from "../../../hooks/category/create-form.hook";
import { useCategoryCreate } from "../../../hooks/category/create.hook";
import { GroupSelect } from "../../group/group-select";

interface CreateCategoryModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const CreateCategoryModal = ({ isOpen, onClose }: CreateCategoryModalProps) => {
	const methods = useCategoryCreateForm();
	const { handleSubmit, register, formState, reset, setValue, watch } = methods;
	const { errors, isSubmitting } = formState;
	const createCategory = useCategoryCreate();
	const groupId = watch("groupId");

	const onSubmit = async (data: { name: string; groupId: number }) => {
		await createCategory.mutateAsync(data, {
			onSuccess: () => {
				reset();
				onClose();
			},
		});
	};

	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md mx-4 sm:mx-0 relative shadow-lg overflow-hidden p-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-mainColor/10 p-2 rounded-full">
						<IoAdd size={24} className="text-mainColor" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">Nova Categoria</h2>
				</div>
				<form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
					<div>
						<GroupSelect
							value={groupId ? String(groupId) : ""}
							onChange={value => setValue("groupId", Number(value))}
							error={errors.groupId?.message}
							required
							label="Grupo"
						/>
					</div>
					<div>
						<label className="block text-sm font-medium text-gray-600 mb-1">Nome da categoria</label>
						<Input
							type="text"
							placeholder="Digite o nome da categoria"
							{...register("name")}
							className="w-full h-[45px] rounded-[10px]"
							autoFocus
						/>
						{errors.name && <span className="text-xs text-red-500 mt-1 block">{errors.name.message as string}</span>}
					</div>
					<div className="flex justify-end gap-3 mt-2">
						<Button type="button" onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
							Cancelar
						</Button>
						<Button
							type="submit"
							className="bg-mainColor text-white hover:bg-mainColor/90"
							disabled={isSubmitting || createCategory.isPending}
						>
							{createCategory.isPending ? "Salvando..." : "Salvar"}
						</Button>
					</div>
				</form>
			</motion.div>
		</OverlayContainer>
	);
};
