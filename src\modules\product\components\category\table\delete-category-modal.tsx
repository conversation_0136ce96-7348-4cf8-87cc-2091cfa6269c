import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { motion } from "framer-motion";
import { Trash2 } from "lucide-react";
import { ICategoryFindDto } from "../../../dtos/category/find";

interface DeleteCategoryModalProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	isPending: boolean;
	category: ICategoryFindDto | null;
}

export const DeleteCategoryModal = ({ isOpen, onClose, onConfirm, isPending, category }: DeleteCategoryModalProps) => {
	if (!category) return null;
	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md mx-4 sm:mx-0 relative shadow-lg overflow-hidden p-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-red-100 p-2 rounded-full">
						<Trash2 className="h-6 w-6 text-red-500" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">Excluir Categoria</h2>
				</div>
				<p className="text-gray-600 mb-6">
					Esta ação não pode ser desfeita. A categoria <span className="font-semibold">"{category.name}"</span> será permanentemente
					removida do sistema.
				</p>
				<div className="flex justify-end gap-3">
					<Button onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
						Cancelar
					</Button>
					<Button onClick={onConfirm} className="bg-red-500 text-white hover:bg-red-600" disabled={isPending}>
						{isPending ? "Excluindo..." : "Excluir"}
					</Button>
				</div>
			</motion.div>
		</OverlayContainer>
	);
};
