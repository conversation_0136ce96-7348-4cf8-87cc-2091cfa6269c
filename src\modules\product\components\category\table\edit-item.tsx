import { SheetModal } from "@/shared/components/custom/sheet-modal";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { SheetClose } from "@/shared/components/ui/sheet";
import { Edit } from "lucide-react";
import React, { useState } from "react";

interface CategoryData {
	id: number;
	name: string;
	totalProducts: number;
	totalSales: number;
	status: "Ativo" | "Inativo";
	icon: React.ElementType;
}

interface EditCategoryModalProps {
	category: CategoryData;
	onSave: (updatedCategory: CategoryData) => void;
	onDelete: (id: number) => void;
}

export function EditCategoryModal({ category, onSave, onDelete }: EditCategoryModalProps) {
	const [name, setName] = useState(category.name);

	const handleSave = () => {
		const updatedCategory = { ...category, name };
		onSave(updatedCategory);
	};

	const handleDelete = () => {
		onDelete(category.id);
	};

	return (
		<SheetModal
			trigger={
				<Button variant="ghost" className="p-0 hover:bg-transparent text-mainColor hover:text-mainColor/80">
					<Edit size={20} />
				</Button>
			}
			title="Editar Categoria"
			description="Atualize os dados da categoria."
			footer={
				<div className="mt-6 flex justify-end gap-2">
					<SheetClose asChild>
						<Button variant="ghost" onClick={handleDelete} className="bg-red-500 text-white hover:bg-red-600">
							Excluir
						</Button>
					</SheetClose>
					<SheetClose asChild>
						<Button onClick={handleSave} className="bg-mainColor text-white hover:bg-mainColor/90">
							Salvar
						</Button>
					</SheetClose>
				</div>
			}
		>
			<div className="flex flex-col gap-4">
				<div className="grid w-full max-w-sm items-center gap-2">
					<Label htmlFor="edit-category-name" className="text-gray-700 font-medium">
						Nome da Categoria
					</Label>
					<Input id="edit-category-name" value={name} onChange={e => setName(e.target.value)} className="border rounded-md px-3 py-2" />
				</div>
			</div>
		</SheetModal>
	);
}
