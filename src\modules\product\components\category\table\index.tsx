import { Separator } from "@/shared/components/ui/separator";
import React from "react";

import { Monitor, ShoppingBag } from "lucide-react";
import { CategoryTable } from "./categories-table";
import { CategoryPagination } from "./pagination";

interface CategoryData {
	id: number;
	name: string;
	totalProducts: number;
	totalSales: number;
	status: "Ativo" | "Inativo";
	icon: React.ElementType;
}

const mockCategories: CategoryData[] = [
	{
		id: 1,
		name: "<PERSON><PERSON><PERSON>",
		totalProducts: 100,
		totalSales: 250,
		status: "Ativo",
		icon: ShoppingBag,
	},
	{
		id: 2,
		name: "Eletrônicos",
		totalProducts: 80,
		totalSales: 190,
		status: "Inativo",
		icon: Monitor,
	},
];

export function CategoryTableSection() {
	const [currentPage, setCurrentPage] = React.useState(1);
	const [itemsPerPage, setItemsPerPage] = React.useState(10);

	return (
		<section id="table-category" className="w-full bg-white p-5 shadow-sm rounded-xl flex flex-col gap-4">
			<CategoryTable categories={mockCategories} />
			<Separator className="bg-gray-200" orientation="horizontal" />
			<CategoryPagination
				currentPage={currentPage}
				totalPages={10}
				onPageChange={setCurrentPage}
				itemsPerPage={itemsPerPage}
				onItemsPerPageChange={setItemsPerPage}
			/>
		</section>
	);
}
