import { Search } from "lucide-react";

const NoRecordsState = () => {
	return (
		<div className="bg-white border border-gray-200 rounded-[15px] p-6 flex flex-col items-center justify-center gap-4 min-h-[300px]">
			<div className="h-14 w-14 rounded-full bg-gray-100 flex items-center justify-center">
				<Search className="h-7 w-7 text-gray-500" />
			</div>
			<div className="text-center">
				<h3 className="text-lg font-medium text-gray-800">Nenhuma categoria encontrada</h3>
				<p className="text-gray-600">Tente ajustar seus filtros ou adicione uma nova categoria.</p>
			</div>
		</div>
	);
};

export default NoRecordsState;
