import { Button } from "@/shared/components/ui/button";
import { Label } from "@/shared/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface CategoryPaginationProps {
	currentPage: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	itemsPerPage: number;
	onItemsPerPageChange: (items: number) => void;
}

export function CategoryPagination({ currentPage, totalPages, onPageChange, itemsPerPage, onItemsPerPageChange }: CategoryPaginationProps) {
	return (
		<div className="w-full flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
			<div className="flex items-center gap-2">
				<Label htmlFor="items-per-page-category-list" className="text-sm text-gray-600 hidden md:block">
					Itens por página:
				</Label>
				<Select
					defaultValue={itemsPerPage.toString()}
					name="items-per-page-category-list"
					onValueChange={value => onItemsPerPageChange(Number(value))}
				>
					<SelectTrigger className="w-[70px]">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="10">10</SelectItem>
						<SelectItem value="20">20</SelectItem>
						<SelectItem value="30">30</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<div className="text-sm text-gray-600 flex justify-center">
				<span>
					Página <strong>{currentPage}</strong> de <strong>{totalPages}</strong>
				</span>
			</div>

			<div className="flex items-center gap-2 justify-center md:justify-end">
				<Button
					variant="ghost"
					className="p-0 hover:bg-transparent text-mainColor hover:text-mainColor/80"
					disabled={currentPage <= 1}
					onClick={() => onPageChange(currentPage - 1)}
				>
					<ChevronLeft size={20} />
				</Button>
				<Button
					variant="ghost"
					className="p-0 hover:bg-transparent text-mainColor hover:text-mainColor/80"
					disabled={currentPage >= totalPages}
					onClick={() => onPageChange(currentPage + 1)}
				>
					<ChevronRight size={20} />
				</Button>
			</div>
		</div>
	);
}
