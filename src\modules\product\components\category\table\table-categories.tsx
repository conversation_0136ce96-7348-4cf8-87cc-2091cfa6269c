import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Button } from "@/shared/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ISuccessResponse } from "@/shared/types/requests/requests.type";
import { ColumnDef, flexRender, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from "@tanstack/react-table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUpDown, Tag, Trash2 } from "lucide-react";
import React, { useState } from "react";
import { ICategoryFindDto } from "../../../dtos/category/find";
import { ICategoryFindAllDto } from "../../../dtos/category/find-all.dto";
import { useCategoryDelete } from "../../../hooks/category/delete.hook";
import { useCategoryFindAll } from "../../../hooks/category/find-all.hook";
import { CategoryCardsMobile } from "./category-cards-mobile";
import { DeleteCategoryModal } from "./delete-category-modal";
import LoadingState from "./loading-state";
import NoRecordsState from "./no-records-state";

interface TableCategoriesProps {
	filter: string | undefined;
	page: number;
	itemsPerPage: number;
	onPageChange: (page: number) => void;
	onItemsPerPageChange: (itemsPerPage: number) => void;
}

export const TableCategories = ({ filter, page, itemsPerPage, onPageChange, onItemsPerPageChange }: TableCategoriesProps) => {
	const { data, isLoading } = useCategoryFindAll({ page, limit: itemsPerPage, filter: filter });
	const [sorting, setSorting] = React.useState<SortingState>([]);
	const { onDelete, isPending } = useCategoryDelete();
	const [categoryToDelete, setCategoryToDelete] = useState<ICategoryFindDto | null>(null);
	const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

	const handleDeleteCategory = () => {
		if (!categoryToDelete) return;

		onDelete({ id: categoryToDelete.id });
		setCategoryToDelete(null);
	};

	const handleOpenDeleteModal = (category: ICategoryFindDto) => {
		setCategoryToDelete(category);
		setIsDeleteModalOpen(true);
	};

	const handleCloseDeleteModal = () => {
		setIsDeleteModalOpen(false);
		setCategoryToDelete(null);
	};

	const columns: ColumnDef<ICategoryFindDto, ICategoryFindDto>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => {
				return (
					<div
						className="flex items-center justify-center gap-1 cursor-pointer group"
						onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
					>
						<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Categoria</span>
						<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
					</div>
				);
			},
			cell: ({ row }) => <div className="text-center font-medium">{row.getValue("name")}</div>,
		},
		{
			id: "actions",
			header: () => <div className="text-right pr-5 text-gray-700 font-semibold">Ações</div>,
			cell: ({ row }) => (
				<div className="flex items-center justify-end gap-1 pr-5">
					<CustomTooltip content="Excluir">
						<motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 rounded-full text-red-500/70 transition-colors hover:bg-red-50 hover:text-red-500"
								onClick={() => handleOpenDeleteModal(row.original)}
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</motion.div>
					</CustomTooltip>
				</div>
			),
		},
	];

	const tableData = (data as ISuccessResponse<ICategoryFindAllDto>)?.data?.data || [];

	const table = useReactTable<ICategoryFindDto>({
		data: tableData,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		state: {
			sorting,
		},
	});

	return (
		<CardLMPContainer
			description="Gerencie as categorias de produtos do seu negócio."
			icon={<Tag size={22} className="text-gray-500" />}
			title="Categorias de Produtos"
		>
			{isLoading ? (
				<LoadingState />
			) : !tableData.length ? (
				<NoRecordsState />
			) : (
				<>
					<div className="hidden md:block">
						<AnimatePresence mode="wait">
							<motion.div
								key={page + itemsPerPage}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								transition={{ duration: 0.2 }}
								className="border rounded-[15px] border-gray-200 overflow-hidden max-h-[600px] overflow-y-auto"
							>
								<Table className="w-full rounded-[15px] text-sm min-w-[700px]">
									<TableHeader className="sticky top-0 bg-gradient-to-r from-gray-50 to-white z-10 border-b border-gray-200">
										{table.getHeaderGroups().map(headerGroup => (
											<TableRow key={headerGroup.id} className="hover:bg-transparent">
												{headerGroup.headers.map(header => (
													<TableHead key={header.id} className="py-4 px-2" style={{ width: `${header.column.getSize()}%` }}>
														{header.isPlaceholder
															? null
															: flexRender(header.column.columnDef.header, header.getContext())}
													</TableHead>
												))}
											</TableRow>
										))}
									</TableHeader>
									<TableBody className="[&>tr:nth-child(even)]:bg-[#f9fbfc] [&>tr:hover]:bg-[#f1faff] [&>tr:hover]:cursor-pointer">
										{table.getRowModel().rows.map(row => (
											<TableRow key={row.id} className="hover:bg-gray-50 transition-colors">
												{row.getVisibleCells().map(cell => (
													<TableCell key={cell.id} className="py-3 px-2">
														{flexRender(cell.column.columnDef.cell, cell.getContext())}
													</TableCell>
												))}
											</TableRow>
										))}
									</TableBody>
								</Table>
							</motion.div>
						</AnimatePresence>
					</div>

					<div className="md:hidden">
						<AnimatePresence mode="wait">
							<motion.div
								key={page + itemsPerPage}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								transition={{ duration: 0.2 }}
							>
								<CategoryCardsMobile data={data?.success ? data : undefined} onDelete={handleOpenDeleteModal} />
							</motion.div>
						</AnimatePresence>
					</div>
				</>
			)}

			<DeleteCategoryModal
				isOpen={isDeleteModalOpen}
				onClose={handleCloseDeleteModal}
				onConfirm={handleDeleteCategory}
				isPending={isPending}
				category={categoryToDelete}
			/>

			<Pagination
				totalPages={data?.success ? data?.data.total : 0}
				itemsPerPage={itemsPerPage}
				page={page}
				onPageChange={onPageChange}
				onItemsPerPageChange={onItemsPerPageChange}
			/>
		</CardLMPContainer>
	);
};
