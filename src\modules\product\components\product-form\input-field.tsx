import { Input } from "@/shared/components/ui/input";
import { motion } from "framer-motion";
import { AlertCircle } from "lucide-react";
import { Controller, UseFormReturn } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { ProductFormData } from "../../validators/product/product-form.schema";

export interface InputFieldProps {
	label: string;
	name:
		| "name"
		| "barcode"
		| "ncm"
		| "description"
		| "categories"
		| "images"
		| "costPrice"
		| "price"
		| "code"
		| "supplierId"
		| `categories.${number}`
		| `images.${number}`
		| `packages.${number}.name`
		| `packages.${number}.barcode`
		| `packages.${number}.code`
		| `packages.${number}.quantityPerPackage`;
	type?: string;
	required?: boolean;
	placeholder?: string;
	icon?: React.ReactNode;
	methods: UseFormReturn<ProductFormData>;
	onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const InputField: React.FC<InputFieldProps> = ({
	label,
	name,
	type = "text",
	required = false,
	placeholder,
	icon,
	methods,
	onChange,
}): JSX.Element => {
	const {
		register,
		formState: { errors },
		clearErrors,
	} = methods;

	const getNestedError = (path: string): string | undefined => {
		type NestedErrors = {
			[key: string]: NestedErrors | { message?: string };
		};

		const result = path.split(".").reduce(
			(obj: NestedErrors | undefined, key) => {
				return obj && (obj[key] as NestedErrors | undefined);
			},
			errors as unknown as NestedErrors
		);

		return (result as { message?: string })?.message;
	};

	const error = getNestedError(name);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
		if (type === "number") {
			const value = e.target.value;
			const cleanValue = value.replace(/[R$\s.]/g, "").replace(",", ".");
			const numericValue = parseFloat(cleanValue);
			if (!isNaN(numericValue)) {
				e.target.value = numericValue.toString();
			}
		}
		if (onChange) {
			onChange(e);
		}
		clearErrors(name as keyof ProductFormData);
	};

	return (
		<div className="w-full">
			<label htmlFor={name} className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
				{icon && <span className="text-gray-500">{icon}</span>}
				{label} {required && <span className="text-red-500 text-xs">*</span>}
			</label>
			<div className="relative">
				{type === "number" ? (
					<Controller
						control={methods.control}
						name={name as keyof ProductFormData}
						render={({ field: { value, onChange: fieldOnChange } }) => {
							const isPrice = name.includes("price") || name.includes("cost");
							const numericValue = typeof value === "string" ? Number(value) : value;
							return (
								<NumericFormat
									value={typeof numericValue === "number" && !isNaN(numericValue) ? numericValue : undefined}
									onValueChange={values => {
										fieldOnChange(values.floatValue ?? 0);
									}}
									thousandSeparator="."
									decimalSeparator=","
									decimalScale={2}
									fixedDecimalScale={isPrice}
									prefix={isPrice ? "R$ " : ""}
									placeholder={placeholder ?? (isPrice ? "R$ 0,00" : "0")}
									customInput={Input}
									className={`
                                        w-full px-4 py-2.5 text-sm transition-all duration-200
                                        border rounded-lg bg-white/50 
                                        focus:outline-none focus:ring-2 focus:ring-mainColor/50
                                        disabled:bg-gray-100 disabled:cursor-not-allowed
                                        ${error ? "border-red-300 focus:border-red-500 focus:ring-red-500/30" : "border-gray-200 hover:border-gray-300"}
                                    `}
								/>
							);
						}}
					/>
				) : (
					<input
						id={name}
						type={type}
						placeholder={placeholder}
						className={`
                            w-full px-4 py-2.5 text-sm transition-all duration-200
                            border rounded-lg bg-white/50 
                            focus:outline-none focus:ring-2 focus:ring-mainColor/50
                            disabled:bg-gray-100 disabled:cursor-not-allowed
                            ${error ? "border-red-300 focus:border-red-500 focus:ring-red-500/30" : "border-gray-200 hover:border-gray-300"}
                        `}
						{...register(name as keyof ProductFormData, {
							onChange: handleInputChange,
						})}
					/>
				)}
				{error && (
					<motion.div
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						className="absolute mt-1 text-xs text-red-500 flex items-center gap-1"
					>
						<AlertCircle size={12} />
						<span className="font-medium">{error}</span>
					</motion.div>
				)}
			</div>
		</div>
	);
};
