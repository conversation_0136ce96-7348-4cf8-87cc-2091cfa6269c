import { Info, Package, Tag } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { ProductFormData } from "../../validators/product/product-form.schema";
import { InputField } from "./input-field";

interface MainInfoCardProps {
	methods: UseFormReturn<ProductFormData>;
	isInEditMode?: boolean;
}

export const MainInfoCard: React.FC<MainInfoCardProps> = ({ methods, isInEditMode = false }): JSX.Element => {
	const watch = methods.watch;

	return (
		<div className="bg-gray-50/50 rounded-lg p-4 border border-gray-100">
			<div className="flex items-center gap-2 mb-3">
				<div className="p-1.5 bg-mainColor/10 rounded-lg">
					<Package size={16} className="text-mainColor" />
				</div>
				<label className="text-sm font-medium text-gray-700">Dados Principais</label>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				{isInEditMode ? (
					<>
						<div className="w-full">
							<label className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
								<span className="text-gray-500">
									<Info size={14} />
								</span>
								Código do Produto
							</label>
							<div className="relative">
								<div className="flex">
									<span className="inline-flex items-center px-3 text-gray-500 bg-gray-50 border border-r-0 border-gray-200 rounded-l-lg">
										<Info size={14} />
									</span>
									<input
										type="text"
										value={watch("code") || ""}
										readOnly
										className="w-full px-3 py-2 text-sm transition-all duration-200
                                            border rounded-lg rounded-l-none bg-gray-50 text-gray-500 cursor-not-allowed
                                            border-gray-200"
									/>
								</div>
							</div>
						</div>

						<InputField
							label="Nome do Produto"
							name="name"
							required
							placeholder="Ex: iPhone 14 Pro"
							icon={<Package size={14} />}
							methods={methods}
						/>

						<div className="w-full md:col-span-2">
							<InputField
								label="Código de Barras"
								name="barcode"
								required
								placeholder="Ex: 123456789"
								icon={<Tag size={14} />}
								methods={methods}
							/>
						</div>
					</>
				) : (
					<>
						<InputField
							label="Nome do Produto"
							name="name"
							required
							placeholder="Ex: iPhone 14 Pro"
							icon={<Package size={14} />}
							methods={methods}
						/>

						<InputField
							label="Código de Barras"
							name="barcode"
							required
							placeholder="Ex: 123456789"
							icon={<Tag size={14} />}
							methods={methods}
						/>
					</>
				)}
			</div>
		</div>
	);
};
