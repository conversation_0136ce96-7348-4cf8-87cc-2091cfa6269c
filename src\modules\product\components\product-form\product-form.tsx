import { CardLMPContainer } from "@/shared/components/custom/card";
import { Info } from "lucide-react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { useCreateProductImage } from "../../hooks/product/create-product-image.hook";
import { useProductId } from "../../hooks/use-product-id.hook";
import { ProductFormData } from "../../validators/product/product-form.schema";
import { AdditionalInfoCard } from "./additional-info-card";
import { ClassificationCard } from "./classification-card";
import { MainInfoCard } from "./main-info-card";
import { PackagesCard } from "./packages-card";
import { PricingCard } from "./pricing-card";
import { ProductImagesCard } from "./product-images-card";

export interface ProductFormProps {
	methods: UseFormReturn<ProductFormData>;
	onSubmit: () => void;
	isSubmitting: boolean;
	isInEditMode?: boolean;
	isLoadingImage?: boolean;
	imageError?: string | null;
}

export const ProductForm: React.FC<ProductFormProps> = ({
	methods,
	onSubmit,
	isSubmitting,
	isInEditMode = false,
	isLoadingImage = false,
	imageError = null,
}): JSX.Element => {
	const { handleSubmit } = methods;
	const { createProductImage } = useCreateProductImage();
	const id = useProductId();
	const productId = isInEditMode ? id : 0;

	const packageFields = useFieldArray({
		control: methods.control,
		name: "packages",
	});

	const handleImageUpload = async (file: File): Promise<void> => {
		if (file.size > 5 * 1024 * 1024) {
			methods.setError("images", {
				type: "manual",
				message: "A imagem deve ter no máximo 5MB",
			});
			return;
		}

		const currentImages = methods.watch("images") || [];
		if (currentImages.length >= 1) {
			methods.setError("images", {
				type: "manual",
				message: "Só é permitido 1 imagem por produto.",
			});
			return;
		}

		if (isInEditMode && productId) {
			await createProductImage(productId, file);
			methods.setValue("images", [file]);
		} else {
			methods.setValue("images", [file]);
		}
	};

	return (
		<form onSubmit={handleSubmit(onSubmit, console.error)} className="mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6">
			<div className="lg:col-span-8 flex flex-col space-y-6">
				<CardLMPContainer
					icon={<Info size={22} className="text-gray-500" />}
					title="Informações do Produto"
					description="Preencha as informações básicas do produto"
				>
					<div className="space-y-4">
						<MainInfoCard methods={methods} isInEditMode={isInEditMode} />
						<ClassificationCard methods={methods} isInEditMode={isInEditMode} />
						<AdditionalInfoCard methods={methods} />
					</div>
				</CardLMPContainer>
				<PackagesCard methods={methods} packageFields={packageFields} isEditMode={isInEditMode} />
			</div>

			<div className="lg:col-span-4">
				<div className="sticky top-6 space-y-6">
					<ProductImagesCard
						methods={methods}
						isInEditMode={isInEditMode}
						isLoadingImage={isLoadingImage}
						imageError={imageError}
						productId={productId}
						handleImageUpload={handleImageUpload}
					/>
					<PricingCard methods={methods} isSubmitting={isSubmitting} isInEditMode={isInEditMode} onSubmit={onSubmit} />
				</div>
			</div>
		</form>
	);
};
