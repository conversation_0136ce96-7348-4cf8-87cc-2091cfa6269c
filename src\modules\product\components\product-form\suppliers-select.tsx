import { useSuppliersFindAll } from "@/modules/person/hooks/find-all-supliers.hook";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Check, ChevronsUpDown, Search, Store } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

interface Supplier {
	id: number;
	name: string;
}

interface SuppliersSelectProps {
	value?: string;
	onChange: (value: string) => void;
	error?: string;
	required?: boolean;
	label?: string;
	icon?: React.ReactNode;
}

export const SuppliersSelect: React.FC<SuppliersSelectProps> = ({
	value,
	onChange,
	error,
	required = false,
	label = "Fornecedor",
	icon = <Store size={16} />,
}) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [isSearching, setIsSearching] = useState(false);
	const { data: suppliers, isLoading } = useSuppliersFindAll({ filter: searchTerm });

	useEffect(() => {
		if (searchTerm) {
			setIsSearching(true);
		}
	}, [searchTerm]);

	useEffect(() => {
		if (suppliers) {
			setIsSearching(false);
		}
	}, [suppliers]);

	const selectedSupplier = useMemo(
		() => suppliers?.success && suppliers.data.data.find((supplier: Supplier) => String(supplier.id) === String(value)),
		[value, suppliers]
	);

	const getButtonText = () => {
		if (selectedSupplier) return selectedSupplier.name;
		if (!isLoading) return "Selecione um fornecedor";
		return "Carregando fornecedores...";
	};

	return (
		<div className="relative">
			<label className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
				<span className="text-gray-500">{icon}</span>
				{label} {required && <span className="text-red-500 text-xs">*</span>}
			</label>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<motion.button
						type="button"
						whileHover={{ scale: 1.01 }}
						whileTap={{ scale: 0.99 }}
						transition={{ type: "spring", stiffness: 400, damping: 17 }}
						className={cn(
							"w-full px-4 py-2.5 text-sm transition-all duration-200",
							"border rounded-lg bg-white/50",
							"focus:outline-none focus:ring-2 focus:ring-mainColor/50",
							"flex items-center justify-between",
							error ? "border-red-300 focus:border-red-500 focus:ring-red-500/30" : "border-gray-200 hover:border-mainColor/40",
							isLoading && "animate-pulse"
						)}
						disabled={isLoading}
					>
						<span className="truncate">{getButtonText()}</span>
						<motion.div animate={{ rotate: open ? 180 : 0 }} transition={{ duration: 0.2 }}>
							<ChevronsUpDown size={16} className="text-gray-500" />
						</motion.div>
					</motion.button>
				</PopoverTrigger>
				<PopoverContent className="w-[--radix-popover-trigger-width] p-0 max-h-[300px] overflow-hidden" align="start">
					<div className="flex flex-col h-full">
						<div className="relative">
							<span className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								<Search size={16} />
							</span>
							<input
								placeholder="Buscar fornecedor..."
								value={searchTerm}
								onChange={e => setSearchTerm(e.target.value)}
								className="h-9 pl-8 pr-2 text-sm border-b border-gray-200 focus:outline-none focus:ring-0 w-full"
							/>
						</div>
						<div className="overflow-y-auto max-h-[260px]">
							{(isSearching || isLoading) && <div className="py-2 px-2 text-sm text-gray-500">Buscando fornecedores...</div>}
							{!isSearching && !isLoading && !suppliers?.success && (
								<div className="py-2 px-2 text-sm text-gray-500">Carregando...</div>
							)}
							{!isSearching && !isLoading && suppliers?.success && suppliers.data.data.length === 0 && (
								<div className="py-2 px-2 text-sm text-gray-500">Nenhum fornecedor encontrado.</div>
							)}
							<AnimatePresence>
								{suppliers?.success &&
									suppliers.data.data.map(supplier => (
										<motion.li
											key={supplier.id + supplier.name}
											initial={{ opacity: 0, y: 10 }}
											animate={{ opacity: 1, y: 0 }}
											exit={{ opacity: 0, y: -10 }}
											transition={{ duration: 0.2 }}
											className="flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-mainColor/5"
											onClick={() => {
												onChange(String(supplier.id));
												setOpen(false);
												setSearchTerm("");
											}}
										>
											<span>{supplier.name}</span>
											{String(supplier.id) === String(value) && <Check size={16} className="text-mainColor" />}
										</motion.li>
									))}
							</AnimatePresence>
						</div>
					</div>
				</PopoverContent>
			</Popover>
			<AnimatePresence>
				{error && (
					<motion.div
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -10 }}
						className="absolute mt-1 text-xs text-red-500 flex items-center gap-1"
					>
						<AlertCircle size={12} />
						<span className="font-medium">{error}</span>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
};
