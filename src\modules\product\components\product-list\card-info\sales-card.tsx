import { SalesCardProps } from "@/modules/product/types/sale-card.type";

export const SalesCard: React.FC<SalesCardProps> = ({ title, amount, orders, percentage, icon: Icon, iconBgColor = "bg-gray-300/20" }) => {
	return (
		<div className="bg-white p-4 md:p-6 justify-between shadow-main rounded-[20px] flex items-center">
			<div className="flex flex-col space-y-2">
				<div className="text-gray-500">{title}</div>
				<div className="text-2xl text-gray-800 font-bold">{amount}</div>
				<div className="text-sm gap-5 text-gray-500 flex items-center">
					<span>{orders}</span>
					{percentage && (
						<span className="text-mainColor bg-bg-mainColor/15 rounded-full font-bold p-1 border border-mainColor px-2 bg-mainColor/10">
							{percentage}
						</span>
					)}
				</div>
			</div>

			<div>
				<span
					className={`flex h-14 w-14 ${iconBgColor} rounded-lg items-center justify-center transition-colors duration-200 hover:bg-gray-300/30`}
					title={title}
				>
					<Icon size={28} className="text-gray-500 font-bold" />
				</span>
			</div>
		</div>
	);
};
