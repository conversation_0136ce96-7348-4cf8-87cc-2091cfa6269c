import { IProductList, IProductListDTO } from "@/modules/product/dtos/product/get-all-products.dto";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Button } from "@/shared/components/ui/button";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { motion } from "framer-motion";
import { DollarSign, Edit, Package, Tag, Trash2 } from "lucide-react";

interface ProductCardMobileProps {
	data: ApiResponse<IProductList> | undefined;
	onDelete?: (product: IProductListDTO) => void;
}

export const ProductCardsMobile = ({ data: list, onDelete }: ProductCardMobileProps) => {
	return (
		<div className="block md:hidden space-y-4">
			{/* <div className="w-full gap-3 flex justify-end">
				<Button className="rounded-lg w-full gap-3 p-4 text-gray-500 bg-white shadow-md">
					Categorias <Tag className="text-gray-500" size={20} />
				</Button>
				<Button className="rounded-lg w-full gap-3 p-4 bg-mainColor/80 shadow-md text-white">
					Adicionar <PackagePlus className="text-white" size={20} />
				</Button>
			</div> */}

			{list?.success &&
				list.data.data.map((item: IProductListDTO) => (
					<motion.div
						key={item.id}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						className="w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm"
					>
						{/* ---------- Cabeçalho ---------- */}
						<header className="flex items-start justify-between gap-3">
							<h3 className="line-clamp-2 max-w-[70%] text-sm font-medium text-gray-800 leading-5">{item.name}</h3>

							<motion.span
								initial={{ scale: 0.8, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								className={`inline-flex shrink-0 items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium ${
									item.status ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
								}`}
							>
								<span className={`h-1.5 w-1.5 rounded-full ${item.status ? "bg-green-500" : "bg-red-500"}`} />
								{item.status ? "Em estoque" : "Fora de estoque"}
							</motion.span>
						</header>

						{/* ---------- Categorias ---------- */}
						<div className="mt-3">
							<div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
								<Tag size={14} className="shrink-0" />
								<span>Categorias:</span>
							</div>
							{item.categories && item.categories.length > 0 ? (
								<div className="flex flex-wrap gap-2">
									{item.categories.map(category => (
										<span
											key={category}
											className="inline-flex items-center px-2 py-1 text-xs rounded-lg bg-mainColor/10 text-mainColor"
										>
											{category}
										</span>
									))}
								</div>
							) : (
								<span className="text-xs text-gray-400 italic">Nenhuma categoria definida</span>
							)}
						</div>

						{/* ---------- Metadados ---------- */}
						<div className="mt-3">
							<div className="grid grid-cols-2 gap-x-4 gap-y-2">
								<div className="flex flex-col gap-1">
									<span className="text-xs text-gray-500">Preço</span>
									<div className="flex items-center gap-1">
										<DollarSign size={14} className="shrink-0 text-gray-400" />
										<span className="font-medium">
											{new Intl.NumberFormat("pt-BR", {
												style: "currency",
												currency: "BRL",
												minimumFractionDigits: 2,
												maximumFractionDigits: 2,
											}).format(item.price)}
										</span>
									</div>
								</div>

								<div className="flex flex-col gap-1">
									<span className="text-xs text-gray-500">Quantidade</span>
									<div className="flex items-center gap-1">
										<Package size={14} className="shrink-0 text-gray-400" />
										<motion.span initial={{ scale: 0.8, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} className="font-medium">
											{item.quantity} un
										</motion.span>
									</div>
								</div>
							</div>
						</div>

						{/* ---------- Ações ---------- */}
						<footer className="mt-4 flex justify-end gap-6 border-t border-gray-100 pt-3">
							{item.status && (
								<CustomTooltip content="Editar Produto">
									<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 rounded-full text-mainColor/70 transition-colors hover:bg-mainColor/10 hover:text-mainColor"
										>
											<Edit className="h-4 w-4" />
										</Button>
									</motion.div>
								</CustomTooltip>
							)}
							<CustomTooltip content="Excluir Produto">
								<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
									<Button
										variant="ghost"
										size="icon"
										className="h-8 w-8 rounded-full text-red-500/70 transition-colors hover:bg-red-50 hover:text-red-500"
										onClick={() => onDelete && onDelete(item)}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</motion.div>
							</CustomTooltip>
						</footer>
					</motion.div>
				))}
		</div>
	);
};
