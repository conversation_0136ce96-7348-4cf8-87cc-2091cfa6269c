// import { TableCell, TableRow } from "@/shared/components/ui/table";
// import { Link } from "@tanstack/react-router";
// import { Edit, Package2 } from "lucide-react";

// interface ItemTableProps {
// 	id: number;
// 	name: string;
// 	price: number;
// 	quantity: number;
// 	category: string[];
// 	status: "Stock" | "Out of stock";
// 	description: string;
// }

// export const ItemTable = ({ ...items }: ItemTableProps) => {
// 	return (
// 		<TableRow className="hover:bg-mainColor/5 cursor-pointer transition-colors duration-200">
// 			<TableCell className="py-3 px-2">
// 				<label className="ios-checkbox blue" title="Selecionar item">
// 					<input type="checkbox" />
// 					<div className="checkbox-wrapper">
// 						<div className="checkbox-bg"></div>
// 						<svg fill="none" viewBox="0 0 24 24" className="checkbox-icon">
// 							<path
// 								strokeLinejoin="round"
// 								strokeLinecap="round"
// 								strokeWidth="3"
// 								stroke="currentColor"
// 								d="M4 12L10 18L20 6"
// 								className="check-path"
// 							></path>
// 						</svg>
// 					</div>
// 				</label>
// 			</TableCell>
// 			<TableCell className="py-3 px-2">
// 				<div className="flex items-center space-x-2">
// 					<Package2 className="h-8 w-8 mr-2 text-mainColor" />
// 					<div>
// 						<div className="font-semibold text-gray-700">{items.name}</div>
// 						<div className="text-gray-400 text-xs">{items.description}</div>
// 					</div>
// 				</div>
// 			</TableCell>
// 			<TableCell className="py-3 gap-2 justify-center flex px-2">
// 				{items.category.map(category => (
// 					<span key={category} className="inline-flex items-center px-2 py-1 text-xs rounded-lg bg-mainColor/10 text-mainColor">
// 						{category}
// 					</span>
// 				))}
// 			</TableCell>

// 			<TableCell className="py-3 px-2 text-center">{items.price.toLocaleString("pt-BR", { style: "currency", currency: "BRL" })}</TableCell>
// 			<TableCell className="py-3 px-2 text-center">{items.quantity}</TableCell>
// 			<TableCell className="py-3 flex justify-center items-center px-2">
// 				{items.status === "Stock" ? (
// 					<span className="inline-flex items-center px-2 py-1 text-xs rounded-lg bg-green-100 text-green-600">Em estoque</span>
// 				) : (
// 					<span className="inline-flex items-center px-2 py-1 text-xs rounded-lg bg-red-100 text-red-600">Fora de estoque</span>
// 				)}
// 			</TableCell>
// 			<TableCell className="py-3 px-2 text-right">
// 				<div className="flex justify-end space-x-2">
// 					<Link
// 						to={`/produtos/$product-id`}
// 						params={{ "product-id": items.id.toString() }}
// 						className="text-cyan-600 hover:underline transition-colors duration-200"
// 						title="Editar produto"
// 					>
// 						<Edit size={16} />
// 					</Link>
// 				</div>
// 			</TableCell>
// 		</TableRow>
// 	);
// };
