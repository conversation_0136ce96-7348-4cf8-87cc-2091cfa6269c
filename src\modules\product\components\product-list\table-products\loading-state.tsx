import { motion } from "framer-motion";

const LoadingState = () => {
	return (
		<div className="flex flex-col items-center justify-center p-8">
			<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="flex flex-col items-center gap-4">
				<div className="h-8 w-8 animate-spin rounded-full border-4 border-mainColor border-t-transparent" />
				<p className="text-gray-600">Carregando produtos...</p>
			</motion.div>
		</div>
	);
};

export default LoadingState;
