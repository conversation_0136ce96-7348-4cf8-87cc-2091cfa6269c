import { motion } from "framer-motion";
import { PackageX } from "lucide-react";

const NoRecordsState = () => {
	return (
		<div className="flex flex-col items-center justify-center p-8">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				exit={{ opacity: 0, y: -20 }}
				className="flex flex-col items-center gap-4 text-center"
			>
				<PackageX className="h-12 w-12 text-gray-400" />
				<div className="space-y-1">
					<p className="text-lg font-medium text-gray-900">Nenhum produto encontrado</p>
					<p className="text-sm text-gray-500">Tente ajustar seus filtros de busca</p>
				</div>
			</motion.div>
		</div>
	);
};

export default NoRecordsState;
