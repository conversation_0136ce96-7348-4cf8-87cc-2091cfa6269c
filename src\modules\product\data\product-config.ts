import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { Box, Package, Tag } from "lucide-react";
import { IoAdd } from "react-icons/io5";
import { PRODUCT_PATHS } from "./routes-paths.data";

export const PRODUCT_SUBITEM_IDS = {
	ALL_PRODUCTS: "all-products",
	CATEGORIES: "categories",
	ADD_PRODUCT: "add-product",
	EDIT_PRODUCT: "edit-product",
} as const;

export const PRODUCT_CONFIG: IItemSidebar = {
	id: "product-group",
	moduleActivated: true,
	label: "Produtos",
	accessibleDescription: "ícone seção de produtos",
	Icon: Package,
	type: "menu",
	path: PRODUCT_PATHS.ALL_PRODUCTS,
	subItems: [
		{
			id: PRODUCT_SUBITEM_IDS.ALL_PRODUCTS,
			label: "Todos os Produtos",
			Icon: Box,
			accessibleDescription: "ícone de todos os produtos",
			path: PRODUCT_PATHS.ALL_PRODUCTS,
		},
		{
			id: PRODUCT_SUBITEM_IDS.CATEGORIES,
			label: "Categorias",
			Icon: Tag,
			accessibleDescription: "ícone de categorias",
			path: PRODUCT_PATHS.CATEGORY,
		},
		{
			id: PRODUCT_SUBITEM_IDS.ADD_PRODUCT,
			label: "Adicionar Produto",
			Icon: IoAdd,
			accessibleDescription: "ícone de adicionar produto",
			path: PRODUCT_PATHS.ADD_PRODUCT,
		},
		{
			id: PRODUCT_SUBITEM_IDS.EDIT_PRODUCT,
			label: "Editar Produto",
			Icon: Box,
			visibleInSidebar: false,
			accessibleDescription: "ícone de editar produto",
			path: PRODUCT_PATHS.EDIT_PRODUCT,
		},
	],
};
