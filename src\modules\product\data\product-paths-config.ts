import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { Box, Package, Tag } from "lucide-react";
import { IoAdd } from "react-icons/io5";

export const PRODUCT_PATH_CONFIG: IItemSidebar = {
	id: "product-group",
	moduleActivated: false,
	label: "Produtos",
	accessibleDescription: "ícone seção de produtos",
	Icon: Package,
	type: "menu",
	path: "/produtos",
	subItems: [
		{
			label: "Todos os Produtos",
			id: "all-products",
			Icon: Box,
			accessibleDescription: "ícone de todos os produtos",
			path: "/produtos",
		},
		{
			label: "Categorias",
			id: "categories",
			Icon: Tag,
			accessibleDescription: "ícone de categorias",
			path: "/produtos/categorias",
		},
		{
			label: "Adicionar Produto",
			id: "add-product",
			Icon: IoAdd,
			accessibleDescription: "ícone de adicionar produto",
			path: "/produtos/adicionar",
		},
	],
};
