export interface IProductFindByIdDto {
	id: number;
	code: string;
	name: string;
	description: string;
	costPrice: number;
	price: number;
	package: {
		id: number;
		name: string;
		code: string;
		barcode: string;
		quantityPerPackage: number;
	};
	supplier: {
		id: number;
		name: string;
	};
	quantity: number;
	barcode: string;
	category: {
		id: number;
		name: string;
	};
	imageUrl: string;
	status: boolean;
}
