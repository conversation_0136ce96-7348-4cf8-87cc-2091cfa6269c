import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { categoryCreateFormSchema, CreateCategoryFormData } from "../../validators/category/create-category.form";

export const useCategoryCreateForm = () => {
	return useForm<CreateCategoryFormData>({
		resolver: zodResolver(categoryCreateFormSchema),
		defaultValues: {
			name: "",
			groupId: undefined,
		},
	});
};
