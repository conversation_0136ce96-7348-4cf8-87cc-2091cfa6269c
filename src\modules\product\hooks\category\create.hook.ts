import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { categoryCreateRequest } from "../../api/requests/category/create";

type CreateParams = { name: string; groupId: number };

export const useCategoryCreate = () => {
	const queryClient = useQueryClient();

	return useMutation<ApiResponse<IGlobalMessageReturn>, Error, CreateParams>({
		mutationFn: async data => {
			const response = await categoryCreateRequest(data);
			if (!response.success) throw new Error(response.data.message);
			return response;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["category-find-all"] });
			toast.dismiss();
			toast.success("Categoria criada com sucesso!");
		},
		onError: error => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
