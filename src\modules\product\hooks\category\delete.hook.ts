import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { categoryDeleteRequest } from "../../api/requests/category/delete";

export const useCategoryDelete = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async ({ id }: { id: number }) => {
			const res = await categoryDeleteRequest({ id });
			if (!res.success) {
				throw new Error(res.data.message);
			}
			return res;
		},
		onSuccess: ({ data }) => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({ queryKey: ["category-find-all"] });
		},
		onError: error => {
			toast.error(error?.message || "Erro ao excluir categoria.");
		},
	});

	return {
		onDelete: (params: { id: number }) => mutation.mutate(params),
		...mutation,
	};
};
