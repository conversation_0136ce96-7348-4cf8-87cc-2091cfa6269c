import { ApiResponse } from "@/shared/types/requests/requests.type";
import { queryOptions, useQuery } from "@tanstack/react-query";
import { useAtomValue } from "jotai";
import { productCategoryFindAllRequest } from "../../api/requests/category/find-all";
import { ICategoryFindAllDto } from "../../dtos/category/find-all.dto";
import { categoryLimitPerPageAtom } from "../../states/category/limit.state";
import { categoryCurrentPageAtom } from "../../states/category/pagination.state";

export const categoryFindAllQuery = ({ page, limit, filter }: { page: number; limit: number; filter?: string }) =>
	queryOptions({
		queryKey: ["category-find-all", { page, limit, filter }],
		queryFn: () =>
			productCategoryFindAllRequest({
				page,
				limit,
				filter,
			}),
	});

export const useCategoryFindAll = ({
	page = 1,
	limit = 10,
	filter,
	groupId,
}: { page?: number; limit?: number; filter?: string; groupId?: string } = {}) => {
	const defaultLimit = useAtomValue(categoryLimitPerPageAtom);
	const defaultPage = useAtomValue(categoryCurrentPageAtom);

	const finalPage = page || defaultPage;
	const finalLimit = limit || defaultLimit;

	const { data, isLoading, isError } = useQuery<ApiResponse<ICategoryFindAllDto>>({
		queryKey: ["category-find-all", { page: finalPage, limit: finalLimit, filter, groupId }],
		queryFn: () => productCategoryFindAllRequest({ page: finalPage, limit: finalLimit, filter, groupId }),
	});

	return { data, isLoading, isError };
};
