import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { IProductCategoryFilters, productCategoryFindAllFiltersSchema } from "../../validators/category/filter.form";

export const useCategoryFilters = () => {
	const methods = useForm<IProductCategoryFilters>({
		resolver: zodResolver(productCategoryFindAllFiltersSchema),
		defaultValues: {
			filter: "",
		},
	});

	const { watch } = methods;
	const filter = watch("filter");
	const debouncedFilter = useDebounce(filter, 500);

	return {
		methods,
		debouncedFilter,
		filter,
	};
};
