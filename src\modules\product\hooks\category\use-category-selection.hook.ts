import { useAtom } from "jotai";
import { selectedCategoriesAtom } from "../../states/category/selected-categories.state";

export const useCategorySelection = () => {
	const [selectedCategories, setSelectedCategories] = useAtom(selectedCategoriesAtom);

	const toggleCategory = (categoryId: string) => {
		setSelectedCategories(prev => (prev.includes(categoryId) ? prev.filter(id => id !== categoryId) : [...prev, categoryId]));
	};

	const addCategory = (categoryId: string) => {
		setSelectedCategories(prev => [...prev, categoryId]);
	};

	const removeCategory = (categoryId: string) => {
		setSelectedCategories(prev => prev.filter(id => id !== categoryId));
	};

	const clearCategories = () => {
		setSelectedCategories([]);
	};

	return {
		selectedCategories,
		toggleCategory,
		addCategory,
		removeCategory,
		clearCategories,
		setSelectedCategories,
	};
};
