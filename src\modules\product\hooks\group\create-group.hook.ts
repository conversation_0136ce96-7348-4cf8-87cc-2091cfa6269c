import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productGroupCreateRequest } from "../../api/requests/group/create";
import { ICreateGroupDto } from "../../dtos/group/create-group.dto";

export const useCreateGroup = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async (data: ICreateGroupDto): Promise<IGlobalMessageReturn> => {
			toast.loading("Criando grupo...");
			const response = await productGroupCreateRequest(data);
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success("Grupo criado com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-groups-find-all"] });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const createGroup = (data: ICreateGroupDto): void => {
		mutation.mutate(data);
	};

	return { createGroup, isLoading: mutation.isPending };
};
