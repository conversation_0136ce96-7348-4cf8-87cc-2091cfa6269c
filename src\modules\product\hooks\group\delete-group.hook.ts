import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productGroupDeleteRequest } from "../../api/requests/group/delete";

export const useDeleteGroup = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async (id: number): Promise<IGlobalMessageReturn> => {
			toast.loading("Excluindo grupo...");
			const response = await productGroupDeleteRequest(id);
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success("Grupo excluído com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-groups-find-all"] });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const deleteGroup = (id: number): void => {
		mutation.mutate(id);
	};

	return { deleteGroup, isLoading: mutation.isPending };
};
