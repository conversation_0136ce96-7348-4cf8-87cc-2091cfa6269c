import { queryOptions, useQuery } from "@tanstack/react-query";
import { productGroupFindAllRequest } from "../../api/requests/group/find-all";

export const createFindAllGroupsQuery = ({ page, limit, filter }: { page: number; limit: number; filter?: string }) =>
	queryOptions({
		queryKey: ["product-groups-find-all", { page, limit, filter }],
		queryFn: () => productGroupFindAllRequest({ page, limit, search: filter }),
		refetchOnWindowFocus: true,
	});

export const useFindAllGroups = ({ page = 1, limit = 10, filter }: { page?: number; limit?: number; filter?: string }) => {
	const queryOptions = createFindAllGroupsQuery({ page, limit, filter });
	const { data, isLoading, isError } = useQuery(queryOptions);
	return { data, isLoading, isError };
};
