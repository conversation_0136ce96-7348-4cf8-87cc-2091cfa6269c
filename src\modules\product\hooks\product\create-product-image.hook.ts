import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productCreateImageRequest } from "../../api/requests/product/create-image";

export const useCreateProductImage = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async ({ productId, image }: { productId: number; image: File }) => {
			toast.loading("Adicionando imagem...");
			const response = await productCreateImageRequest({ productId, image });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: (_, variables) => {
			toast.dismiss();
			toast.success("Imagem adicionada com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-find-by-id", { id: variables.productId }] });
		},
		onError: (error: Error) => {
			console.error(error);
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const createProductImage = (productId: number, image: File) => {
		mutation.mutate({ productId, image });
	};

	return { createProductImage, isLoading: mutation.isPending };
};
