import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";
import { productCreateRequest } from "../../api/requests/product/create";
import { ICreateProductDto } from "../../dtos/product/create-product.dto";

export const useCreateProduct = () => {
	const queryClient = useQueryClient();
	const navigate = useNavigate();

	const mutation = useMutation({
		mutationFn: async (data: ICreateProductDto): Promise<IGlobalMessageReturn> => {
			toast.loading("Criando produto...");
			const response = await productCreateRequest({
				barcode: data.barcode,
				categoryId: data.categoryId,
				costPrice: data.costPrice,
				description: data.description,
				image: data.image,
				name: data.name,
				ncm: data.ncm,
				price: data.price,
				...(data.package && {
					package: {
						barcode: data.package.barcode,
						code: data.package.code,
						name: data.package.name,
						quantityPerPackage: data.package.quantityPerPackage,
					},
				}),
				supplierId: data.supplierId,
			});
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success("Produto criado com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["products-find-all"] });
			navigate({ to: "/produtos" });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const createProduct = (data: ICreateProductDto): void => {
		mutation.mutate({
			...data,
			supplierId: Number(data.supplierId),
		});
	};

	return { createProduct };
};
