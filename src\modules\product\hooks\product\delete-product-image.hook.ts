import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productDeleteImageRequest } from "../../api/requests/product/delete-image";

export const useDeleteProductImage = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async ({ productId }: { productId: number }) => {
			toast.loading("Excluindo imagem...");
			const response = await productDeleteImageRequest({ productId });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: (_, variables) => {
			toast.dismiss();
			toast.success("Imagem excluída com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-find-by-id", { id: variables.productId }] });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const deleteProductImage = (productId: number) => {
		mutation.mutate({ productId });
	};

	return { deleteProductImage, isLoading: mutation.isPending };
};
