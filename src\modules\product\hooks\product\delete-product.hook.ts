import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { deleteProductRequest } from "../../api/requests/product/delete";

export const useDeleteProduct = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async ({ id }: { id: number }) => {
			toast.loading("Excluindo produto...");
			const response = await deleteProductRequest({ id });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success("Produto excluído com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["products-find-all"] });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const deleteProduct = (id: number) => {
		mutation.mutate({ id });
	};

	return { deleteProduct, isLoading: mutation.isPending };
};
