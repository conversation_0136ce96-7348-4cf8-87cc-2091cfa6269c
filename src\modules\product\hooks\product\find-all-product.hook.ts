import { queryOptions, useQuery } from "@tanstack/react-query";
import { productsFindAllRequest } from "../../api/requests/product/find-all";

export const createFindAllProductQuery = ({ page, limit, filter }: { page: number; limit: number; filter?: string }) =>
	queryOptions({
		queryKey: ["products-find-all", { page, limit, filter }],
		queryFn: () => productsFindAllRequest({ page, limit, filter }),
		refetchOnWindowFocus: true,
	});

export const useFindAllProduct = ({ page = 1, limit = 10, filter }: { page?: number; limit?: number; filter?: string }) => {
	const queryOptions = createFindAllProductQuery({ page, limit, filter });
	const { data, isLoading, isError } = useQuery(queryOptions);
	return { data, isLoading, isError };
};
