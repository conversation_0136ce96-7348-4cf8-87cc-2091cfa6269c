import { useQuery } from "@tanstack/react-query";
import { productFindByIdRequest } from "../../api/requests/product/find-by-id";

export const useFindProductById = (id: string) => {
	const findProductById = useQuery({
		queryKey: ["find-product-by-id", id],
		queryFn: async () => {
			const response = await productFindByIdRequest({
				id: Number(id),
			});

			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
	});

	return { findProductById: findProductById.data, isLoading: findProductById.isPending };
};
