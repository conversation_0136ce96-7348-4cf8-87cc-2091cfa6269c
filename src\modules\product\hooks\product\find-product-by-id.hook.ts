import { queryOptions, useQuery } from "@tanstack/react-query";
import { productFindByIdRequest } from "../../api/requests/product/find-by-id";

export const createProductQueryById = ({ id }: { id: number }) =>
	queryOptions({
		queryKey: ["product-find-by-id", { id }],
		queryFn: () => productFindByIdRequest({ id }),
		refetchOnWindowFocus: true,
	});

export const useFindProductById = (id: number) => {
	const queryOptions = createProductQueryById({ id });
	const { data, isLoading, isError } = useQuery(queryOptions);
	return { data, isLoading, isError };
};
