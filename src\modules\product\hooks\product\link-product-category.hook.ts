import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productLinkCategoryRequest } from "../../api/requests/product/link-category";

export const useLinkProductCategory = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async ({ productId, categoryId }: { productId: number; categoryId: number }) => {
			toast.loading("Linkando categoria...");
			const response = await productLinkCategoryRequest({ id: productId, categoryId });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: (_, variables) => {
			toast.dismiss();
			toast.success("Categoria linkada com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-find-by-id", { id: variables.productId }] });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const linkProductCategory = (productId: number, categoryId: number) => {
		mutation.mutate({ productId, categoryId });
	};

	return { linkProductCategory, isLoading: mutation.isPending };
};
