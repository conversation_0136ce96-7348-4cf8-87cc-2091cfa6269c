import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productRemoveCategoryRequest } from "../../api/requests/product/remove-category";

export const useRemoveProductCategory = () => {
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationFn: async ({ productId, categoryId }: { productId: number; categoryId: number }) => {
			toast.loading("Removendo categoria...");
			const response = await productRemoveCategoryRequest({ productId, categoryId });
			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: (_, variables) => {
			toast.dismiss();
			toast.success("Categoria removida com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-find-by-id", { id: variables.productId }] });
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	const removeProductCategory = (productId: number, categoryId: number) => {
		mutation.mutate({ productId, categoryId });
	};

	return { removeProductCategory, isLoading: mutation.isPending };
};
