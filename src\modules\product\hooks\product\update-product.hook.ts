import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { productUpdateRequest } from "../../api/requests/product/update";
import { IUpdateProductDto } from "../../dtos/product/update-product.dto";

export const useUpdateProduct = () => {
	const queryClient = useQueryClient();

	const updateProductMutation = useMutation({
		mutationKey: ["update-product"],
		mutationFn: async ({ productId, items }: { productId: number; items: IUpdateProductDto }) => {
			const response = await productUpdateRequest({ productId, items });

			if (response.success) return response.data;

			throw new Error(response.data.message);
		},
		onSuccess: () => {
			toast.dismiss();
			toast.success("Produto atualizado com sucesso!");
			queryClient.invalidateQueries({ queryKey: ["product-find-all"], exact: false });
		},
		onError: error => {
			toast.dismiss();
			toast.error(error.message || "Erro ao atualizar produto.");
		},
	});

	return { updateProductMutation };
};
