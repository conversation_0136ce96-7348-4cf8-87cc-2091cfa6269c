import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { productFindAllFiltersSchema, type IProductFindAllFilters } from "../validators/product/product-find-all-filters.validator";

export const useProductFilters = () => {
	const methods = useForm<IProductFindAllFilters>({
		resolver: zodResolver(productFindAllFiltersSchema),
		defaultValues: {
			filter: "",
		},
	});

	const { watch } = methods;
	const filter = watch("filter");
	const debouncedFilter = useDebounce(filter, 500);

	return {
		methods,
		debouncedFilter,
	};
};
