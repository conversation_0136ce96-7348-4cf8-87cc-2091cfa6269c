import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { Home, PackagePlus } from "lucide-react";
import { AddProduct } from "../components/product-list/add-product";
import { PRODUCT_CONFIG } from "../data/product-config";

const AddProductPage = () => {
	return (
		<MainSkeletonContainer
			pageTitle="Adicionar produto"
			iconTitle={PackagePlus}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{ href: "/produtos", label: "Produtos", icon: PRODUCT_CONFIG.Icon },
			]}
			currentBreadcrumb={{
				href: `/produtos/adicionar`,
				label: "Adicionar produto",
				icon: PackagePlus,
			}}
		>
			<AddProduct />
		</MainSkeletonContainer>
	);
};

export default AddProductPage;
