import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { Home } from "lucide-react";
import { FiltersSection } from "../components/category/filters/filters-section";
import { TableCategories } from "../components/category/table/table-categories";
import { PRODUCT_CONFIG, PRODUCT_SUBITEM_IDS } from "../data/product-config";
import { useCategoryFilters } from "../hooks/category/use-category-filters.hook";

const CategoryPage = () => {
	const methods = useCategoryFilters();
	const { page, itemsPerPage, handleItemsPerPageChange, handlePageChange } = usePagination();
	const categoryItem = PRODUCT_CONFIG.subItems?.find(item => item.id === PRODUCT_SUBITEM_IDS.CATEGORIES);

	return (
		<MainSkeletonContainer
			pageTitle="Categorias"
			iconTitle={categoryItem?.Icon}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{ href: "/produtos", label: "Produtos", icon: PRODUCT_CONFIG.Icon },
			]}
			currentBreadcrumb={{
				href: `/produtos/categorias`,
				label: `Categorias`,
				icon: categoryItem?.Icon,
			}}
		>
			<FiltersSection methods={methods.methods} />
			<TableCategories
				filter={methods.debouncedFilter}
				page={page}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
				onItemsPerPageChange={handleItemsPerPageChange}
			/>
		</MainSkeletonContainer>
	);
};

export default CategoryPage;
