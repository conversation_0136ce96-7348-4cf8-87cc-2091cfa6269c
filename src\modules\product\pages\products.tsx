import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { Home, List } from "lucide-react";
import { FiltersSection } from "../components/product-list/filters/filters-section";
import { TableProducts } from "../components/product-list/table-products";
import { useProductFilters } from "../hooks/use-product-filters.hook";

export const ProductsPage = () => {
	const methods = useProductFilters();
	const { page, itemsPerPage, handleItemsPerPageChange, handlePageChange } = usePagination();

	return (
		<MainSkeletonContainer
			iconTitle={List}
			itemsBreadcrumb={[{ href: "/", label: "Página inicial", icon: Home }]}
			currentBreadcrumb={{ href: "/produtos", label: "Produtos", icon: List }}
			pageTitle="Lista de produtos"
		>
			<FiltersSection methods={methods.methods} />
			<TableProducts
				filter={methods.debouncedFilter}
				page={page}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
				onItemsPerPageChange={handleItemsPerPageChange}
			/>
		</MainSkeletonContainer>
	);
};
