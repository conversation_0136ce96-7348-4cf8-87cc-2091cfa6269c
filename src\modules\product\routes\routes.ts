import { PRODUCT_CONFIG, PRODUCT_SUBITEM_IDS } from "../data/product-config";
import CategoryPage from "../pages/categories";
import { ProductsPage } from "../pages/products";

import { home } from "@/shared/routes/dashboard.route";
import { ModuleSubRoute, createModuleRoutes } from "@/shared/routes/module-routes.factory";
import AddProductPage from "../pages/add-product";
import EditProductPage from "../pages/product-edit";

const productSubRoutes: ModuleSubRoute<NonNullable<typeof PRODUCT_CONFIG.subItems>[number]>[] = [
	{ id: PRODUCT_SUBITEM_IDS.ALL_PRODUCTS, component: ProductsPage },
	{ id: PRODUCT_SUBITEM_IDS.CATEGORIES, component: CategoryPage },
	{ id: PRODUCT_SUBITEM_IDS.ADD_PRODUCT, component: AddProductPage },
	{ id: PRODUCT_SUBITEM_IDS.EDIT_PRODUCT, component: EditProductPage },
];

export const productRoutes = createModuleRoutes(
	{
		...PRODUCT_CONFIG,
		subItems: PRODUCT_CONFIG.subItems ?? [],
	},
	productSubRoutes,
	() => home
);
