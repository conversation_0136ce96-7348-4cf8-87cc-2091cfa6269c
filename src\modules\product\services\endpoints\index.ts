export const PRODUCT_ROUTES = {
	FIND_ALL: ({ page, limit, filter }: { page: number; limit: number; filter: string }) => `/product/find-all/${page}/${limit}?filter=${filter}`,
	CREATE: "/product/create",
	UPDATE: ({ id }: { id: number }) => `/product/update/${id}`,
	DELETE: ({ id }: { id: number }) => `/product/delete/${id}`,
	FIND_BY_ID: ({ id }: { id: number }) => `/product/find-by-id/${id}`,
	REMOVE_CATEGORY_WITH_PRODUCT: ({ productId, categoryId }: { productId: number; categoryId: number }) =>
		`/product/${productId}/remove-category/${categoryId}`,
} as const;

export const PRODUCT_CATEGORY_ROUTES = {
	FIND_ALL: ({ page, limit }: { page: number; limit: number }) => `/product/category/find-all/${page}/${limit}`,
	CREATE: "/product/category/create",
	DELETE: ({ id }: { id: number }) => `/product/category/delete/${id}`,
} as const;
