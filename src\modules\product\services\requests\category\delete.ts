import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_CATEGORY_ROUTES } from "../../endpoints";

export const categoryDeleteRequest = async ({ id }: { id: number }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const response = await createRequest<IGlobalMessageReturn>({
		path: `${PRODUCT_CATEGORY_ROUTES.DELETE({ id })}`,
		method: "DELETE",
	});
	return response;
};
