import { IProductListDTO } from "@/modules/product/dtos/product/get-all-products.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_CATEGORY_ROUTES } from "../../endpoints";

export const productCategoryFindAllRequest = async ({ page, limit }: { page: number; limit: number }): Promise<ApiResponse<IProductListDTO[]>> => {
	const response = await createRequest<IProductListDTO[]>({
		path: `${PRODUCT_CATEGORY_ROUTES.FIND_ALL({ page, limit })}`,
		method: "GET",
	});
	return response;
};
