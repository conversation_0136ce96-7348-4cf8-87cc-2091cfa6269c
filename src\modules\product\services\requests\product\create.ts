import { ICreateProductDto } from "@/modules/product/dtos/product/create-product.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productCreateRequest = async ({ ...items }: ICreateProductDto): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const response = await createRequest<IGlobalMessageReturn>({
		path: `${PRODUCT_ROUTES.CREATE}`,
		method: "POST",
		body: items,
	});
	return response;
};
