import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productRemoveCategoryRequest = async ({
	productId,
	categoryId,
}: {
	productId: number;
	categoryId: number;
}): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const response = await createRequest<IGlobalMessageReturn>({
		path: `${PRODUCT_ROUTES.REMOVE_CATEGORY_WITH_PRODUCT({ productId, categoryId })}`,
		method: "DELETE",
	});
	return response;
};
