import { z } from "zod";

const packageSchema = z.object({
	name: z.string().min(3, "O nome deve ter no mínimo 3 caracteres"),
	barcode: z.string().min(8, "O código de barras deve ter no mínimo 8 caracteres"),
	code: z.string().min(1, "O código é obrigatório"),
	quantityPerPackage: z.number().min(1, "A quantidade por pacote deve ser maior que zero"),
});

export const productFormSchema = z.object({
	name: z.string().min(3, "O nome deve ter no mínimo 3 caracteres").max(100, "O nome deve ter no máximo 100 caracteres"),
	barcode: z.string().min(8, "O código de barras deve ter no mínimo 8 caracteres"),
	ncm: z.string().regex(/^\d+$/, "O NCM deve conter apenas números"),
	description: z
		.string()
		.optional()
		.nullable()
		.transform(val => val || undefined),
	categoryId: z.string().min(1, "A categoria é obrigatória"),
	images: z.array(z.instanceof(File)).max(5, "Máximo de 5 imagens permitidas").optional().default([]),
	supplierId: z.string().min(1, "O fornecedor é obrigatório"),
	code: z
		.string()
		.optional()
		.nullable()
		.transform(val => val || undefined),
	packages: z.array(packageSchema).optional().default([]),
	costPrice: z.number().min(0.01, "O preço de custo deve ser maior que zero"),
	price: z.number().min(0.01, "O preço de venda deve ser maior que zero"),
	marginPercentage: z
		.number()
		.optional()
		.nullable()
		.transform(val => val ?? undefined),
});

export type ProductFormData = z.infer<typeof productFormSchema>;
