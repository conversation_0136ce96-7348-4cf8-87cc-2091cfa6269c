import { ConsultPendingFormData } from "@/modules/sales-panel/validators/consult-pending.validator";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { UseFormReturn } from "react-hook-form";

interface FilterFormProps {
	form: UseFormReturn<ConsultPendingFormData>;
}

export const FilterForm = ({ form }: FilterFormProps) => {
	const { register } = form;

	return (
		<form className="flex flex-wrap gap-2">
			<div className="flex-1 w-full lg:w-auto lg:min-w-[200px] relative">
				<Label className="absolute top-[-5px] bg-white px-3 left-3">Cliente</Label>
				<Input
					{...register("customer")}
					placeholder="Ex: <PERSON>"
					className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050]"
				/>
			</div>
			<div className="lg:min-w-[150px] lg:w-auto w-full relative">
				<Label className="absolute top-[-5px] bg-white px-3 left-3">Nº do Pedido</Label>
				<Input
					{...register("orderId")}
					placeholder="Ex: 123456"
					className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050]"
				/>
			</div>
		</form>
	);
};
