import { AnimatePresence, motion } from "framer-motion";
import { LucideShoppingCart } from "lucide-react";

interface PendingOrder {
	id: number;
	customer: string;
	createdAt: string;
	total: string;
	status: string;
}

interface PendingOrdersListProps {
	orders: PendingOrder[];
	currentOrderId: number | null;
	onSelectOrder: (order: PendingOrder) => void;
	isLoading: boolean;
}

export const PendingOrdersList = ({ orders, currentOrderId, onSelectOrder, isLoading }: PendingOrdersListProps) => {
	if (isLoading) {
		return (
			<div className="flex items-center justify-center h-full">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-600"></div>
			</div>
		);
	}

	if (!orders.length) {
		return (
			<div className="flex flex-col items-center justify-center h-full text-gray-500">
				<LucideShoppingCart size={48} className="text-gray-300 mb-2" />
				<p>Nenhum pedido pendente encontrado. 😊</p>
			</div>
		);
	}

	return (
		<AnimatePresence mode="wait">
			<div className="flex flex-col">
				{orders.map((order, index) => (
					<motion.div
						key={order.id}
						className="relative pt-3 pr-3"
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -10 }}
						transition={{ duration: 0.15, delay: index * 0.05 }}
					>
						<div
							onClick={() => onSelectOrder(order)}
							className={`border-2 ${
								currentOrderId === order.id ? "border-green-500 bg-green-50" : "border-mainColor"
							} rounded-[15px] p-5 bg-white shadow-sm hover:shadow-lg hover:border-mainColor transition-all duration-200 mb-4 grid grid-cols-1 md:grid-cols-2 gap-4 cursor-pointer`}
						>
							{currentOrderId === order.id && (
								<div className="absolute top-0 right-0 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-md z-50">
									ATUAL
								</div>
							)}
							<div className="flex flex-col gap-3">
								<div className="flex items-center gap-2">
									<span className="text-gray-600 font-medium">Pedido:</span>
									<span className="font-bold text-mainColor text-lg">#{order.id}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-gray-600 font-medium">Cliente:</span>
									<span className="text-gray-700 font-medium">{order.customer}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="text-gray-600 font-medium">Data:</span>
									<span className="text-gray-500">{order.createdAt}</span>
								</div>
							</div>
							<div className="flex flex-col md:items-end gap-3 justify-center">
								<div className="flex items-center gap-2">
									<span className="text-gray-600 font-medium">Total:</span>
									<span className="font-bold text-cyan-600 text-lg">R$ {order.total}</span>
								</div>
								<span className="px-4 py-2 rounded-full bg-mainColor/10 text-mainColor font-medium text-sm inline-flex items-center justify-center">
									{order.status}
								</span>
							</div>
						</div>
					</motion.div>
				))}
			</div>
		</AnimatePresence>
	);
};
