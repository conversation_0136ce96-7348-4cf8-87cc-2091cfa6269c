import { useCancelOrder } from "@/modules/sales-panel/hooks/order/cancel-order.hook";
import { useCreateOrder } from "@/modules/sales-panel/hooks/order/use-create-order.hook";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { Button } from "@/shared/components/ui/button";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { Link } from "@tanstack/react-router";
import { useAtomValue } from "jotai";
import { ArrowLeft, Plus, User, X } from "lucide-react";
import { PendingOrderConsultButton } from "../consult-pending/modal/button";
import { IdentifyPersonButton } from "../person/modal/button";
import { LinkCpf } from "./link-cpf";

export const TerminalHeaderDesktop = ({
	createOrderMutation,
	orderId,
	cancelOrderMutation,
}: {
	createOrderMutation: ReturnType<typeof useCreateOrder>["createOrderMutation"];
	cancelOrderMutation: ReturnType<typeof useCancelOrder>["cancelOrderMutation"];
	orderId: number | null;
}) => {
	const orderInfo = useAtomValue(orderInfoAtom);

	useKeyboardShortcut({
		combination: { key: "c" },
		handler: () => {
			if (orderId) {
				cancelOrderMutation.mutate();
			}
		},
		options: {
			ignoreInputs: false,
			preventDefault: true,
			disabled: !orderId,
		},
	});

	useKeyboardShortcut({
		combination: { key: "a" },
		handler: () => {
			if (!orderId) {
				createOrderMutation.mutate();
			}
		},
		options: {
			ignoreInputs: false,
			preventDefault: true,
			disabled: !!orderId,
		},
	});

	return (
		<header className="w-full z-50  top-0 px-2 py-4 hidden lg:flex justify-between items-center bg-white shadow-main rounded-[20px]">
			<div className="flex  w-full xl:flex-row gap-2  flex-col  justify-between items-center xl:gap-2 h-full px-4">
				<div className="flex w-full   xl:w-1/4 items-center gap-2">
					<Button className="bg-mainColor  shadow-[0_4px_4px_0_rgba(0,0,0,0.25)] gap-2 flex text-white  px-3 py-1 rounded-[15px]">
						<Link to="/" className="flex items-center gap-2">
							<ArrowLeft size={18} />
							Sair
						</Link>
					</Button>
					{orderId && (orderInfo?.customer || orderInfo?.cpfCnpj) ? (
						<div className="flex flex-col">
							<div className="flex items-center gap-2">
								<h2 className="font-regular text-xl text-[#505050] whitespace-nowrap">Pedido #{orderId}</h2>
								<span className="bg-mainColor/15 border border-mainColor text-mainColor px-3 py-1 rounded-[10px]">Em aberto</span>
							</div>

							<div className="flex items-center gap-2 mt-1">
								<User size={16} className="text-mainColor" />
								{orderInfo?.customer && <span className="text-sm text-gray-600">{orderInfo.customer}</span>}
								{orderInfo?.cpfCnpj && <span className="text-sm text-gray-500">({orderInfo.cpfCnpj})</span>}
							</div>
						</div>
					) : (
						<h2 className="font-regular  text-sm sm:text-base 2xl:text-lg text-[#505050]">Nenhum pedido em aberto</h2>
					)}
				</div>
				<div className="md:flex  hidden w-full xl:w-3/4 justify-between  items-center xl:gap-1 2xl:gap-3 xl:justify-end">
					{!orderId && (
						<Button
							onClick={() => createOrderMutation.mutate()}
							className="
    bg-gradient-to-r from-[#227989] to-[#2fa2b5]
    shadow-main
    flex items-center gap-1 2xl:gap-2
    text-white font-semibold
    2xl:px-4 2xl:py-2
    rounded-[15px]
    transition-all duration-300
    hover:scale-105 hover:shadow-lg
    active:scale-100
  "
						>
							<Plus size={18} />
							Adicionar venda
							<span className="text-xs bg-white text-[#227989] px-1 rounded">A</span>
						</Button>
					)}

					<IdentifyPersonButton orderId={orderId} data-testid="identify-person-button" />
					<LinkCpf orderId={orderId!} data-testid="link-cpf-button" />
					<PendingOrderConsultButton data-testid="consult-pending-button" />
					<Button
						disabled={!orderId}
						onClick={() => cancelOrderMutation.mutate()}
						className="bg-[#D47070] shadow-main px-2 gap-1 2xl:gap-2 flex text-white  2xl:px-3 2xl:py-1 rounded-[15px]"
					>
						<X size={18} />
						Cancelar venda
						<span className="text-xs bg-white text-red-500 px-1 rounded">C</span>
					</Button>
				</div>
			</div>
		</header>
	);
};
