import { useCpfModal } from "@/modules/sales-panel/hooks/modal-cpf/cpf-modal.hook";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";

interface IModalCpfProps {
	orderId: number;
}

export const ModalCpf = ({ orderId }: IModalCpfProps) => {
	const { closeModal, handleSubmit, cpf, modalCpf, handleCpfChange } = useCpfModal(orderId);
	if (!modalCpf) return null;

	const titleMessage = modalCpf.type === "add" ? "CPF na nota" : "Nova venda identificada";
	const descriptionMessage = modalCpf.type === "auto" ? "Deseja vincular CPF na nota?" : "Informe o CPF para vincular à nota fiscal";

	return (
		<OverlayContainer isVisible={modalCpf.isOpen} onClose={closeModal}>
			<div className="bg-white p-4 rounded-[15px] shadow-md max-w-[400px] w-full" onClick={e => e.stopPropagation()}>
				<h2 className="text-xl  text-[#505050] mb-2 font-semibold">{titleMessage}</h2>
				<p className="mb-4 text-sm text-gray-600">{descriptionMessage}</p>

				<form onSubmit={handleSubmit} className="flex flex-col gap-3">
					<div className="relative">
						<Label className="absolute top-[-5px] bg-white px-3 left-3">CPF/CNPJ</Label>

						<Input
							value={cpf}
							onChange={handleCpfChange}
							placeholder="CPF/CNPJ"
							inputMode="numeric"
							className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050]"
						/>
					</div>

					<div className="flex justify-end gap-2 mt-2">
						<button
							type="button"
							onClick={closeModal}
							className="bg-gray-200 hover:bg-gray-300  h-[45px] shadow-main gap-2 flex text-black px-3 py-1 items-center rounded-[15px]"
						>
							Cancelar
						</button>
						<button
							type="submit"
							className="bg-mainColor shadow-main  h-[45px] gap-2 flex text-white px-3 py-1 items-center  rounded-[15px]"
						>
							Vincular
						</button>
					</div>
				</form>
			</div>
		</OverlayContainer>
	);
};
