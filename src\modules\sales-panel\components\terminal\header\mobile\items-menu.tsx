import { useCancelOrder } from "@/modules/sales-panel/hooks/order/cancel-order.hook";
import { openModalCpfAtom } from "@/modules/sales-panel/states/cpf-modal-open.state";
import { Button } from "@/shared/components/ui/button";
import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { useSetAtom } from "jotai";
import { FileClock, FileText, Plus, User, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { ConsultPendingModal } from "../../consult-pending/modal";
import { ModalCpf } from "../link-cpf/modal";

const itemVariants = {
	initial: { opacity: 0, y: 20, scale: 0.9 },
	animate: {
		opacity: 1,
		y: 0,
		scale: 1,
		transition: { type: "spring", stiffness: 400, damping: 20 },
	},
	exit: {
		opacity: 0,
		y: 20,
		scale: 0.9,
		transition: { type: "spring", stiffness: 400, damping: 20 },
	},
};

const menuButtonClass = "flex items-center gap-2 shadow-main text-white h-[48px] px-4 py-2 rounded-[15px]";

export const ItemsMenuMobile = ({
	orderId,
	cancelOrderMutation,
}: {
	orderId: number | null;
	cancelOrderMutation: ReturnType<typeof useCancelOrder>["cancelOrderMutation"];
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const setOpenModalCpf = useSetAtom(openModalCpfAtom);
	const [openModalPending, setOpenModalPending] = useState(false);
	const menuRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
				setIsOpen(false);
			}
		}

		if (isOpen) {
			document.addEventListener("mousedown", handleClickOutside);
		} else {
			document.removeEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isOpen]);

	const handleItemClick = (action?: () => void) => {
		if (action) {
			action();
		}
		setIsOpen(false);
	};

	return (
		<div ref={menuRef} className="fixed bottom-6 right-6 flex flex-col items-end gap-3 z-50 lg:hidden">
			<AnimatePresence>
				{isOpen && (
					<motion.div
						className="flex flex-col items-end gap-3 mb-2"
						initial="initial"
						animate="animate"
						exit="exit"
						variants={{
							animate: { transition: { staggerChildren: 0.1, staggerDirection: -1 } },
							exit: { transition: { staggerChildren: 0.05 } },
						}}
					>
						{[
							{
								label: "Identificar cliente",
								icon: <User size={18} />,
								action: () => {},
								bg: "bg-mainColor",
								disabled: !orderId,
							},
							{
								label: "Consultar pendentes",
								icon: <FileClock size={18} />,
								bg: "bg-mainColor",
								action: () => setOpenModalPending(true),
								disabled: false,
							},

							{
								label: "CPF na nota",
								icon: <FileText size={18} />,
								bg: "bg-mainColor",
								action: () => setOpenModalCpf({ isOpen: true, type: "add" }),
								disabled: !orderId,
							},
							{
								label: "Cancelar venda",
								icon: <X size={18} />,
								bg: "bg-[#D47070]",
								action: () => cancelOrderMutation.mutate(),
								disabled: !orderId,
							},
						]
							.filter(btn => !btn.disabled)
							.map(btn => (
								<motion.div key={btn.label} variants={itemVariants}>
									<Button
										onClick={() => {
											handleItemClick(btn.action);
										}}
										className={cn(btn.bg, menuButtonClass)}
									>
										{btn.icon}
										{btn.label}
									</Button>
								</motion.div>
							))}
					</motion.div>
				)}
			</AnimatePresence>
			<Button
				onClick={() => setIsOpen(prev => !prev)}
				className={cn("bg-mainColor shadow-xl rounded-[15px] w-14 h-14 flex items-center justify-center transition-transform")}
			>
				{isOpen ? <X size={26} className="text-white" /> : <Plus size={26} className="text-white" />}
			</Button>

			<ModalCpf orderId={orderId ?? 0} />
			<ConsultPendingModal isOpen={openModalPending} onClose={() => setOpenModalPending(false)} />
		</div>
	);
};
