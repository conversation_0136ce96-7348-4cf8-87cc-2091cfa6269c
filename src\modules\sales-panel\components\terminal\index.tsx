import { ItemsContainer } from "./items";
import { PaymentContainer } from "./payment";
import { ResumeCard } from "./resume";

export const TerminalGrid: React.FC = () => (
	<div className="flex flex-col lg:max-h-[calc(100vh-120px)] flex-1 lg:flex-row mb-20 sm:mb-0 w-full gap-4 ">
		<div className="md:flex-[2]">
			<ItemsContainer className="h-full max-h-full" />
		</div>
		<div className="flex flex-col gap-4 md:flex-[1] min-h-0">
			<PaymentContainer className="flex-[3] min-h-0" />
			<ResumeCard />
		</div>
	</div>
);
