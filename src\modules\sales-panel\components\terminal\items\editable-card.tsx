import { IItemSocket } from "@/modules/sales-panel/dto/order-socket.dto";
import { useUpdateItemForm } from "@/modules/sales-panel/hooks/items/update-item-form.hook";
import { useUpdateItem } from "@/modules/sales-panel/hooks/items/update-item-mutation.hook";
import { IUpdateItemForm } from "@/modules/sales-panel/validators/update-item.form";
import { Button } from "@/shared/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/components/ui/dropdown-menu";
import { Input } from "@/shared/components/ui/input";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import { FiEdit, FiSave } from "react-icons/fi";
import { MdOutlineCancel } from "react-icons/md";
import { NumericFormat } from "react-number-format";
import { RemoveItem } from "./remove-item";

const itemVariants = {
	hidden: { opacity: 0, y: 10 },
	visible: { opacity: 1, y: 0 },
	exit: { opacity: 0, y: -10 },
};

interface EditableItemCardProps {
	item: IItemSocket;
	orderId: number;
}

export function EditableItemCard({ item, orderId }: EditableItemCardProps) {
	const [isEditing, setIsEditing] = useState(false);
	const updateForm = useUpdateItemForm();
	const { errors } = updateForm.formState;
	const updateItem = useUpdateItem();
	const discountType = updateForm.watch("discountType", "value");
	const discountValue = updateForm.watch("discount", undefined);
	const [previewValue, setPreviewValue] = useState<number | null>(null);

	useEffect(() => {
		updateForm.reset({
			quantity: item.quantity,
			discount: item.discount,
			discountType: item.discountType || "value",
		});
	}, [item, isEditing, updateForm]);

	const parsePrice = (price: number) => {
		return price;
	};

	useEffect(() => {
		if (discountValue !== undefined && discountValue !== null) {
			const price = parsePrice(item.price);
			const preview = discountType === "percentage" ? (price * discountValue) / 100 : discountValue;
			setPreviewValue(preview);
		} else {
			setPreviewValue(null);
		}
	}, [discountValue, discountType, item.price]);

	const onSave = (data: IUpdateItemForm) => {
		try {
			const price = parsePrice(item.price);
			const finalDiscount = data.discountType === "percentage" ? (price * (data.discount || 0)) / 100 : data.discount || 0;

			updateItem.updateItemMutation.mutate({
				orderId,
				item: {
					id: item.id,
					quantity: data.quantity,
					discount: finalDiscount,
				},
			});
			setIsEditing(false);
		} catch (error) {
			console.error("Erro ao atualizar item:", error);
		}
	};

	const handleDiscountTypeChange = (type: "value" | "percentage") => {
		updateForm.setValue("discountType", type);
		updateForm.setValue("discount", 0);
	};

	return (
		<motion.div
			variants={itemVariants}
			initial="hidden"
			animate="visible"
			exit="exit"
			layout
			className="
				relative
				border border-mainColor 
				rounded-[10px] 
				p-4 
				bg-white
				shadow-sm
				hover:shadow-md
				transition-shadow
				mb-3
			"
		>
			<form onSubmit={updateForm.handleSubmit(onSave)}>
				<div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 lg:gap-4">
					<div className="p-2 lg:p-4 bg-gray-50 shadow-sm rounded-lg w-full space-y-3">
						<div className="flex justify-between items-center">
							<h2 className="font-bold text-lg lg:text-xl text-[#2F2F2F]">{item.product}</h2>
							<span className="text-sm font-medium text-gray-600">Preço: {item.price}</span>
						</div>

						<div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
							<div className="space-y-1">
								<label className="text-sm text-gray-600 font-medium">Quantidade</label>
								{isEditing ? (
									<div>
										<Input
											{...updateForm.register("quantity")}
											className="border-2 px-2 w-full h-10 text-gray-700 rounded-[8px] placeholder-gray-400"
										/>
										{errors.quantity && <p className="text-red-600 text-sm mt-1">{errors.quantity.message}</p>}
									</div>
								) : (
									<span className="text-sm text-gray-800 block h-10 leading-10">{item.quantity}</span>
								)}
							</div>

							<div className="space-y-1">
								<label className="text-sm text-gray-600 font-medium">Desconto</label>
								{isEditing ? (
									<div className="relative">
										<Controller
											control={updateForm.control}
											name="discount"
											render={({ field }) => (
												<>
													<div className="relative">
														<NumericFormat
															value={field.value ?? ""}
															thousandSeparator="."
															decimalSeparator=","
															decimalScale={2}
															prefix={discountType === "value" ? "R$ " : ""}
															suffix={discountType === "percentage" ? " %" : ""}
															className="w-full h-10 border-2 px-2 text-gray-700 rounded-[8px] placeholder-gray-400 pr-12"
															onValueChange={values => field.onChange(values.floatValue)}
														/>
														<DropdownMenu>
															<DropdownMenuTrigger asChild>
																<button
																	type="button"
																	className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 border-none bg-transparent hover:bg-transparent focus:ring-0 flex items-center justify-center text-gray-500 hover:text-gray-700"
																>
																	{discountType === "value" ? "R$" : "%"}
																	<ChevronDown size={14} className="ml-0.5" />
																</button>
															</DropdownMenuTrigger>
															<DropdownMenuContent align="end" className="w-20 bg-white p-0 z-[9999]">
																<DropdownMenuItem
																	className="flex items-center hover:bg-gray-100 gap-2 px-3 py-2 cursor-pointer"
																	onClick={() => handleDiscountTypeChange("value")}
																>
																	<span className="text-sm">R$</span>
																</DropdownMenuItem>
																<DropdownMenuItem
																	className="flex items-center hover:bg-gray-100 gap-2 px-3 py-2 cursor-pointer"
																	onClick={() => handleDiscountTypeChange("percentage")}
																>
																	<span className="text-sm">%</span>
																</DropdownMenuItem>
															</DropdownMenuContent>
														</DropdownMenu>
													</div>
													{previewValue !== null && !isNaN(previewValue) && (
														<p className="text-sm text-gray-500 mt-1">
															{discountType === "percentage" ? (
																<>Valor: R$ {previewValue.toFixed(2).replace(".", ",")}</>
															) : (
																<>
																	Porcentagem:{" "}
																	{previewValue > 0
																		? ((previewValue / parsePrice(item.price)) * 100).toFixed(2).replace(".", ",")
																		: "0,00"}
																	%
																</>
															)}
														</p>
													)}
												</>
											)}
										/>
									</div>
								) : (
									<span className="text-sm text-gray-800 block h-10 leading-10">
										{item.discountType === "percentage" ? `${item.discount}%` : `R$ ${item.discount}`}
									</span>
								)}
							</div>
						</div>

						<div className="flex justify-between items-center pt-2 border-t border-gray-200">
							<span className="text-sm text-gray-600 font-medium">Total:</span>
							<span className="text-sm font-medium text-gray-800">{item.total}</span>
						</div>
					</div>

					<div className="flex flex-col w-full lg:w-auto items-end justify-end md:items-center gap-2">
						{isEditing ? (
							<div className="flex lg:flex-col items-end justify-end w-full lg:items-center gap-2">
								<Button
									type="submit"
									variant="outline"
									size="icon"
									className="border border-mainColor text-mainColor hover:bg-mainColor hover:text-white rounded-[15px] h-9 w-9 flex items-center justify-center transition-colors"
								>
									<FiSave className="text-lg" />
								</Button>

								<Button
									variant="outline"
									size="icon"
									className="border border-red-600 text-red-600 hover:bg-red-600 hover:text-white rounded-[15px] h-9 w-9 flex items-center justify-center transition-colors"
									onClick={() => setIsEditing(false)}
								>
									<MdOutlineCancel className="text-lg" />
								</Button>
							</div>
						) : (
							<div className="flex lg:flex-col items-end justify-end w-full lg:items-center gap-2">
								<Button
									type="button"
									variant="outline"
									size="icon"
									onClick={e => {
										e.preventDefault();
										setIsEditing(true);
									}}
									className="border border-mainColor text-mainColor hover:bg-mainColor hover:text-white rounded-[15px] h-9 w-9 flex items-center justify-center transition-colors"
								>
									<FiEdit className="text-lg" />
								</Button>

								<RemoveItem orderId={orderId} itemId={item.id} />
							</div>
						)}
					</div>
				</div>
			</form>
		</motion.div>
	);
}
