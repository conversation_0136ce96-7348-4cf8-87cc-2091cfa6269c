import { useBarcodeForm } from "@/modules/sales-panel/hooks/items/barcoder-form.hook";
import { useCreateOrder } from "@/modules/sales-panel/hooks/order/use-create-order.hook";
import { useAddProduct } from "@/modules/sales-panel/hooks/product/use-add-product.hook";
import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { ShoppingBag } from "lucide-react";
import { Controller } from "react-hook-form";
import { toast } from "sonner";
import { containerVariants } from "../payment/animate-variants";
import { TitleWithLine } from "../title";
import { EditableItemCard } from "./editable-card";
import { ProductSearchInput } from "./search-input";

interface ItemsProps {
	className?: string;
}

export const ItemsContainer = ({ className = "" }: ItemsProps) => {
	const orderId = useAtomValue(orderIdAtom);
	const orderInfo = useAtomValue(orderInfoAtom);
	const methods = useBarcodeForm();
	const { createOrderMutation } = useCreateOrder();
	const addProduct = useAddProduct();
	const items = orderInfo?.items ?? [];

	const addProductByBarcode = async (barcode: string, quantity: number = 1) => {
		let currentOrderId = orderId;
		if (!currentOrderId) {
			const newOrder = await createOrderMutation.mutateAsync();
			currentOrderId = newOrder?.data.id;
			if (!currentOrderId) {
				toast.error("Falha ao iniciar o pedido.");
				return;
			}
		}
		await addProduct.addProductMutation.mutateAsync({
			orderId: currentOrderId,
			item: { barcode, quantity },
		});
		methods.reset();
	};

	const onSubmit = async (data: { barcode: string; quantity?: number }) => {
		await addProductByBarcode(data.barcode, data.quantity ?? 1);
	};

	return (
		<div className={`w-full h-full flex flex-col gap-2 p-3 sm:p-4 md:p-6 bg-white min-h-[200px] rounded-[20px] shadow-main ${className}`}>
			<TitleWithLine title="Itens da venda" icon={ShoppingBag} />
			<div className="flex-1 flex flex-col gap-3 min-h-0">
				<header className="w-full flex items-center">
					<form className="w-full" onSubmit={methods.handleSubmit(onSubmit)}>
						<Controller
							control={methods.control}
							name="barcode"
							render={({ field }) => (
								<ProductSearchInput
									value={field.value}
									onChange={field.onChange}
									onSelect={product => addProductByBarcode(product.barcode)}
								/>
							)}
						/>
					</form>
				</header>
				<div className="w-full flex-1 p-1 sm:p-2 shadow-[inset_0_4px_4px_rgba(0,0,0,0.25)] bg-[#eeeeee] rounded-[15px] overflow-y-auto">
					{items.length > 0 ? (
						<motion.div variants={containerVariants} initial="hidden" animate="visible" className="space-y-2 sm:space-y-3">
							<AnimatePresence>
								{items.map(item => (
									<EditableItemCard key={item.id} item={item} orderId={orderId!} />
								))}
							</AnimatePresence>
						</motion.div>
					) : (
						<div className="flex flex-col items-center justify-center gap-3 sm:gap-5 h-full p-2 sm:p-0">
							<h1 className="text-xl sm:text-2xl md:text-[32px] mb-2 sm:mb-4 text-[#505050] text-center px-2">
								Os itens da venda aparecerão aqui!
							</h1>
							<svg
								className="w-[150px] h-auto sm:w-[200px] md:w-[239px]"
								viewBox="0 0 239 256"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
							>
								<g clipPath="url(#clip0_49_323)">
									<path
										fillRule="evenodd"
										clipRule="evenodd"
										d="M34.85 27.9795H50.91V160.762H34.85V27.9795ZM121.365 116.318C126.03 116.388 130.6 116.963 135 117.998V27.9795H146.475V121.878C151.365 124.073 155.95 126.873 160.115 130.177V27.9795H176.17V149.042C176.415 149.477 176.65 149.912 176.885 150.352C180.685 157.547 183.175 165.607 183.985 174.237C184.58 180.551 184.235 186.751 183.065 192.661C182.021 197.937 180.309 203.058 177.97 207.901L205.155 232.455C206.405 233.585 206.5 235.52 205.365 236.77L188.845 255C188.299 255.598 187.538 255.956 186.728 255.996C185.919 256.035 185.127 255.753 184.525 255.21L158.52 231.495C154.207 234.715 149.503 237.373 144.52 239.405C138.86 241.715 132.76 243.245 126.35 243.85C117.73 244.66 109.35 243.715 101.565 241.32C93.475 238.83 86.02 234.76 79.605 229.446C73.1285 224.091 67.7713 217.513 63.84 210.086C60.035 202.896 57.545 194.831 56.735 186.206C55.925 177.592 56.865 169.207 59.265 161.422C60.59 157.107 62.37 152.967 64.545 149.072V27.9795H69.425V141.617C69.98 140.887 70.55 140.167 71.135 139.462C74.605 135.267 78.615 131.512 83.06 128.312V27.9795H99.125V119.928C103.455 118.408 108.02 117.333 112.76 116.768V27.9795H121.365V116.318ZM152.89 140.932C142.44 132.277 129.085 128.167 115.58 129.437C95.085 131.362 77.7 145.457 71.595 165.222C69.6295 171.618 68.9447 178.34 69.58 185.001C70.845 198.501 77.39 210.861 87.835 219.506C92.955 223.746 98.905 226.996 105.355 228.981C111.575 230.895 118.27 231.645 125.145 231C132.01 230.355 138.45 228.366 144.2 225.321C150.126 222.184 155.376 217.911 159.65 212.746C163.89 207.626 167.14 201.676 169.125 195.226C171.04 189.006 171.79 182.311 171.145 175.437C170.526 168.775 168.597 162.301 165.47 156.387C162.331 150.46 158.057 145.208 152.89 140.932ZM4.63 0H47.45V9.25982H9.255V33.4493H0V4.62991C0 2.07496 2.07 0 4.63 0ZM9.255 155.292V179.481H47.45V188.741H4.63C2.07 188.741 0 186.666 0 184.111V155.292H9.255ZM191.55 0H234.37C236.925 0 239 2.07496 239 4.62991V33.4493H229.745V9.25982H191.55V0ZM239 155.292V184.111C239 186.666 236.925 188.741 234.37 188.741H191.55V179.481H229.745V155.292H239ZM189.81 27.9795H204.15V160.762H189.81V27.9795Z"
										fill="#505050"
									/>
								</g>
								<defs>
									<clipPath id="clip0_49_323">
										<rect width="239" height="256" fill="white" />
									</clipPath>
								</defs>
							</svg>
							<p className="text-sm sm:text-base md:text-[20px] max-w-md md:max-w-lg text-center text-[#505050] px-2">
								Para adicionar itens na venda, realize a leitura do código de barras do item ou adicione manualmente
							</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};
