import { useRemoveItem } from "@/modules/sales-panel/hooks/items/remove-item-mutation.hook";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/shared/components/ui/alert-dialog";
import { Button } from "@/shared/components/ui/button";
import { FiTrash2 } from "react-icons/fi";

export const RemoveItem = ({ orderId, itemId }: { orderId: number; itemId: number }) => {
	const { removeItemMutation } = useRemoveItem();

	return (
		<AlertDialog>
			<AlertDialogTrigger className="text-[#DB5353]">
				{" "}
				<Button
					variant="outline"
					size="icon"
					className="
                  border border-red-600 text-red-600
                  hover:bg-red-600 hover:text-white
                  rounded-[15px] h-9 w-9 flex items-center justify-center
                  transition-colors
                "
				>
					<FiTrash2 className="text-lg" />
				</Button>
			</AlertDialogTrigger>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>Você realmente deseja remover este item?</AlertDialogTitle>
					<AlertDialogDescription>Esta ação não pode ser desfeita. O item será removido do pedido.</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel>Cancelar</AlertDialogCancel>
					<AlertDialogAction onClick={() => removeItemMutation.mutate({ orderId, itemId })}>Confirmar</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
};
