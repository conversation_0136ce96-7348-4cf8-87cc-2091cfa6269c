import { productsFindAllRequest } from "@/modules/product/api/requests/product/find-all";
import { IProductListDTO } from "@/modules/product/dtos/product/get-all-products.dto";
import FullScreenModal from "@/shared/components/custom/full-screen";

import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import BarcodeScanner from "@/shared/components/utils/barcode-scanner";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { AnimatePresence, motion } from "framer-motion";
import { ScanLine, Search, X } from "lucide-react";
import { KeyboardEvent, useCallback, useEffect, useMemo, useRef, useState } from "react";

interface ProductSearchInputProps {
	value: string;
	onChange: (value: string) => void;
	onSelect: (product: IProductListDTO) => void;
}

export const ProductSearchInput = ({ value, onChange, onSelect }: ProductSearchInputProps) => {
	const [query, setQuery] = useState(value);
	const [results, setResults] = useState<IProductListDTO[]>([]);
	const [showResults, setShowResults] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [errorMsg, setErrorMsg] = useState<string | null>(null);
	const [selectedIndex, setSelectedIndex] = useState<number>(-1);
	const [scanning, setScanning] = useState(false);
	const containerRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLInputElement>(null);
	const cacheRef = useRef<Record<string, IProductListDTO[]>>({});

	const debouncedQuery = useDebounce(query, 300);

	useEffect(() => {
		if (!debouncedQuery) {
			setResults([]);
			setIsLoading(false);
			setErrorMsg(null);
			return;
		}

		const fetchProducts = async () => {
			setIsLoading(true);
			setErrorMsg(null);
			try {
				if (cacheRef.current[debouncedQuery]) {
					setResults(cacheRef.current[debouncedQuery]);
				} else {
					const response = await productsFindAllRequest({
						page: 1,
						limit: 10,
						filter: debouncedQuery,
					});
					if (response.success) {
						cacheRef.current[debouncedQuery] = response.data.data;
						setResults(response.data.data);
					} else {
						setErrorMsg("Erro na resposta do servidor.");
					}
				}
			} catch {
				setErrorMsg("Erro ao buscar produtos, tente novamente.");
			} finally {
				setIsLoading(false);
				setSelectedIndex(-1);
			}
		};

		fetchProducts();
	}, [debouncedQuery]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
				setShowResults(false);
			}
		};

		document.addEventListener("click", handleClickOutside);
		return () => document.removeEventListener("click", handleClickOutside);
	}, []);

	const handleGlobalKeyDown = useCallback((event: globalThis.KeyboardEvent) => {
		if (event.key === "Escape") {
			setShowResults(false);
			setSelectedIndex(-1);
		}
	}, []);

	useEffect(() => {
		document.addEventListener("keydown", handleGlobalKeyDown);
		return () => document.removeEventListener("keydown", handleGlobalKeyDown);
	}, [handleGlobalKeyDown]);

	const handleSelect = useCallback(
		(product: IProductListDTO) => {
			setQuery("");
			setResults([]);
			setShowResults(false);
			setSelectedIndex(-1);
			onSelect(product);
		},
		[onSelect]
	);

	const handleClear = useCallback(() => {
		setQuery("");
		setResults([]);
		onChange("");
		setSelectedIndex(-1);
	}, [onChange]);

	const handleKeyDown = useCallback(
		(event: KeyboardEvent<HTMLInputElement>) => {
			if (!showResults) return;
			if (event.key === "ArrowDown") {
				event.preventDefault();
				setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : prev));
			} else if (event.key === "ArrowUp") {
				event.preventDefault();
				setSelectedIndex(prev => (prev > 0 ? prev - 1 : prev));
			} else if (event.key === "Enter") {
				event.preventDefault();
				if (selectedIndex >= 0 && selectedIndex < results.length) {
					handleSelect(results[selectedIndex]);
				}
			}
		},
		[results, selectedIndex, showResults, handleSelect]
	);

	const highlightText = useCallback((text: string, highlight: string) => {
		if (!highlight) return text;
		const regex = new RegExp(`(${highlight.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`, "gi");
		const parts = text.split(regex);
		return parts.map((part, index) =>
			regex.test(part) ? (
				<mark key={index} className="bg-mainColor/30">
					{part}
				</mark>
			) : (
				part
			)
		);
	}, []);

	useEffect(() => {
		setQuery(value);
	}, [value]);

	const handleBarcodeDetected = useCallback(
		(barcode: string) => {
			setQuery(barcode);
			setScanning(false);
			onChange(barcode);
			setShowResults(true);
			if (results.length === 1) {
				handleSelect(results[0]);
			}
		},
		[onChange, handleSelect, results]
	);

	const activeDescendantId = useMemo(() => {
		return selectedIndex >= 0 && results[selectedIndex] ? `product-item-${results[selectedIndex].id}` : undefined;
	}, [selectedIndex, results]);

	return (
		<div ref={containerRef} className="relative w-full">
			<div className="relative flex items-center">
				<div className="relative flex-1">
					<Label className="absolute top-[-5px] bg-white px-3 left-3">Entrada de dados</Label>
					<Input
						ref={inputRef}
						value={query}
						onChange={e => {
							setQuery(e.target.value);
							setShowResults(true);
							onChange(e.target.value);
						}}
						onKeyDown={handleKeyDown}
						placeholder="Código de barras ou nome do produto..."
						className="w-full h-[45px] pl-4 pr-10 rounded-[15px] border-2 border-[#505050] focus:outline-none transition-all duration-300"
						aria-autocomplete="list"
						aria-controls="product-list"
						aria-expanded={showResults}
						aria-activedescendant={activeDescendantId}
					/>
					{query && (
						<button
							type="button"
							onClick={handleClear}
							className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
							aria-label="Limpar busca"
						>
							<X size={16} />
						</button>
					)}
					<Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
				</div>

				<button
					type="button"
					onClick={() => setScanning(true)}
					className="ml-2 p-2 text-gray-500 hover:text-gray-700 md:hidden" // Hidden on md and larger screens
					aria-label="Escanear código de barras"
				>
					<ScanLine size={24} />
				</button>
			</div>

			<AnimatePresence>
				{showResults && (
					<motion.div
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -10 }}
						className="absolute z-10 top-[60px] left-0 right-0 bg-white border border-mainColor shadow-lg rounded-[15px] max-h-[250px] overflow-y-auto transition-all duration-300"
						role="listbox"
						id="product-list"
					>
						{isLoading ? (
							<div className="px-4 py-2 text-gray-500">Carregando...</div>
						) : errorMsg ? (
							<div className="px-4 py-2 text-red-500">{errorMsg}</div>
						) : results.length > 0 ? (
							<ul className="divide-y divide-gray-200">
								{results.map((product, index) => (
									<motion.li
										key={product.id}
										id={`product-item-${product.id}`}
										className={`px-4 py-2 cursor-pointer transition-colors duration-200 ${
											selectedIndex === index ? "bg-gray-100" : "hover:bg-gray-50"
										}`}
										onClick={() => handleSelect(product)}
										role="option"
										aria-selected={selectedIndex === index}
									>
										<div className="flex justify-between">
											<span>{highlightText(product.name, query)}</span>
											<span className="text-sm text-gray-500">{product.quantity} unidades</span>
										</div>
									</motion.li>
								))}
							</ul>
						) : (
							<div className="px-4 py-2 text-gray-500">Nenhum produto encontrado. Tente outros termos de pesquisa.</div>
						)}
					</motion.div>
				)}
			</AnimatePresence>

			<FullScreenModal isOpen={scanning} onClose={() => setScanning(false)}>
				<BarcodeScanner onDetected={handleBarcodeDetected} onClose={() => setScanning(false)} />
			</FullScreenModal>
		</div>
	);
};
