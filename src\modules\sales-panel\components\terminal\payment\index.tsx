import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { useAtomValue } from "jotai";
import { BadgeDollarSign } from "lucide-react";
import { TitleWithLine } from "../title";
import PaymentList from "./list";
import PaymentForm from "./payment-form";

export const PaymentContainer = ({ className }: { className?: string }) => {
	const orderInfo = useAtomValue(orderInfoAtom);

	if (!orderInfo) {
		return (
			<div
				className={`
          w-full min-h-[12rem] p-4 sm:p-6 rounded-2xl shadow-main
          bg-gradient-to-br from-white to-[#f0fcfc]
          flex flex-col justify-center items-center text-center
          ${className ?? ""}
        `}
			>
				<TitleWithLine title="Pagamentos" icon={BadgeDollarSign} />
				<p className="text-gray-500 mt-2">Inicie uma venda para adicionar pagamentos 🤝</p>
			</div>
		);
	}

	const cashBackValue = +orderInfo.remainingValue < 0 ? Math.abs(+orderInfo.remainingValue) : 0;
	const remainingValue = +orderInfo.remainingValue < 0 ? "0,00" : orderInfo.remainingValue;

	return (
		<div
			className={`
        w-full min-h-[12rem] p-4 sm:p-6 rounded-2xl shadow-main
        bg-gradient-to-br from-white to-[#f0fcfc]
        flex flex-col gap-4
        ${className ?? ""}
      `}
		>
			<TitleWithLine title="Pagamentos" icon={BadgeDollarSign} />
			<PaymentForm />
			<main className="flex-1  min-h-0">
				<PaymentList />
			</main>
			<footer className="w-full flex flex-col md:flex-row justify-between items-center gap-4 px-2">
				<div className="flex sm:flex-col md:flex-row items-start md:items-center gap-4">
					<h2 className="text-base gap-1 flex items-center font-semibold">
						Pago: <span className="font-extrabold text-mainColor">{orderInfo.paidValue ?? "0,00"}</span>
					</h2>
					<h2 className="text-base text-[#505050] gap-1 flex items-center font-semibold">
						Falta: <span className="font-extrabold text-[#DB5353]">R$ {remainingValue}</span>
					</h2>
				</div>
				{cashBackValue > 0 && (
					<div>
						<p className="text-sm text-gray-500">
							🪙 Troco: <span className="font-semibold text-gray-700">R$ {cashBackValue.toFixed(2)}</span>
						</p>
					</div>
				)}
			</footer>
		</div>
	);
};
