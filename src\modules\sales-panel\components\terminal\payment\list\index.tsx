import { useRemovePayment } from "@/modules/sales-panel/hooks/payment/remove-payment-mutation.hook";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { toast } from "sonner";
import { containerVariants } from "../animate-variants";
import PaymentItem from "./item";

const PaymentList = () => {
	const orderInfo = useAtomValue(orderInfoAtom);
	const payments = orderInfo?.payments ?? [];
	const { removePaymentMutation } = useRemovePayment();

	return (
		<section
			className="
        w-full h-full p-3 sm:p-2 shadow-[inset_0_2px_4px_rgba(0,0,0,0.15)]
        bg-[#f5f5f5] rounded-lg overflow-y-auto
      "
		>
			<motion.div variants={containerVariants} initial="hidden" animate="visible">
				<AnimatePresence>
					{payments.length > 0 ? (
						payments.map(payment => (
							<PaymentItem
								key={payment.id}
								payment={payment}
								onRemove={id =>
									removePaymentMutation.mutate(
										{ orderId: orderInfo?.id ?? 0, paymentId: id },
										{
											onSuccess: () => toast.success("✅ Pagamento removido com sucesso!"),
										}
									)
								}
							/>
						))
					) : (
						<div className="w-full h-full flex items-center justify-center py-6">
							<h1 className="text-[#606060] text-center text-base sm:text-lg">Nenhuma forma de pagamento adicionada 😕</h1>
						</div>
					)}
				</AnimatePresence>
			</motion.div>
		</section>
	);
};

export default PaymentList;
