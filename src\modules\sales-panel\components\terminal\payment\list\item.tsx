import { IPaymentSocket } from "@/modules/sales-panel/dto/order-socket.dto";
import { motion } from "framer-motion";
import React from "react";
import { itemVariants } from "../animate-variants";

type PaymentItemProps = {
	payment: IPaymentSocket;
	onRemove: (id: number) => void;
};

const PaymentItem: React.FC<PaymentItemProps> = ({ payment, onRemove }) => (
	<motion.article
		key={payment.id}
		variants={itemVariants}
		layout
		className="
      w-full border border-mainColor bg-white flex items-center justify-between 
      p-3 rounded-lg mb-2 shadow-sm hover:shadow-md transition-shadow duration-200
    "
	>
		<div className="flex-1 min-w-0">
			<h2 className="font-semibold text-lg sm:text-xl truncate">{payment.paymentMethod}</h2>
			<p className="text-gray-600 text-sm sm:text-base truncate">
				Valor: R$ {payment.value}
				{!!payment.parcel && payment.parcel > 1 && <span className="ml-1">{`em ${payment.parcel}x`}</span>}
				{!!payment.parcel && payment.parcel === 1 && <span className="ml-1">à vista</span>}
			</p>
		</div>
		<motion.button
			type="button"
			className="
        text-[#DB5353] text-sm sm:text-base font-medium px-2 py-1 rounded-md
        hover:bg-[#DB5353] hover:text-white transition-colors duration-150
      "
			onClick={() => onRemove(payment.id)}
			aria-label={`Remover pagamento ${payment.id}`}
			whileTap={{ scale: 0.95 }}
		>
			Remover
		</motion.button>
	</motion.article>
);

export default PaymentItem;
