import { CreatePersonSchemaType, useAddress } from "@/modules/person";
import { AnimatedInput } from "@/shared/components/ui/animated-input";
import { MapPinHouse } from "lucide-react";
import { Control, UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";

interface AddressSectionProps {
	register: UseFormRegister<CreatePersonSchemaType>;
	setValue: UseFormSetValue<CreatePersonSchemaType>;
	watch: UseFormWatch<CreatePersonSchemaType>;
	control: Control<CreatePersonSchemaType>;
}

export const AddressSection = ({ register, setValue, watch }: AddressSectionProps) => {
	const { fetchAddressByCep, isLoading, error } = useAddress(setValue);
	const formValues = {
		street: watch("address.street"),
		number: watch("address.number"),
		complement: watch("address.complement"),
		neighborhood: watch("address.neighborhood"),
		city: watch("address.city"),
		state: watch("address.state"),
	};

	const handleCepChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const cep = e.target.value.replace(/\D/g, "");
		if (cep.length === 8) {
			fetchAddressByCep(cep);
		}
	};

	return (
		<section>
			<h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
				<MapPinHouse size={20} />
				<span>Endereço</span>
				<div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
			</h4>
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
				<AnimatedInput
					{...register("address.postalCode")}
					id="address.postalCode"
					label="CEP"
					placeholder="Ex: 00000-000"
					onChange={e => {
						register("address.postalCode").onChange(e);
						handleCepChange(e);
					}}
					isLoading={isLoading}
					error={error || undefined}
				/>

				<div className="sm:col-span-2">
					<AnimatedInput
						{...register("address.street")}
						id="address.street"
						label="Logradouro"
						placeholder="Rua, Avenida, etc."
						value={formValues.street}
					/>
				</div>

				<AnimatedInput {...register("address.number")} id="address.number" label="Número" placeholder="Nº" value={formValues.number} />

				<AnimatedInput
					{...register("address.complement")}
					id="address.complement"
					label="Complemento"
					placeholder="Apt, bloco, etc."
					value={formValues.complement}
				/>

				<AnimatedInput
					{...register("address.neighborhood")}
					id="address.neighborhood"
					label="Bairro"
					placeholder="Ex: Centro"
					value={formValues.neighborhood}
				/>

				<AnimatedInput {...register("address.city")} id="address.city" label="Cidade" placeholder="Ex: São Paulo" value={formValues.city} />

				<AnimatedInput {...register("address.state")} id="address.state" label="Estado" placeholder="Ex: SP" value={formValues.state} />
			</div>
		</section>
	);
};
