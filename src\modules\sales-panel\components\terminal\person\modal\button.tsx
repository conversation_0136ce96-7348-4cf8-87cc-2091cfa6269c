import { But<PERSON> } from "@/shared/components/ui/button";

import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { User } from "lucide-react";
import { IdentifyPersonModal } from "./index";

export const IdentifyPersonButton = ({ orderId }: { orderId: number | null }) => {
	const { isOpen, closeModal, toggleModal } = useModal();

	useKeyboardShortcut({
		combination: { key: "i" },
		handler: () => {
			if (orderId && !isOpen) {
				toggleModal();
			}
		},
		options: {
			ignoreInputs: false,
			preventDefault: true,
			disabled: !orderId,
		},
	});

	return (
		<>
			<CustomTooltip content={!orderId ? "Selecione um pedido primeiro" : "Identificar cliente"}>
				<Button
					onClick={toggleModal}
					disabled={!orderId}
					className="bg-mainColor shadow-main px-2 gap-1 2xl:gap-2 flex text-white 2xl:px-3 2xl:py-1 rounded-[15px] transition-all duration-200 hover:bg-mainColor/90 disabled:opacity-50 disabled:cursor-not-allowed"
					aria-label="Identificar cliente"
					aria-disabled={!orderId}
				>
					<User size={18} aria-hidden="true" />
					<span>Identificar cliente</span>
					<span className="text-xs bg-white text-mainColor px-1 rounded" aria-hidden="true">
						I
					</span>
				</Button>
			</CustomTooltip>
			<IdentifyPersonModal isOpen={isOpen} onClose={closeModal} />
		</>
	);
};
