import { useIdentifyPersonModal } from "@/modules/sales-panel/hooks/person/use-identify-person-modal.hook";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { motion } from "framer-motion";
import { FiArrowLeft } from "react-icons/fi";
import { MainDataSection } from "./main-data-section";
import { ModalHeader } from "./modal-header";
import { BusinessDetailsForm } from "./optional-data-section";
import { SearchCustomerSection } from "./search-customer-section";
import { SelectionScreen } from "./selection-screen";

interface IdentifyPersonModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const IdentifyPersonModal = ({ isOpen, onClose }: IdentifyPersonModalProps) => {
	const { mode, setMode, isCnpj, setIsCnpj, isLoading, register, handleSubmit, control, errors, formattedOrderInfo, onSubmit } =
		useIdentifyPersonModal({ isOpen, onClose });

	return (
		<OverlayContainer isVisible={isOpen} onClose={onClose}>
			<motion.dialog
				onClick={e => e.stopPropagation()}
				open={isOpen}
				aria-label="Identificar Cliente"
				className="flex flex-col w-full min-h-[90vh] max-w-4xl max-h-[90vh] bg-white rounded-[15px] shadow-lg"
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.9 }}
				transition={{ duration: 0.3, ease: "easeInOut" }}
			>
				<div className="flex-shrink-0">
					<ModalHeader onClose={onClose} />
				</div>

				{mode === "select" && (
					<SelectionScreen
						onSearchClick={() => setMode("search")}
						onCreateClick={() => setMode("create")}
						onClose={onClose}
						orderInfo={formattedOrderInfo}
					/>
				)}
				{mode === "search" && <SearchCustomerSection onBack={() => setMode("select")} onClose={onClose} />}

				{mode === "create" && (
					<motion.form
						onSubmit={handleSubmit(onSubmit)}
						className="flex flex-col flex-1 overflow-hidden p-4"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.3 }}
					>
						<div className="mb-4">
							<Button
								type="button"
								onClick={() => setMode("select")}
								className="p-1 text-gray-600 hover:text-mainColor hover:bg-transparent flex items-center gap-1"
								variant="ghost"
							>
								<FiArrowLeft size={18} />
								<span className="text-sm">Voltar</span>
							</Button>
						</div>
						<div className="flex-1 overflow-y-auto gap-4 flex flex-col px-4 py-4">
							{errors.root && (
								<div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
									<p className="text-sm text-red-700">{errors.root.message}</p>
								</div>
							)}

							{Object.keys(errors).length > 0 && (
								<div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
									<p className="text-sm text-red-700">Verifique os campos obrigatórios.</p>
								</div>
							)}
							<MainDataSection register={register} control={control} onCnpjChange={setIsCnpj} isCnpj={isCnpj} />
							<BusinessDetailsForm register={register} control={control} isCnpj={isCnpj} />
						</div>

						<div className="flex-shrink-0 border-t border-gray-200 p-4 bg-white flex justify-end gap-3">
							<Button
								type="submit"
								disabled={isLoading}
								className="px-4 py-2 rounded-[15px] bg-mainColor text-white font-semibold hover:bg-cyan-800 transition-colors disabled:opacity-50"
							>
								{isLoading ? "Salvando..." : "Salvar"}
							</Button>
						</div>
					</motion.form>
				)}
			</motion.dialog>
		</OverlayContainer>
	);
};
