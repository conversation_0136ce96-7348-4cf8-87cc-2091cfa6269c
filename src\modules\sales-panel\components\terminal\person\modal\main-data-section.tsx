import { CreatePersonSchemaType } from "@/modules/person";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";

import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
import { phoneMask } from "@/shared/data/masks/phone";
import { UserPen } from "lucide-react";
import { Control, Controller, UseFormRegister, useFormState } from "react-hook-form";

interface MainDataSectionProps {
	register: UseFormRegister<CreatePersonSchemaType>;
	control: Control<CreatePersonSchemaType>;
	onCnpjChange: (isCnpj: boolean) => void;
	isCnpj: boolean;
}

export const MainDataSection = ({ register, control, onCnpjChange, isCnpj }: MainDataSectionProps) => {
	const { errors } = useFormState({ control });

	return (
		<section className="flex flex-col gap-2">
			<h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
				<UserPen size={20} aria-hidden="true" />
				<span>Dados Principais</span>
				<div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
			</h4>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div className="lg:min-w-[150px] lg:w-auto w-full relative">
					<Label htmlFor="name" className="absolute top-[-5px] bg-white px-3 left-3">
						Nome
					</Label>
					<Input
						{...register("name")}
						id="name"
						placeholder="Informe o nome completo"
						className={`border-2 h-[45px] text-gray-600 rounded-[15px] pl-10 w-full px-3 transition-all duration-200 ${
							errors.name ? "border-red-500 focus:border-red-500" : "border-gray-400 focus:border-mainColor"
						}`}
						aria-invalid={!!errors.name}
						aria-describedby={errors.name ? "name-error" : undefined}
					/>
					{errors.name && (
						<p id="name-error" className="text-sm text-red-500 mt-1 ml-3">
							{errors.name.message}
						</p>
					)}
				</div>

				<div className="lg:min-w-[150px] lg:w-auto w-full relative">
					<Label htmlFor="cnpjCpf" className="absolute top-[-5px] bg-white px-3 left-3">
						CPF/CNPJ
					</Label>
					<Controller
						name="cnpjCpf"
						control={control}
						render={({ field }) => (
							<>
								<Input
									{...field}
									onChange={e => {
										const numericValue = e.target.value.replace(/\D/g, "");
										const maskedValue = cpfCnpjMask(numericValue);
										field.onChange(maskedValue);
										onCnpjChange(numericValue.length > 11);
									}}
									id="cnpjCpf"
									placeholder="Digite o CPF ou CNPJ"
									className={`border-2 h-[45px] text-gray-600 rounded-[15px] pl-10 w-full px-3 transition-all duration-200 ${
										errors.cnpjCpf ? "border-red-500 focus:border-red-500" : "border-gray-400 focus:border-mainColor"
									}`}
									aria-invalid={!!errors.cnpjCpf}
									aria-describedby={errors.cnpjCpf ? "cnpjCpf-error" : undefined}
								/>
								{errors.cnpjCpf && (
									<p id="cnpjCpf-error" className="text-sm text-red-500 mt-1 ml-3">
										{errors.cnpjCpf.message}
									</p>
								)}
							</>
						)}
					/>
				</div>

				<div className="lg:min-w-[150px] lg:w-auto w-full relative">
					<Label htmlFor="phone" className="absolute top-[-5px] bg-white px-3 left-3">
						Telefone{" "}
						{!isCnpj && (
							<CustomTooltip content="Opcional para pessoas físicas">
								<span className="text-gray-400 text-sm">(Opcional)</span>
							</CustomTooltip>
						)}
					</Label>
					<Controller
						name="phone"
						control={control}
						render={({ field }) => (
							<>
								<Input
									{...field}
									onChange={e => {
										const maskedValue = phoneMask(e.target.value);
										field.onChange(maskedValue);
									}}
									id="phone"
									placeholder="(XX) 9XXXX-XXXX"
									className={`border-2 h-[45px] text-gray-600 rounded-[15px] pl-10 w-full px-3 transition-all duration-200 ${
										errors.phone ? "border-red-500 focus:border-red-500" : "border-gray-400 focus:border-mainColor"
									}`}
									aria-invalid={!!errors.phone}
									aria-describedby={errors.phone ? "phone-error" : undefined}
								/>
								{errors.phone && (
									<p id="phone-error" className="text-sm text-red-500 mt-1 ml-3">
										{errors.phone.message}
									</p>
								)}
							</>
						)}
					/>
				</div>

				<div className="lg:min-w-[150px] lg:w-auto w-full relative">
					<Label htmlFor="email" className="absolute top-[-5px] bg-white px-3 left-3">
						E-mail{" "}
						{!isCnpj && (
							<CustomTooltip content="Opcional para pessoas físicas">
								<span className="text-gray-400 text-sm">(Opcional)</span>
							</CustomTooltip>
						)}
					</Label>
					<Input
						{...register("email")}
						id="email"
						placeholder="<EMAIL>"
						className={`border-2 h-[45px] text-gray-600 rounded-[15px] pl-10 w-full px-3 transition-all duration-200 ${
							errors.email ? "border-red-500 focus:border-red-500" : "border-gray-400 focus:border-mainColor"
						}`}
						aria-invalid={!!errors.email}
						aria-describedby={errors.email ? "email-error" : undefined}
					/>
					{errors.email && (
						<p id="email-error" className="text-sm text-red-500 mt-1 ml-3">
							{errors.email.message}
						</p>
					)}
				</div>
			</div>
		</section>
	);
};
