import { LucideX, User } from "lucide-react";

interface ModalHeaderProps {
	onClose: () => void;
}

export const ModalHeader = ({ onClose }: ModalHeaderProps) => {
	return (
		<div className="flex items-center justify-between pt-4 border-b border-b-gray-400 px-4">
			<div>
				<h3 className="text-lg font-semibold text-[#505050] flex items-center gap-2">
					<User size={20} />
					Identificar cliente
				</h3>
				<p className="mb-4 text-sm text-gray-600">Utilize os campos abaixo para identificar o cliente</p>
			</div>
			<button className="font-semibold text-[#505050] hover:text-white hover:bg-cyan-700 p-2 rounded-full transition-colors" onClick={onClose}>
				<LucideX size={20} />
			</button>
		</div>
	);
};
