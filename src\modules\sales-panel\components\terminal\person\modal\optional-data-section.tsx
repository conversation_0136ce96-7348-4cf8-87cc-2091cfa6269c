import { CreatePersonSchemaType } from "@/modules/person";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { User } from "lucide-react";
import { Control, UseFormRegister, useFormState } from "react-hook-form";

interface BusinessDetailsFormProps {
	register: UseFormRegister<CreatePersonSchemaType>;
	control: Control<CreatePersonSchemaType>;
	isCnpj: boolean;
}

export const BusinessDetailsForm = ({ register, control, isCnpj }: BusinessDetailsFormProps) => {
	const { errors } = useFormState({ control });

	if (!isCnpj) return null;

	return (
		<section>
			<h4 className="text-base font-bold text-gray-500 flex items-center mb-2 gap-1">
				<User size={20} />
				<span>Dados Empresariais</span>
				<div className="flex-1 h-[1px] bg-gray-300 ml-2"></div>
			</h4>
			<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
				<div className="lg:min-w-[150px] lg:w-auto w-full relative">
					<Label htmlFor="tradeName" className="absolute top-[-5px] bg-white px-3 left-3">
						Nome Empresarial
					</Label>
					<Input
						{...register("tradeName")}
						id="tradeName"
						placeholder="Digite o nome empresarial"
						className={`border-2 h-[45px] text-gray-600 rounded-[15px] border-gray-400 pl-10 w-full px-3 ${
							errors.tradeName ? "border-red-500" : "border-gray-400"
						}`}
					/>
					{errors.tradeName && <p className="text-sm text-red-500 mt-1 ml-3">{errors.tradeName.message}</p>}
				</div>

				<div className="lg:min-w-[150px] lg:w-auto w-full relative">
					<Label htmlFor="ie" className="absolute top-[-5px] bg-white px-3 left-3">
						Inscrição Estadual
					</Label>
					<Input
						{...register("ie")}
						id="ie"
						placeholder="Se houver"
						className={`border-2 h-[45px] text-gray-600 rounded-[15px] border-gray-400 pl-10 w-full px-3 ${
							errors.ie ? "border-red-500" : "border-gray-400"
						}`}
					/>
					{errors.ie && <p className="text-sm text-red-500 mt-1 ml-3">{errors.ie.message}</p>}
				</div>
			</div>
		</section>
	);
};
