import { useUpdateOrderMutation } from "@/modules/sales-panel/hooks/order/update-order-mutation.hook";
import { useSearchCustomer } from "@/modules/sales-panel/hooks/person/use-search-customer.hook";
import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { Controller } from "react-hook-form";
import { FiArrowLeft, FiUser } from "react-icons/fi";
import { Pagination } from "../../../../../../shared/components/custom/pagination";

interface SearchCustomerSectionProps {
	onBack: () => void;
	onClose: () => void;
}

export const SearchCustomerSection = ({ onBack, onClose }: SearchCustomerSectionProps) => {
	const { methods, response, isLoading, page, itemsPerPage, onPageChange, onItemsPerPageChange, resetFilters } = useSearchCustomer({
		onClose: () => {
			methods.reset();
			resetFilters();
			onClose();
		},
	});
	const orderId = useAtomValue(orderIdAtom);
	const { updateOrderMutation } = useUpdateOrderMutation();

	const handleCustomerSelect = (customerId: number) => {
		if (orderId) {
			updateOrderMutation.mutate(
				{
					orderId,
					item: { customerId },
				},
				{
					onSuccess: () => {
						onClose();
					},
				}
			);
		} else {
			onClose();
		}
	};

	return (
		<motion.div
			className="flex flex-col flex-1 overflow-hidden p-4"
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.3 }}
		>
			<div className="mb-4">
				<Button
					type="button"
					onClick={onBack}
					className="p-1 text-gray-600 hover:text-mainColor hover:bg-transparent flex items-center gap-1"
					variant="ghost"
				>
					<FiArrowLeft size={18} />
					<span className="text-sm">Voltar</span>
				</Button>
			</div>
			<div className="flex gap-4 mb-4">
				<div className="flex-1 relative">
					<Label htmlFor="name" className="absolute top-[-5px] bg-white px-3 left-3">
						Nome
					</Label>

					<Input
						{...methods.register("name")}
						id="name"
						placeholder="Informe o nome completo"
						className="border-2 h-[45px] text-gray-600 rounded-[15px] border-gray-400 pl-10 w-full px-3"
					/>
				</div>
				<div className="flex-1 relative">
					<Label htmlFor="cnpjCpf" className="absolute top-[-5px] bg-white px-3 left-3">
						CPF/CNPJ
					</Label>
					<Controller
						name="document"
						control={methods.control}
						render={({ field }) => (
							<Input
								{...field}
								onChange={e => {
									const numericValue = e.target.value.replace(/\D/g, "");
									const maskedValue = cpfCnpjMask(numericValue);
									field.onChange(maskedValue);
								}}
								id="cnpjCpf"
								placeholder="Digite o CPF ou CNPJ"
								className="border-2 h-[45px] text-gray-600 rounded-[15px] border-gray-400 w-full px-3"
							/>
						)}
					/>
				</div>
			</div>

			<div className="flex-1 overflow-y-auto">
				{isLoading ? (
					<motion.div
						className="flex flex-col items-center justify-center h-full gap-3"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.5 }}
					>
						<motion.div
							className="w-10 h-10 border-t-2 border-b-2 border-mainColor rounded-full"
							animate={{ rotate: 360 }}
							transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
						/>
						<span className="text-gray-500 font-medium">Buscando clientes...</span>
					</motion.div>
				) : (
					<AnimatePresence>
						<motion.div
							className="grid gap-3 p-2"
							initial="hidden"
							animate="show"
							variants={{
								hidden: { opacity: 0 },
								show: {
									opacity: 1,
									transition: {
										staggerChildren: 0.1,
									},
								},
							}}
						>
							{response?.success &&
								response?.data?.data?.map(customer => (
									<motion.div
										key={customer.id}
										className="p-4 border border-gray-200 rounded-[15px] cursor-pointer hover:bg-gray-50 shadow-sm hover:shadow-md hover:border-mainColor"
										whileHover={{
											scale: 1.01,
											backgroundColor: "rgba(0, 159, 227, 0.05)",
										}}
										whileTap={{ scale: 0.98 }}
										onClick={() => {
											handleCustomerSelect(customer.id);
										}}
										variants={{
											hidden: { opacity: 0, y: 20 },
											show: { opacity: 1, y: 0 },
										}}
										transition={{
											type: "spring",
											stiffness: 300,
											damping: 20,
										}}
									>
										<div className="flex items-center gap-3">
											<motion.div
												className="p-3 bg-mainColor/10 rounded-full"
												whileHover={{ scale: 1.1, backgroundColor: "rgba(0, 159, 227, 0.2)" }}
											>
												<FiUser size={20} className="text-mainColor" />
											</motion.div>
											<div className="flex-1">
												<motion.div
													className="flex justify-between items-center"
													initial={{ opacity: 0 }}
													animate={{ opacity: 1 }}
													transition={{ delay: 0.2 }}
												>
													<p className="text-sm text-gray-500 font-medium">Código: #{customer.id}</p>
													<motion.div
														className="bg-mainColor text-white px-2 py-1 rounded-full text-xs font-bold"
														whileHover={{ scale: 1.05 }}
														whileTap={{ scale: 0.95 }}
														transition={{ type: "spring", stiffness: 400 }}
													>
														Selecionar
													</motion.div>
												</motion.div>
												<h3 className="font-semibold text-gray-800 text-lg">{customer.name}</h3>
											</div>
										</div>
									</motion.div>
								))}
							{response?.success && response?.data.data?.length === 0 && (
								<motion.div
									className="text-center py-10 border-2 border-dashed border-gray-300 rounded-lg"
									initial={{ opacity: 0, y: 10 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.5 }}
								>
									<p className="text-gray-500 font-medium">Nenhum cliente encontrado com os critérios informados</p>
									<p className="text-sm text-gray-400 mt-2">Tente alterar os filtros de busca</p>
								</motion.div>
							)}
						</motion.div>
					</AnimatePresence>
				)}
			</div>
			<Pagination
				page={page}
				totalPages={response?.success && response.data.total ? response.data.total : 0}
				itemsPerPage={itemsPerPage}
				onPageChange={onPageChange}
				onItemsPerPageChange={onItemsPerPageChange}
			/>
		</motion.div>
	);
};
