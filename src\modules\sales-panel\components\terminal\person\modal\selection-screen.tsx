import { Button } from "@/shared/components/ui/button";
import { motion } from "framer-motion";
import { FiChevronRight, FiSearch, FiUser, FiUserPlus } from "react-icons/fi";

interface SelectionScreenProps {
	onSearchClick: () => void;
	onCreateClick: () => void;
	onClose: () => void;
	orderInfo?: { customer?: string; total?: string };
}

export const SelectionScreen = ({ onSearchClick, onCreateClick, onClose, orderInfo }: SelectionScreenProps) => (
	<motion.div
		className="flex flex-col items-center justify-center gap-6 p-8"
		initial={{ opacity: 0 }}
		animate={{ opacity: 1 }}
		transition={{ duration: 0.5 }}
	>
		<motion.h2
			className="text-2xl font-bold text-gray-800 mb-2"
			initial={{ y: -20 }}
			animate={{ y: 0 }}
			transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
		>
			{orderInfo?.customer ? "Cliente Atual" : "Como deseja prosseguir?"}
		</motion.h2>

		<motion.p
			className="text-gray-500 text-center max-w-md mb-4"
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ delay: 0.3, duration: 0.5 }}
		>
			{orderInfo?.customer
				? "Esta venda já possui um cliente vinculado. Vincular outro cliente irá substituir o atual."
				: "Escolha uma das opções abaixo para continuar com a identificação do cliente"}
		</motion.p>

		{orderInfo?.customer ? (
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3, type: "spring" }}
				className="flex items-center p-4 border border-gray-200 rounded-xl bg-mainColor/10 gap-3"
			>
				<div className="p-2 bg-mainColor rounded-full text-white shrink-0">
					<FiUser size={18} />
				</div>
				<div className="min-w-0 flex-1">
					<h3 className="text-base font-semibold text-gray-800 truncate">{orderInfo.customer}</h3>
					<p className="text-xs text-gray-500">Cliente vinculado</p>
				</div>
				<Button onClick={onClose} className="shrink-0 h-8 px-3 bg-mainColor text-white text-sm hover:bg-mainColor/60 rounded-xl">
					Manter
				</Button>
			</motion.div>
		) : null}

		<div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
			<motion.div
				whileHover={{
					scale: 1.03,
					boxShadow: "0 10px 25px -5px rgba(0, 159, 227, 0.2), 0 10px 10px -5px rgba(0, 159, 227, 0.1)",
				}}
				whileTap={{ scale: 0.98 }}
				initial={{ opacity: 0, x: -50 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.4, type: "spring" }}
				className="border border-gray-200 rounded-[20px] overflow-hidden shadow-sm hover:border-cyan-600"
			>
				<div className="p-6 flex flex-col h-full">
					<div className="flex items-center mb-4">
						<div className="p-3 bg-cyan-600 rounded-full text-white">
							<FiSearch size={24} />
						</div>
						<h3 className="ml-4 text-lg font-semibold text-gray-800">Buscar Cliente</h3>
					</div>
					<p className="text-gray-500 mb-4 flex-grow">
						{orderInfo?.customer
							? "Escolha outro cliente para vincular a esta venda"
							: "Localize um cliente já cadastrado no sistema para prosseguir com a operação"}
					</p>
					<Button
						onClick={onSearchClick}
						className="w-full py-3 rounded-[15px] bg-cyan-600 text-white hover:bg-cyan-700 transition-all flex items-center justify-center gap-2 group"
					>
						Buscar Cliente
						<FiChevronRight className="transition-transform group-hover:translate-x-1" />
					</Button>
				</div>
			</motion.div>

			<motion.div
				whileHover={{
					scale: 1.03,
					boxShadow: "0 10px 25px -5px rgba(0, 159, 227, 0.2), 0 10px 10px -5px rgba(0, 159, 227, 0.1)",
				}}
				whileTap={{ scale: 0.98 }}
				initial={{ opacity: 0, x: 50 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.5, type: "spring" }}
				className="border border-gray-200 rounded-[20px] overflow-hidden shadow-sm hover:border-mainColor"
			>
				<div className="p-6 flex flex-col h-full">
					<div className="flex items-center mb-4">
						<div className="p-3 bg-mainColor rounded-full text-white">
							<FiUserPlus size={24} />
						</div>
						<h3 className="ml-4 text-lg font-semibold text-gray-800">Novo Cliente</h3>
					</div>
					<p className="text-gray-500 mb-4 flex-grow">
						{orderInfo?.customer
							? "Cadastre um novo cliente para substituir o atual"
							: "Cadastre um novo cliente no sistema para prosseguir com a operação atual"}
					</p>
					<Button
						onClick={onCreateClick}
						className="w-full py-3 rounded-[15px] bg-mainColor text-white hover:bg-cyan-800 transition-all flex items-center justify-center gap-2 group"
					>
						Criar Cliente
						<FiChevronRight className="transition-transform group-hover:translate-x-1" />
					</Button>
				</div>
			</motion.div>
		</div>
	</motion.div>
);
