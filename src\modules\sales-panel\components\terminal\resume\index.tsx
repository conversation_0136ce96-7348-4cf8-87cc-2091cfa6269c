import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { Separator } from "@/shared/components/ui/separator";
import { useAtomValue } from "jotai";
import { ShoppingCart } from "lucide-react";
import { TitleWithLine } from "../title";
import { FinishButton } from "./finish";
import { ShippingDiscountForm } from "./shipping-discount-form";

const formatCurrency = (value: string | number | undefined) => {
	if (!value) return "R$ 0,00";
	const number = typeof value === "string" ? parseFloat(value) : value;
	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
	}).format(number);
};

interface IResumeCardProps {
	customClassName?: string;
}

export const ResumeCard = ({ customClassName = "" }: IResumeCardProps) => {
	const orderId = useAtomValue(orderIdAtom);
	const orderInfo = useAtomValue(orderInfoAtom);

	if (!orderId) {
		return (
			<div
				className={`
          flex flex-col w-full min-h-[300px] max-w-full min-w-0
          p-5 sm:p-7 bg-white rounded-[20px] shadow-main
          border border-gray-200 transition-all duration-300
          items-center justify-center text-gray-400
          ${customClassName}
        `}
			>
				<TitleWithLine title="Resumo" icon={ShoppingCart} />
				<p className="text-gray-500 mt-2">Inicie uma venda para visualizar o resumo</p>
			</div>
		);
	}

	return (
		<div
			className={`
        flex flex-col w-full max-w-full min-w-0
        p-5 sm:p-7 bg-white rounded-[20px] shadow-main
        border border-gray-200 transition-all duration-300
        ${customClassName}
      `}
		>
			<TitleWithLine title="Resumo do Pedido" icon={ShoppingCart} />

			<div className="flex flex-col flex-grow">
				<div
					className="
            grid grid-cols-1 gap-y-3
            text-gray-700 text-sm sm:text-base
            lg:grid-cols-2 lg:gap-x-4
            3xl:grid-cols-1
            overflow-hidden
          "
				>
					<div className="flex justify-between">
						<span className="font-semibold">Itens</span>
						<span>{orderInfo?.items?.length ?? 0}</span>
					</div>

					<div className="flex justify-between">
						<span className="font-semibold">Subtotal</span>
						<span>{formatCurrency(orderInfo?.subtotal)}</span>
					</div>

					<div className="flex justify-between">
						<span className="font-semibold">Desconto</span>
						<span>{formatCurrency(orderInfo?.discount)}</span>
					</div>

					<div className="flex justify-between">
						<span className="font-semibold">Frete</span>
						<span>{formatCurrency(orderInfo?.shippingCost)}</span>
					</div>
				</div>

				<Separator className="my-4 bg-gray-300" />

				<div className="flex justify-between items-center font-bold text-lg sm:text-xl text-gray-800">
					<span>Total</span>
					<span className="text-mainColor">{orderInfo?.total}</span>
				</div>

				<div className="flex justify-end flex-col sm:flex-row gap-3 mt-5">
					<ShippingDiscountForm />
					<FinishButton />
				</div>
			</div>
		</div>
	);
};
