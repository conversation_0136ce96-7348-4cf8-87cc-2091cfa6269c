import { couponState } from "@/modules/sales-panel/states/coupon.state";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { useAtom } from "jotai";
import { X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

function useIsMobile() {
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		if (typeof window !== "undefined") {
			setIsMobile(/Mobi|Android/i.test(window.navigator.userAgent));
		}
	}, []);

	return isMobile;
}

export function PdfModalCoupon() {
	const [coupon, setCoupon] = useAtom(couponState);
	const [pdfUrl, setPdfUrl] = useState<string | null>(null);
	const isMobile = useIsMobile();
	const open = Boolean(coupon);
	const onClose = () => setCoupon(null);

	const iframeRef = useRef<HTMLIFrameElement | null>(null);

	useEffect(() => {
		if (!coupon) return;

		const blob = coupon instanceof Blob ? coupon : new Blob([coupon], { type: "application/pdf" });
		const url = URL.createObjectURL(blob);
		setPdfUrl(url);

		return () => {
			URL.revokeObjectURL(url);
			setPdfUrl(null);
		};
	}, [coupon]);

	useEffect(() => {
		if (!pdfUrl || !iframeRef.current || isMobile) return;

		const handleLoad = () => {
			iframeRef.current?.contentWindow?.print();
		};

		const iframe = iframeRef.current;
		iframe.addEventListener("load", handleLoad);

		return () => {
			iframe.removeEventListener("load", handleLoad);
		};
	}, [pdfUrl, isMobile]);

	if (!coupon) return null;

	return (
		<OverlayContainer isVisible={open} onClose={onClose}>
			<div
				className="
          bg-white
          rounded-xl
          m-4
          w-[90vw]
          h-[90vh]
          shadow-xl
          flex
          flex-col
        "
				role="dialog"
				aria-modal="true"
				aria-label="Cupom Fiscal"
			>
				<header className="flex items-center justify-between px-6 py-4 bg-mainColor text-white rounded-t-xl">
					<h2 className="font-semibold text-2xl">Cupom Fiscal</h2>
					<button onClick={onClose} className="hover:opacity-80 transition-opacity" aria-label="Fechar Modal">
						<X size={24} />
					</button>
				</header>

				<main className="flex-1 bg-gray-100 overflow-auto p-4">
					{!pdfUrl ? (
						<p>Carregando PDF…</p>
					) : isMobile ? (
						<div className="flex flex-col items-center justify-center h-full space-y-4">
							<p>📱 Visualização de PDF não é compatível com dispositivos móveis.</p>
							<p>Por favor, utilize o botão abaixo para baixar o PDF.</p>
							<a
								href={pdfUrl}
								download="cupom.pdf"
								className="bg-mainColor text-white px-4 py-2 rounded hover:opacity-80 transition-opacity"
							>
								💾 Baixar PDF
							</a>
						</div>
					) : (
						<>
							<embed src={pdfUrl} type="application/pdf" width="100%" height="100%" className="w-full h-full rounded" />
							<iframe ref={iframeRef} src={pdfUrl} style={{ display: "none" }} title="PDF Print" />
						</>
					)}
				</main>
			</div>
		</OverlayContainer>
	);
}
