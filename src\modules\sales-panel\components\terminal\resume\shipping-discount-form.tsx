import { useShippingDiscountUI } from "@/modules/sales-panel/hooks/shipping/use-shipping-discount-ui.hook";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/components/ui/dropdown-menu";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { ChevronDown, Truck } from "lucide-react";
import { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import { NumericFormat } from "react-number-format";

export function ShippingDiscountForm() {
	const { isOpen, toggleModal, form, onSubmit, isLoading, calculatePreview } = useShippingDiscountUI();
	const { handleSubmit, control, watch, setValue } = form;
	const discountType = watch("discountType", "value");
	const discountValue = watch("discount", "");
	const [previewValue, setPreviewValue] = useState<number | null>(null);

	useEffect(() => {
		const preview = calculatePreview(discountValue || "", discountType);
		setPreviewValue(preview);
	}, [discountValue, discountType, calculatePreview]);

	useKeyboardShortcut({
		combination: { key: "e" },
		handler: () => {
			toggleModal();
		},
	});

	const handleDiscountTypeChange = (type: "value" | "percentage") => {
		setValue("discountType", type);
		setValue("discount", "");
	};

	return (
		<>
			<Button onClick={toggleModal} className="bg-mainColor shadow-main gap-2 flex text-white px-3 py-1 rounded-[15px]">
				<Truck size={18} />
				<span className="text-sm">Frete e Desconto</span>
				<span className="text-xs bg-white text-mainColor px-1 rounded">E</span>
			</Button>
			<OverlayContainer isVisible={isOpen} onClose={toggleModal}>
				<form
					onSubmit={handleSubmit(onSubmit)}
					className="flex flex-col bg-white p-4 rounded-[15px] shadow-main relative z-10"
					onClick={e => e.stopPropagation()}
				>
					<h2 className="text-xl text-[#505050] mb-2 font-semibold">Frete e Desconto</h2>
					<p className="mb-4 text-sm text-gray-600">Informe os valores de frete e desconto aqui abaixo.</p>

					<div className="flex gap-2">
						<div className="relative">
							<Controller
								control={control}
								name="shippingCost"
								render={({ field: { onChange, value } }) => (
									<>
										<Label className="absolute top-[-5px] bg-white px-3 left-3 z-10">Frete</Label>
										<NumericFormat
											value={value}
											onValueChange={values => onChange(values.value)}
											thousandSeparator="."
											decimalSeparator=","
											prefix="R$ "
											placeholder="R$ 0,00"
											customInput={Input}
											className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050]"
										/>
									</>
								)}
							/>
						</div>

						<div className="relative">
							<Controller
								control={control}
								name="discount"
								render={({ field: { onChange, value } }) => (
									<>
										<Label className="absolute top-[-5px] bg-white px-3 left-3 z-10">
											{discountType === "value" ? "Desconto" : "Desconto (%)"}
										</Label>
										<div className="relative">
											<NumericFormat
												value={value}
												onValueChange={values => onChange(values.value)}
												thousandSeparator="."
												decimalSeparator=","
												prefix={discountType === "value" ? "R$ " : ""}
												suffix={discountType === "percentage" ? " %" : ""}
												placeholder={discountType === "value" ? "R$ 0,00" : "0 %"}
												customInput={Input}
												className="border-2 h-[45px] text-gray-400 rounded-[15px] border-[#505050] pr-12"
											/>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<button
														type="button"
														className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 border-none bg-transparent hover:bg-transparent focus:ring-0 flex items-center justify-center text-gray-500 hover:text-gray-700"
													>
														{discountType === "value" ? "R$" : "%"}
														<ChevronDown size={14} className="ml-0.5" />
													</button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end" className="w-20 bg-white  p-0 z-[9999]">
													<DropdownMenuItem
														className="flex items-center hover:bg-gray-100 gap-2 px-3 py-2 cursor-pointer"
														onClick={() => handleDiscountTypeChange("value")}
													>
														<span className="text-sm">R$</span>
													</DropdownMenuItem>
													<DropdownMenuItem
														className="flex items-center hover:bg-gray-100 gap-2 px-3 py-2 cursor-pointer"
														onClick={() => handleDiscountTypeChange("percentage")}
													>
														<span className="text-sm">%</span>
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
										{previewValue !== null && !isNaN(previewValue) && (
											<p className="text-sm text-gray-500 mt-1">
												{discountType === "percentage" ? (
													<>Valor: R$ {previewValue.toFixed(2).replace(".", ",")}</>
												) : (
													<>Porcentagem: {previewValue.toFixed(2).replace(".", ",")}%</>
												)}
											</p>
										)}
									</>
								)}
							/>
						</div>
					</div>
					<div className="flex justify-end gap-2 mt-2">
						<button
							type="button"
							onClick={toggleModal}
							className="bg-gray-200 hover:bg-gray-300 h-[45px] shadow-main gap-2 flex text-black px-3 py-1 items-center rounded-[15px]"
						>
							Cancelar
						</button>
						<button
							type="submit"
							disabled={isLoading}
							className="bg-mainColor shadow-main h-[45px] gap-2 flex text-white px-3 py-1 items-center rounded-[15px]"
						>
							{isLoading ? "Salvando..." : "Salvar"}
						</button>
					</div>
				</form>
			</OverlayContainer>
		</>
	);
}
