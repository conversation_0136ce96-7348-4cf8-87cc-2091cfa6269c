import { LucideIcon } from "lucide-react";
import "./styles.css";

interface TitleProps {
	title: string;
	icon?: LucideIcon;
}

export const TitleWithLine = ({ title, icon: Icon }: TitleProps) => {
	return (
		<div
			className="
		flex items-center gap-2 md:gap-4 p-2
		flex-nowrap whitespace-nowrap
	  "
		>
			<span
				className="
		  font-semibold text-[#505050]
		  md:text-base 
		  lg:text-xl
		  xl:text-2xl
		  2xl:text-3xl
		"
			>
				{title}
			</span>

			{Icon && (
				<Icon
					size={28}
					className="
			text-mainColor opacity-80 transition
			hover:opacity-100
		  "
				/>
			)}

			<hr
				className="
		  flex-1 border-0 h-[2px]
		  bg-gradient-to-r from-mainColor to-mainColor/40
		  rounded-full shadow-sm
		"
			/>
		</div>
	);
};
