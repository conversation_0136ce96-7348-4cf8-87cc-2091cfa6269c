export enum PaymentMethodEnum {
	Cash = 1,
	BankCheck = 2,
	CreditCard = 3,
	DebitCard = 4,
	BankTicket = 15,
	BankDeposit = 16,
	PIX = 17,
	BankTransfer = 18,
	Other = 99,
}

export const PaymentMethodData = [
	{ value: PaymentMethodEnum.Cash, label: "<PERSON><PERSON><PERSON>" },
	{ value: PaymentMethodEnum.BankCheck, label: "Cheque" },
	{ value: PaymentMethodEnum.CreditCard, label: "Crédito" },
	{ value: PaymentMethodEnum.DebitCard, label: "Debito" },
	{ value: PaymentMethodEnum.BankTicket, label: "Boleto" },
	{ value: PaymentMethodEnum.BankDeposit, label: "Deposito" },
	{ value: PaymentMethodEnum.PIX, label: "Pix" },
	{ value: PaymentMethodEnum.BankTransfer, label: "Transferência" },
	{ value: PaymentMethodEnum.Other, label: "Outro" },
];
