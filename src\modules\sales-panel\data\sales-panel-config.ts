import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { ScreenShare } from "lucide-react";
import { SALES_PANEL_ROUTES } from "./routes-path";

export const SALES_PANEL_CONFIG: IItemSidebar = {
	id: "sales-panel-group",
	moduleActivated: true,
	label: "Terminal",
	accessibleDescription: "ícone seção de vendas",
	Icon: ScreenShare,
	type: "config",
	path: SALES_PANEL_ROUTES.TERMINAL,
	subItems: [],
};
