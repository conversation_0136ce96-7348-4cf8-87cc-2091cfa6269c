export enum PersonClassificationEnum {
	CUSTOMER = 1,
	SUPPLIER = 2,
}

export enum PersonTypeEnum {
	Physical = 0,
	Legal = 1,
}

export interface CreatePersonDTO {
	type: PersonTypeEnum;
	name: string;
	cnpjCpf: string;
	email: string;
	phone: string;
	classifications: PersonClassificationEnum[];
	address: {
		street: string;
		number: string;
		complement: string;
		neighborhood: string;
		city: string;
		state: string;
		postalCode: string;
	};
	tradeName: string;
	ie: string;
}
