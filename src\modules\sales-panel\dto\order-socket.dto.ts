export interface IOrderSocketDto {
	customer: string;
	id: number;
	status: string;
	subtotal: string;
	shippingCost: number;
	discount: number;
	total: string;
	items: IItemSocket[];
	remainingValue: string;
	paidValue: string;
	cpfCnpj?: string;
	payments: IPaymentSocket[];
}

export interface IPaymentSocket {
	id: number;
	value: string;
	paymentMethod: string;
	parcel: number;
}

export interface IItemSocket {
	id: number;
	quantity: number;
	price: number;
	discount: number;
	discountType: "value" | "percentage";
	total: number;
	product: string;
}
