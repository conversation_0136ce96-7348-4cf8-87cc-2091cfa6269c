import { ApiResponse } from "@/shared/types/requests/requests.type";
import { useQuery } from "@tanstack/react-query";
import { IGetAllPendingsDto, IGetAllPendingsResponse } from "../../dto/get-all-pending.dto";
import { getAllPendingsRequest } from "../../services/request/get-pendings";

export const useFindAllPending = (filters: IGetAllPendingsDto) => {
	return useQuery<ApiResponse<IGetAllPendingsResponse>>({
		queryKey: ["pendings", filters],
		queryFn: () => getAllPendingsRequest(filters),
	});
};
