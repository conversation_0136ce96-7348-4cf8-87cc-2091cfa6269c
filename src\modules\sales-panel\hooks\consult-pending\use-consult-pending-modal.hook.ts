import { isNewOrder<PERSON>tom } from "@/modules/sales-panel/states/is-new-order.state";
import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useCallback, useEffect, useMemo } from "react";
import { usePagination } from "../../../../shared/hooks/utils/use-pagination.hook";
import { useFindAllPending } from "./find-all.hook";
import { useFilters } from "./use-filters.hook";

export const useConsultPendingModal = (isOpen: boolean) => {
	const queryClient = useQueryClient();
	const [currentOrderId, setCurrentOrderId] = useAtom(orderIdAtom);
	const [, setIsNewOrder] = useAtom(isNewOrderAtom);
	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange, resetPagination } = usePagination();
	const { form, filters } = useFilters();
	const debouncedFilters = useDebounce(filters, 300);

	const queryFilters = useMemo(
		() => ({
			page,
			limit: itemsPerPage,
			...debouncedFilters,
		}),
		[page, itemsPerPage, debouncedFilters]
	);

	const handleModalOpen = useCallback(() => {
		queryClient.invalidateQueries({ queryKey: ["pendings"] });
	}, [queryClient]);

	const handleModalClose = useCallback(() => {
		form.reset({
			customer: "",
			orderId: undefined,
		});
		resetPagination();
	}, [form, resetPagination]);

	useEffect(() => {
		const modalStateHandler = isOpen ? handleModalOpen : handleModalClose;
		modalStateHandler();
	}, [isOpen, handleModalOpen, handleModalClose]);

	const { data, isLoading } = useFindAllPending(queryFilters);

	const totalPages = useMemo(() => (data?.success ? Math.ceil(data.data.total / itemsPerPage) : 0), [data, itemsPerPage]);

	const handleSelectOrder = useCallback(
		(orderId: number) => {
			setIsNewOrder(false);
			setCurrentOrderId(orderId);
		},
		[setCurrentOrderId, setIsNewOrder]
	);

	return {
		data,
		isLoading,
		currentOrderId,
		page,
		itemsPerPage,
		totalPages,
		form,
		handlePageChange,
		handleItemsPerPageChange,
		handleSelectOrder,
	};
};
