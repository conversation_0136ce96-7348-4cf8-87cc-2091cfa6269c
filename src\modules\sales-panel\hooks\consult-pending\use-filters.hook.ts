import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { ConsultPendingFormData, consultPendingSchema } from "../../validators/consult-pending.validator";

export const useFilters = () => {
	const form = useForm<ConsultPendingFormData>({
		resolver: zodResolver(consultPendingSchema),
		defaultValues: {
			customer: "",
			orderId: undefined,
		},
	});

	return {
		form,
		filters: form.watch(),
	};
};
