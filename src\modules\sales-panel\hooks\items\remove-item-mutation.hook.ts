import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { removeItemRequest } from "../../services/request/remove-item";

export const useRemoveItem = () => {
	const removeItemMutation = useMutation({
		mutationKey: ["remove-item"],
		mutationFn: async ({ orderId, itemId }: { orderId: number; itemId: number }) => {
			const response = await removeItemRequest({ orderId, itemId });

			if (response.success) return response.data;

			throw new Error(response.data.message);
		},
		onError: error => toast.error(error.message),
	});

	return { removeItemMutation };
};
