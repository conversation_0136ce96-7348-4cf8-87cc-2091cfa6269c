import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { IUpdateItemForm, updateItemFormSchema } from "../../validators/update-item.form";

export const useUpdateItemForm = () => {
	return useForm<IUpdateItemForm>({
		resolver: zodResolver(updateItemFormSchema),
		defaultValues: {
			quantity: undefined,
			discount: undefined,
			discountType: "value",
		},
		mode: "onSubmit",
	});
};
