import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { IUpdateItemDto } from "../../dto/update-item-dto";
import { updateItemOrderRequest } from "../../services/request/update-product";

export const useUpdateItem = () => {
	const updateItemMutation = useMutation({
		mutationKey: ["update-item"],
		mutationFn: async ({ orderId, item }: { orderId: number; item: IUpdateItemDto }) => {
			const response = await updateItemOrderRequest({ orderId, item });

			if (response.success) return response.data;

			throw new Error(response.data.message);
		},
		onError: error => toast.error(error.message),
	});

	return { updateItemMutation };
};
