import { useUpdateOrderMutation } from "@/modules/sales-panel/hooks/order/update-order-mutation.hook";
import { cpfCnpjState, openModalCpfAtom } from "@/modules/sales-panel/states/cpf-modal-open.state";
import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
import { useAtom } from "jotai";
import { ChangeEvent, FormEvent } from "react";

interface ModalCpfState {
	isOpen: boolean;
	type: "add" | "auto";
}

interface UseCpfModalReturn {
	cpf: string;
	handleSubmit: (event: FormEvent<HTMLFormElement>) => void;
	closeModal: () => void;
	modalCpf: ModalCpfState | null;
	handleCpfChange: (e: ChangeEvent<HTMLInputElement>) => void;
}

const useCpfField = () => {
	const [cpf, setCpf] = useAtom(cpfCnpjState);

	const handleCpfChange = (e: ChangeEvent<HTMLInputElement>) => {
		const oldValue = cpf;
		const numericValue = e.target.value.replace(/\D/g, "");
		const cursorPosition = e.target.selectionStart ?? 0;
		const nonNumericBeforeCursor = (oldValue?.substring(0, cursorPosition).match(/\D/g) || []).length;
		const maskedValue = cpfCnpjMask(numericValue);
		setCpf(maskedValue);
		const adjustment = (maskedValue.substring(0, cursorPosition).match(/\D/g) || []).length - nonNumericBeforeCursor;
		const newCursorPosition = cursorPosition + adjustment;
		requestAnimationFrame(() => {
			e.target.setSelectionRange(newCursorPosition, newCursorPosition);
		});
	};

	return { cpf, handleCpfChange };
};

export const useCpfModal = (orderId: number): UseCpfModalReturn => {
	const [modalCpf, setModalCpf] = useAtom(openModalCpfAtom);
	const { cpf, handleCpfChange } = useCpfField();
	const [, setCpf] = useAtom(cpfCnpjState);
	const { updateOrderMutation } = useUpdateOrderMutation();

	const closeModal = () => {
		setModalCpf(null);
		setCpf(null);
	};

	const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
		event.preventDefault();

		updateOrderMutation.mutate({ orderId, item: { cpfCnpj: cpf?.replace(/\D/g, "") } }, { onSuccess: closeModal });
	};

	return { cpf: cpf ?? "", handleSubmit, closeModal, modalCpf, handleCpfChange };
};
