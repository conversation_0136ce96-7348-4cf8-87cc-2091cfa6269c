import { useMutation } from "@tanstack/react-query";
import { useAtom } from "jotai";

import { toast } from "sonner";
import { deleteOrderRequest } from "../../services/request/delete-order";
import { orderIdAtom } from "../../states/order-id.state";

export const useCancelOrder = () => {
	const [orderId, setOrderId] = useAtom(orderIdAtom);

	const cancelOrderMutation = useMutation({
		mutationKey: ["cancelOrder"],
		mutationFn: async () => deleteOrderRequest(orderId!),
		onMutate: () => {
			const toastId = toast.loading("Cancelando pedido...");
			return { toastId };
		},
		onSuccess: (_, __, context) => {
			if (context?.toastId) {
				toast.success("Pedido cancelado com sucesso!", { id: context.toastId });
				setOrderId(null);
			}
		},
		onError: (_, __, context) => {
			if (context?.toastId) {
				toast.error("Falha ao cancelar o pedido.", { id: context.toastId });
			}
		},
	});

	return {
		cancelOrderMutation,
	};
};
