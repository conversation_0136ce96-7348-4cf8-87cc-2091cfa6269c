import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { useSetAtom } from "jotai";
import { finishOrderRequest } from "../../services/request/finish-order";
import { fetchCouponByOrderId } from "../../services/request/get-coupon";
import { couponState } from "../../states/coupon.state";
import { orderIdAtom } from "../../states/order-id.state";

type UseFinishOrderAndCouponResult = {
	finishOrderAndShowCoupon: (params: { orderId: number }) => void;
	isLoading: boolean;
};

export function useFinishOrderAndCoupon(): UseFinishOrderAndCouponResult {
	const setCoupon = useSetAtom(couponState);
	const setOrderId = useSetAtom(orderIdAtom);

	const finishOrderMutation = useMutation({
		mutationKey: ["finish-order-and-coupon"],
		mutationFn: async ({ orderId }: { orderId: number }) => {
			try {
				toast.dismiss();
				toast.loading("Finalizando pedido...");

				const finishResponse = await finishOrderRequest({ orderId });

				if (!finishResponse.success) {
					throw new Error(`FINISH_ORDER_ERROR:${finishResponse.data.message}`);
				}

				toast.dismiss();
				toast.loading("Buscando cupom fiscal...");

				const couponResponse = await fetchCouponByOrderId(orderId);

				if (!couponResponse.success) {
					throw new Error(`COUPON_ERROR:${couponResponse.data.message}`);
				}

				return couponResponse.data;
			} catch (error) {
				toast.dismiss();
				throw error;
			}
		},
		onSuccess: (data: File) => {
			toast.dismiss();
			toast.success("Pedido finalizado com sucesso! 🎉");

			setOrderId(null);
			setCoupon(data);
		},
		onError: (error: unknown) => {
			toast.dismiss();

			if (error instanceof Error) {
				const errorMessage = error.message;
				if (errorMessage.startsWith("FINISH_ORDER_ERROR:")) {
					toast.error(`Erro ao finalizar pedido: ${errorMessage.replace("FINISH_ORDER_ERROR:", "")}`);
				} else if (errorMessage.startsWith("COUPON_ERROR:")) {
					toast.error(`Erro ao buscar cupom fiscal: ${errorMessage.replace("COUPON_ERROR:", "")}`);
				} else {
					toast.error(errorMessage);
				}
			} else {
				toast.error("Ocorreu um erro inesperado");
			}
		},
	});

	return {
		finishOrderAndShowCoupon: (params: { orderId: number }) => {
			finishOrderMutation.mutateAsync(params);
		},
		isLoading: finishOrderMutation.isPending,
	};
}
