import { useAtomValue } from "jotai";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { useAddProduct } from "../product/use-add-product.hook";
import { useCreateOrder } from "./use-create-order.hook";

export const useGlobalBarcodeScanner = () => {
	const orderId = useAtomValue(orderIdAtom);
	const { createOrderMutation } = useCreateOrder();
	const addProduct = useAddProduct();
	const barcodeBuffer = useRef<string>("");
	const lastKeyTime = useRef<number>(0);
	const [isProcessing, setIsProcessing] = useState(false);

	const resetBuffer = () => {
		barcodeBuffer.current = "";
	};

	const processBarcode = useCallback(
		async (barcode: string) => {
			if (isProcessing) return;

			if (!/^\d{8,}$/.test(barcode)) {
				toast.error("Código de barras inválido");
				return;
			}

			setIsProcessing(true);
			try {
				let currentOrderId = orderId;
				if (!currentOrderId) {
					const newOrder = await createOrderMutation.mutateAsync();
					currentOrderId = newOrder?.data.id;
					if (!currentOrderId) {
						throw new Error("Falha ao iniciar o pedido");
					}
				}
				await addProduct.addProductMutation.mutateAsync({
					orderId: currentOrderId,
					item: { barcode, quantity: 1 },
				});
				toast.success("Produto adicionado com sucesso");
			} catch (error) {
				console.error("Erro ao processar código de barras:", error);
				toast.error(error instanceof Error ? error.message : "Erro ao processar o código de barras");
			} finally {
				setIsProcessing(false);
			}
		},
		[orderId, createOrderMutation, addProduct, isProcessing]
	);

	const handleKeyPress = useCallback(
		(event: KeyboardEvent) => {
			const target = event.target as HTMLElement;
			const now = Date.now();

			if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
				return;
			}

			if (now - lastKeyTime.current > 100) {
				resetBuffer();
			}
			lastKeyTime.current = now;

			if (/^\d$/.test(event.key)) {
				barcodeBuffer.current += event.key;
			} else if (event.key === "Enter" && barcodeBuffer.current) {
				event.preventDefault();
				processBarcode(barcodeBuffer.current);
				resetBuffer();
			}
		},
		[processBarcode]
	);

	const handlePaste = useCallback(
		(event: ClipboardEvent) => {
			const target = event.target as HTMLElement;
			if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
				return;
			}

			const clipboardData = event.clipboardData;
			if (!clipboardData) return;

			const pastedText = clipboardData.getData("text").trim();
			if (pastedText) {
				event.preventDefault();
				processBarcode(pastedText);
			}
		},
		[processBarcode]
	);

	useEffect(() => {
		document.addEventListener("keypress", handleKeyPress);
		document.addEventListener("paste", handlePaste);
		return () => {
			document.removeEventListener("keypress", handleKeyPress);
			document.removeEventListener("paste", handlePaste);
		};
	}, [handleKeyPress, handlePaste]);
};
