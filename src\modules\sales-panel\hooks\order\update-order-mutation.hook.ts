import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { IUpdateOrderDto } from "../../dto/update-order.dto";
import { updateOrderRequest } from "../../services/request/update-order";

export const useUpdateOrderMutation = () => {
	const updateOrderMutation = useMutation({
		mutationKey: ["update-order"],
		mutationFn: async ({ orderId, item }: { orderId: number; item: Partial<IUpdateOrderDto> }) => {
			const response = await updateOrderRequest({ orderId, item });
			if (response.success) {
				return response.data;
			}
			throw new Error(response.data.message);
		},

		onMutate: () => {
			const pendingToastId = toast.loading("Atualizando pedido...");
			return { pendingToastId };
		},
		onSuccess: data => {
			toast.dismiss();
			toast.success(data.message);
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	return { updateOrderMutation };
};
