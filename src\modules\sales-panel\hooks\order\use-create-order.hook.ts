import { useMutation } from "@tanstack/react-query";
import { useSet<PERSON>tom } from "jotai";
import { toast } from "sonner";
import { ICreateOrderDto, createOrderRequest } from "../../services/request/create-order";

import { isNewOrderAtom } from "../../states/is-new-order.state";
import { orderIdAtom } from "../../states/order-id.state";

export const useCreateOrder = () => {
	const setOrderId = useSetAtom(orderIdAtom);
	const setIsNewOrder = useSetAtom(isNewOrderAtom);

	const createOrderMutation = useMutation<ICreateOrderDto, Error, void>({
		mutationFn: async () => {
			toast.dismiss();
			toast.loading("Criando pedido...");
			const response = await createOrderRequest();
			if (response.success) return response.data;
			throw new Error(response.data.message);
		},
		onError: error => {
			toast.dismiss();
			toast.error(error.message);
		},
		onSuccess: async data => {
			setIsNewOrder(true);
			setOrderId(data.data.id);
			toast.dismiss();
			toast.success(data.message);
		},
	});

	return { createOrderMutation };
};
