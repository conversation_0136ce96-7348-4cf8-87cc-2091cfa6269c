import { useAtom, useSet<PERSON>tom } from "jotai";
import Cookies from "js-cookie";
import { useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";
import { IOrderSocketDto } from "../../dto/order-socket.dto";
import { SOCKET_ORDER_CONNECTION } from "../../services/endpoints";
import { cpfCnpjState, openModalCpfAtom } from "../../states/cpf-modal-open.state";
import { isNewOrderAtom } from "../../states/is-new-order.state";
import { orderIdAtom } from "../../states/order-id.state";
import { orderInfoAtom } from "../../states/order-info.state";

export const useOrderSocket = () => {
	const [orderId, setOrderId] = useAtom(orderIdAtom);
	const [isNewOrder, setIsNewOrder] = useAtom(isNewOrderAtom);
	const setOrderInfo = useSetAtom(orderInfoAtom);
	const setModalCpf = useSetAtom(openModalCpfAtom);
	const setCpfState = useSetAtom(cpfCnpjState);
	const socketRef = useRef<Socket | null>(null);
	const [isPendingOrder, setIsPendingOrder] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	useEffect(() => {
		if (!orderId) return;

		setIsLoading(true);

		if (orderId) {
			setIsPendingOrder(true);
			const socketInstance = io(SOCKET_ORDER_CONNECTION.CONNECT({ orderId }), {
				transports: ["websocket"],
				auth: {
					token: Cookies.get("access_token"),
				},
				reconnection: true,
				reconnectionAttempts: 5,
				reconnectionDelay: 1000,
			});

			socketRef.current = socketInstance;

			socketInstance.on("connect", () => {
				setIsLoading(false);
				if (isNewOrder) {
					setModalCpf({ isOpen: true, type: "auto" });
					setIsNewOrder(false);
				}
			});

			socketInstance.on("connect_error", () => {
				setOrderId(null);
				setCpfState(null);
				setIsNewOrder(false);

				setTimeout(() => {
					if (socketRef.current) {
						socketRef.current.connect();
					}
				}, 2000);
			});

			socketInstance.on("orderUpdated", (event: IOrderSocketDto) => {
				if (event) {
					setOrderInfo(event);
				}
			});

			socketInstance.on("disconnect", () => {
				setOrderInfo(null);
				if (!isPendingOrder) {
					setOrderId(null);
					setCpfState(null);
					setIsNewOrder(false);
				}
			});

			return () => {
				if (socketRef.current) {
					socketRef.current.disconnect();
					socketRef.current = null;
				}
				setIsPendingOrder(false);
				setIsLoading(false);
			};
		}

		return undefined;
	}, [orderId, setOrderInfo, setOrderId, setModalCpf, setCpfState, isPendingOrder, isNewOrder, setIsNewOrder]);

	return { isLoading };
};
