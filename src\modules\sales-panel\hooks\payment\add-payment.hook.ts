import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { IAddPaymentDto } from "../../dto/add-paymet.dto";
import { addPaymentRequest } from "../../services/request/add-payment";

export const useAddPayment = () => {
	const addPaymentMutation = useMutation({
		mutationKey: ["add-payment"],
		mutationFn: async ({ orderId, item }: { orderId: number; item: IAddPaymentDto }) => {
			const response = await addPaymentRequest({ orderId, item });
			if (response.success) return response.data;
			throw new Error(response.data.message);
		},
		onError: error => toast.error(error.message),
	});

	return { addPaymentMutation };
};
