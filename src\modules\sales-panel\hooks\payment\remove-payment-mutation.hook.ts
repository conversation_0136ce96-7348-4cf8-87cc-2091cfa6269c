import { useMutation } from "@tanstack/react-query";
import { removePaymentRequest } from "../../services/request/remove-payment";

export const useRemovePayment = () => {
	const removePaymentMutation = useMutation({
		mutationKey: ["remove-payment"],
		mutationFn: async ({ orderId, paymentId }: { orderId: number; paymentId: number }) => {
			const response = await removePaymentRequest({ orderId, paymentId });

			if (response.success) return response.data;

			throw new Error(response.data.message);
		},
	});

	return { removePaymentMutation };
};
