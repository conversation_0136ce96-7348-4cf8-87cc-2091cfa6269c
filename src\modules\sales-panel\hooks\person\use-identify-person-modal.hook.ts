import { useCreatePerson, useCreatePersonMutation } from "@/modules/person";
import { PersonTypeEnum } from "@/modules/person/enums/person-classification.enum";

import { useUpdateOrderMutation } from "@/modules/sales-panel/hooks/order/update-order-mutation.hook";
import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { useAtomValue } from "jotai";
import { useEffect, useState } from "react";

type ModalMode = "select" | "create" | "search";

interface UseIdentifyPersonModalProps {
	isOpen: boolean;
	onClose: () => void;
}

export const useIdentifyPersonModal = ({ isOpen, onClose }: UseIdentifyPersonModalProps) => {
	const orderId = useAtomValue(orderIdAtom);
	const orderInfo = useAtomValue(orderInfoAtom);
	const { updateOrderMutation } = useUpdateOrderMutation();
	const [isCnpj, setIsCnpj] = useState(false);
	const [mode, setMode] = useState<ModalMode>("select");

	const { createPerson, isLoading } = useCreatePersonMutation({
		closeModal: () => {
			onClose();
			form.reset();
			setMode("select");
			setIsCnpj(false);
		},
	});

	const { form, onSubmit } = useCreatePerson({
		createPerson: async data => {
			const response = await createPerson(data);

			if (response && orderId) {
				updateOrderMutation.mutate({
					orderId,
					item: { customerId: response.data },
				});
			}
			return response;
		},
	});

	const {
		register,
		handleSubmit,
		control,
		formState: { errors },
		setValue,
	} = form;

	const formattedOrderInfo = orderInfo
		? {
				customer: orderInfo.customer,
				total: orderInfo.total,
			}
		: undefined;

	useEffect(() => {
		if (isOpen) {
			setMode("select");
		} else {
			form.reset();
			setIsCnpj(false);
		}
	}, [isOpen, form]);

	useEffect(() => {
		if (isCnpj) {
			setValue("type", PersonTypeEnum.Legal);
		} else {
			setValue("type", PersonTypeEnum.Physical);
		}
	}, [isCnpj, setValue]);

	return {
		mode,
		setMode,
		isCnpj,
		setIsCnpj,
		isLoading,
		register,
		handleSubmit,
		control,
		errors,
		formattedOrderInfo,
		onSubmit,
	};
};
