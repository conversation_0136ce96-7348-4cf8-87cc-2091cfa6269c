import { useFindAllCustomers } from "@/modules/person/hooks/find-all-customers.hook";
import { customersFilterSchema } from "@/modules/sales-panel/validators/customers-filter.form";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

interface UseSearchCustomerProps {
	onClose: () => void;
}

export const useSearchCustomer = ({ onClose }: UseSearchCustomerProps) => {
	const methods = useForm({
		resolver: zodResolver(customersFilterSchema),
		defaultValues: {
			name: "",
			document: "",
		},
	});

	const debounceName = useDebounce(methods.watch("name"), 500);
	const debounceDocument = useDebounce(methods.watch("document"), 500);

	const [page, setPage] = useState(1);
	const [itemsPerPage, setItemsPerPage] = useState(10);

	const onPageChange = (newPage: number) => {
		setPage(newPage);
	};

	const onItemsPerPageChange = (newItemsPerPage: number) => {
		setItemsPerPage(newItemsPerPage);
		setPage(1);
	};

	const resetFilters = () => {
		setPage(1);
		setItemsPerPage(10);
	};

	const { data: response, isLoading } = useFindAllCustomers({
		page,
		limit: itemsPerPage,
		...(debounceName ? { name: debounceName } : { document: debounceDocument }),
	});

	return {
		methods,
		response,
		isLoading,
		onClose,
		page,
		itemsPerPage,
		onPageChange,
		onItemsPerPageChange,
		resetFilters,
	};
};
