import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { IAddProductDto } from "../../dto/add-product.dto";
import { addProductRequest } from "../../services/request/add-product";

export const useAddProduct = () => {
	const addProductMutation = useMutation({
		mutationKey: ["add-product"],
		mutationFn: async ({ orderId, item }: { orderId: number; item: IAddProductDto }) => {
			const response = await addProductRequest({ orderId, item });

			if (response.success) return response.data;

			throw new Error(response.data.message);
		},
		onError: error => toast.error(error.message),
	});

	return { addProductMutation };
};
