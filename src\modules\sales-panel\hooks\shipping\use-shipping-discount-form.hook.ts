import { zodResolver } from "@hookform/resolvers/zod";
import { useAtomValue } from "jotai";
import { useForm } from "react-hook-form";
import { orderIdAtom } from "../../states/order-id.state";
import { orderInfoAtom } from "../../states/order-info.state";
import { IShippingDiscountForm, shippingDiscountFormSchema } from "../../validators/shipping-discount.form";
import { useUpdateOrderMutation } from "../order/update-order-mutation.hook";

export const useShippingDiscountForm = () => {
	const orderId = useAtomValue(orderIdAtom);
	const orderInfo = useAtomValue(orderInfoAtom);

	const { updateOrderMutation } = useUpdateOrderMutation();

	const form = useForm<IShippingDiscountForm>({
		resolver: zodResolver(shippingDiscountFormSchema),
		defaultValues: {
			shippingCost: "",
			discount: "",
			discountType: "value",
		},
	});

	const calculatePreview = (value: string, type: "value" | "percentage") => {
		if (!value || !orderInfo?.subtotal) return null;

		const numericValue = Number(value.replace(",", "."));
		if (isNaN(numericValue) || numericValue <= 0) return null;

		const subtotal = Number(orderInfo.subtotal);

		if (isNaN(subtotal) || subtotal <= 0) return null;
		if (type === "percentage") {
			return (numericValue / 100) * subtotal;
		} else {
			return (numericValue / subtotal) * 100;
		}
	};

	const onSubmit = (data: IShippingDiscountForm) => {
		if (!orderId) return;

		let discountValue: number | undefined;
		if (data.discount) {
			if (data.discountType === "percentage") {
				const percentage = Number(data.discount.replace(",", "."));
				discountValue = (percentage / 100) * Number(orderInfo?.subtotal || 0);
			} else {
				discountValue = Number(data.discount.replace(",", "."));
			}
		}

		updateOrderMutation.mutate({
			orderId,
			item: {
				shippingCost: data.shippingCost ? Number(data.shippingCost.replace(",", ".")) : undefined,
				discount: discountValue,
			},
		});

		form.reset();
	};

	return {
		form,
		onSubmit,
		isLoading: updateOrderMutation.isPending,
		calculatePreview,
	};
};
