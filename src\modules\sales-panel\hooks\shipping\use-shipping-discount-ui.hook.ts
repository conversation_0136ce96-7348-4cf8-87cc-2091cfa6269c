import { useModal } from "@/shared/hooks/utils/modal.hook";
import { useAtomValue } from "jotai";
import { useEffect } from "react";
import { orderInfoAtom } from "../../states/order-info.state";
import { useShippingDiscountForm } from "./use-shipping-discount-form.hook";

export const useShippingDiscountUI = () => {
	const { isOpen, toggleModal } = useModal();
	const { form, onSubmit, isLoading, calculatePreview } = useShippingDiscountForm();
	const orderInfo = useAtomValue(orderInfoAtom);

	useEffect(() => {
		if (!isOpen) {
			form.reset({
				shippingCost: "",
				discount: "",
				discountType: "value",
			});
		} else if (orderInfo) {
			form.reset({
				shippingCost: orderInfo.shippingCost > 0 ? orderInfo.shippingCost.toString() : "",
				discount: orderInfo.discount > 0 ? orderInfo.discount.toString() : "",
				discountType: "value",
			});
		}
	}, [isOpen, orderInfo, form]);

	return {
		isOpen,
		toggleModal,
		form,
		onSubmit,
		isLoading,
		calculatePreview,
	};
};
