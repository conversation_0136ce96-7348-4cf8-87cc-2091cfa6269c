// import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
// import { toast } from "sonner";

// interface UseTerminalShortcutsProps {
// 	onCreateOrder: () => void;
// 	onCancelOrder: () => void;
// 	onIdentifyPerson: () => void;
// 	onLinkCpf: () => void;
// 	onConsultPending: () => void;
// 	orderId: number | null;
// }

// const createShortcutHandler = (handler: () => void, condition: boolean, disabledMessage: string) => {
// 	return {
// 		handler,
// 		disabled: !condition,
// 		fallback: () => {
// 			if (!condition) {
// 				toast.dismiss();
// 				toast.error(disabledMessage);
// 			}
// 		},
// 	};
// };

// export const useTerminalShortcuts = ({
// 	onCreateOrder,
// 	onCancelOrder,
// 	onIdentifyPerson,
// 	onLinkCpf,
// 	onConsultPending,
// 	orderId,
// }: UseTerminalShortcutsProps) => {
// 	const createOrderShortcut = createShortcutHandler(onCreateOrder, !orderId, "Não é possível criar um novo pedido enquanto há um pedido em aberto");
// 	const cancelOrderShortcut = createShortcutHandler(onCancelOrder, !!orderId, "Não há pedido em aberto para cancelar");
// 	const identifyPersonShortcut = createShortcutHandler(onIdentifyPerson, !!orderId, "Não há pedido em aberto para identificar pessoa");
// 	const linkCpfShortcut = createShortcutHandler(onLinkCpf, !!orderId, "Não há pedido em aberto para vincular CPF");
// 	const consultPendingShortcut = createShortcutHandler(onConsultPending, true, "");

// 	useKeyboardShortcut({
// 		combination: { key: "a" },
// 		handler: createOrderShortcut.handler,
// 		options: {
// 			ignoreInputs: true,
// 			preventDefault: true,
// 			disabled: createOrderShortcut.disabled || !!orderId,
// 			fallback: createOrderShortcut.fallback,
// 		},
// 	});

// 	useKeyboardShortcut({
// 		combination: { key: "c" },
// 		handler: cancelOrderShortcut.handler,
// 		options: {
// 			ignoreInputs: true,
// 			preventDefault: true,
// 			disabled: cancelOrderShortcut.disabled,
// 			fallback: cancelOrderShortcut.fallback,
// 		},
// 	});

// 	useKeyboardShortcut({
// 		combination: { key: "i" },
// 		handler: identifyPersonShortcut.handler,
// 		options: {
// 			ignoreInputs: true,
// 			preventDefault: true,
// 			disabled: identifyPersonShortcut.disabled,
// 			fallback: identifyPersonShortcut.fallback,
// 		},
// 	});

// 	useKeyboardShortcut({
// 		combination: { key: "n" },
// 		handler: linkCpfShortcut.handler,
// 		options: {
// 			ignoreInputs: true,
// 			preventDefault: true,
// 			disabled: linkCpfShortcut.disabled,
// 			fallback: linkCpfShortcut.fallback,
// 		},
// 	});

// 	useKeyboardShortcut({
// 		combination: { key: "p" },
// 		handler: consultPendingShortcut.handler,
// 		options: {
// 			ignoreInputs: true,
// 			preventDefault: true,
// 			disabled: consultPendingShortcut.disabled,
// 			fallback: consultPendingShortcut.fallback,
// 		},
// 	});
// };
