import { LoadingOverlay } from "@/shared/components/overlay/loading-overlay";
import { useAtomValue } from "jotai";
import { SalesBackgroundLayout } from "../components/container/background";
import { TerminalGrid } from "../components/terminal";
import { TerminalHeaderDesktop } from "../components/terminal/header";
import { ItemsMenuMobile } from "../components/terminal/header/mobile/items-menu";
import { TerminalMobileHeader } from "../components/terminal/header/mobile/terminal-header-mobile";
import { PdfModalCoupon } from "../components/terminal/resume/pdf-coupon";
import { useCancelOrder } from "../hooks/order/cancel-order.hook";
import { useGlobalBarcodeScanner } from "../hooks/order/global-barcode-scanner.hook";
import { useCreateOrder } from "../hooks/order/use-create-order.hook";
import { useOrderSocket } from "../hooks/order/use-order-socket.hook";
import { orderIdAtom } from "../states/order-id.state";

export const TerminalScreen: React.FC = () => {
	const { isLoading } = useOrderSocket();
	useGlobalBarcodeScanner();

	const { createOrderMutation } = useCreateOrder();
	const { cancelOrderMutation } = useCancelOrder();
	const orderId = useAtomValue(orderIdAtom);

	return (
		<SalesBackgroundLayout>
			<LoadingOverlay isVisible={isLoading} />
			<div className="flex min-h-screen lg:h-auto w-full flex-col gap-5 px-3 py-3 lg:px-10 md:py-2">
				<TerminalMobileHeader orderId={orderId} createOrderMutation={createOrderMutation} />
				<TerminalHeaderDesktop createOrderMutation={createOrderMutation} cancelOrderMutation={cancelOrderMutation} orderId={orderId} />
				<TerminalGrid />
				<ItemsMenuMobile cancelOrderMutation={cancelOrderMutation} orderId={orderId} />
				<PdfModalCoupon />
			</div>
		</SalesBackgroundLayout>
	);
};
