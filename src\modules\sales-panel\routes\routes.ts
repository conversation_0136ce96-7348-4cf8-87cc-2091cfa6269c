import { SALES_PANEL_CONFIG } from "../data/sales-panel-config";

import { rootRoute } from "@/shared/routes/config";
import { ModuleSubRoute, createModuleRoutes } from "@/shared/routes/module-routes.factory";
import { TerminalScreen } from "../pages/terminal";

const salesSubRoutes: ModuleSubRoute<{ id: string; path: string }>[] = [{ id: SALES_PANEL_CONFIG.id, component: TerminalScreen }];

export const salesPanelRoutes = createModuleRoutes(
	{
		...SALES_PANEL_CONFIG,
		subItems: [{ id: SALES_PANEL_CONFIG.id, path: SALES_PANEL_CONFIG.path }],
	},
	salesSubRoutes,
	() => rootRoute
);
