import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IAddPaymentDto } from "../../dto/add-paymet.dto";
import { SALES_PANEL_PATHS } from "../endpoints";

export const addPaymentRequest = async ({ orderId, item }: { orderId: number; item: IAddPaymentDto }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest<IGlobalMessageReturn>({
		method: "POST",
		path: SALES_PANEL_PATHS.ADD_PAYMENT({ orderId }),
		body: item,
	});
};
