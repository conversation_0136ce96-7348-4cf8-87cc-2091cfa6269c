import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IAddProductDto } from "../../dto/add-product.dto";
import { SALES_PANEL_PATHS } from "../endpoints";

export const addProductRequest = async ({ orderId, item }: { orderId: number; item: IAddProductDto }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest<IGlobalMessageReturn>({
		method: "PATCH",
		path: SALES_PANEL_PATHS.ADD_PRODUCT({ orderId }),
		body: item,
	});
};
