import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { SALES_PANEL_PATHS } from "../endpoints";

export interface ICreateOrderDto {
	data: {
		id: number;
	};
	message: string;
}

export const createOrderRequest = async (): Promise<ApiResponse<ICreateOrderDto>> => {
	return createRequest<ICreateOrderDto>({
		method: "POST",
		path: SALES_PANEL_PATHS.CREATE_ORDER,
	});
};
