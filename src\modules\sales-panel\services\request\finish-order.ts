import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { SALES_PANEL_PATHS } from "../endpoints";

export const finishOrderRequest = async ({ orderId }: { orderId: number }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest<IGlobalMessageReturn>({
		method: "PATCH",
		path: SALES_PANEL_PATHS.FINISH_ORDER({ orderId }),
	});
};
