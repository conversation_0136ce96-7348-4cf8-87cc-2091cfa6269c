import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { SALES_PANEL_PATHS } from "../endpoints";

export const fetchCouponByOrderId = async (orderId: number): Promise<ApiResponse<File>> => {
	return createRequest({
		path: SALES_PANEL_PATHS.GET_FISCAL_CUPOM({ orderId }),
		method: "GET",
		headers: {
			"Content-Type": "application/pdf",
			Accept: "application/pdf",
		},
		responseType: "blob",
	});
};
