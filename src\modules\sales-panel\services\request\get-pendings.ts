import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IGetAllPendingsDto, IGetAllPendingsResponse } from "../../dto/get-all-pending.dto";
import { SALES_PANEL_PATHS } from "../endpoints";

export const getAllPendingsRequest = async (filters: IGetAllPendingsDto): Promise<ApiResponse<IGetAllPendingsResponse>> => {
	return createRequest<IGetAllPendingsResponse>({
		path: SALES_PANEL_PATHS.GET_ALL_PENDINGS({
			page: filters.page,
			limit: filters.limit,
			orderId: filters.orderId ?? 0,
			customer: filters.customer ?? "",
		}),
		method: "GET",
	});
};
