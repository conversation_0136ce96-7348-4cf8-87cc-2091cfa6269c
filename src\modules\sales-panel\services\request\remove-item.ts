import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { SALES_PANEL_PATHS } from "../endpoints";

export const removeItemRequest = async ({ orderId, itemId }: { orderId: number; itemId: number }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest<IGlobalMessageReturn>({
		method: "DELETE",
		path: SALES_PANEL_PATHS.REMOVE_ITEM({ orderId, itemId }),
	});
};
