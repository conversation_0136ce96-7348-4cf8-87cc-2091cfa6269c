import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { SALES_PANEL_PATHS } from "../endpoints";

export const removePaymentRequest = async ({
	orderId,
	paymentId,
}: {
	orderId: number;
	paymentId: number;
}): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest<IGlobalMessageReturn>({
		method: "DELETE",
		path: SALES_PANEL_PATHS.REMOVE_PAYMENT({ orderId, paymentId }),
	});
};
