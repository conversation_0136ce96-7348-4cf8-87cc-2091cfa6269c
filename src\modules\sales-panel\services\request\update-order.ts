import { createRequest } from "@/shared/lib/create-request.lib";
import { omitUndefinedFields } from "@/shared/lib/utils";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IUpdateOrderDto } from "../../dto/update-order.dto";
import { SALES_PANEL_PATHS } from "../endpoints";

export const updateOrderRequest = async ({
	orderId,
	item,
}: {
	orderId: number;
	item: Partial<IUpdateOrderDto>;
}): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const body = omitUndefinedFields(item);

	return createRequest<IGlobalMessageReturn>({
		method: "PATCH",
		path: SALES_PANEL_PATHS.UPDATE_ORDER({ orderId }),
		body: body,
	});
};
