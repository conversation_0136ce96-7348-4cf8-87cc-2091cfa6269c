import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IUpdateItemDto } from "../../dto/update-item-dto";
import { SALES_PANEL_PATHS } from "../endpoints";

export const updateItemOrderRequest = async ({
	orderId,
	item,
}: {
	orderId: number;
	item: IUpdateItemDto;
}): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest<IGlobalMessageReturn>({
		method: "PATCH",
		path: SALES_PANEL_PATHS.UPDATE_ITEM({ orderId }),
		body: item,
	});
};
