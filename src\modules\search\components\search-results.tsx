import { Link } from "@tanstack/react-router";
import React from "react";
import useSearch from "../hooks/search.hook";

const SearchResults: React.FC = () => {
	const { results } = useSearch();

	if (results.length === 0) {
		return <div>Nenhum resultado encontrado.</div>;
	}

	return (
		<ul className="search-results">
			{results.map(result => (
				<li key={result.id}>
					<Link to={result.route}>
						<h3>{result.title}</h3>
						<p>{result.description}</p>
					</Link>
				</li>
			))}
		</ul>
	);
};
export default SearchResults;
