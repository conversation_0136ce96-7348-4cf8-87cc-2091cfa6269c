// src/hooks/useSearch.ts

import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { useAtom, useAtomValue } from "jotai";
import { searchResultsAtom } from "../states/search-item.state";
import { searchQueryAtom } from "../states/search-query.state";

const useSearch = () => {
	const [query, setQuery] = useAtom(searchQueryAtom);
	const debouncedQuery = useDebounce(query, 300);
	const results = useAtomValue(searchResultsAtom);

	return {
		query,
		setQuery,
		debouncedQuery,
		results,
	};
};

export default useSearch;
