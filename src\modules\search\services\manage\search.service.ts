// src/services/searchService.ts

import Fuse, { IFuseOptions } from "fuse.js";
import { ISearchItem } from "../../types/search.type";

class SearchService {
	private fuse: Fuse<ISearchItem>;

	constructor(items: ISearchItem[]) {
		const options: IFuseOptions<ISearchItem> = {
			keys: ["title", "description", "route"],
			threshold: 0.3,
			shouldSort: true,
		};
		this.fuse = new Fuse(items, options);
	}

	public search(query: string): ISearchItem[] {
		if (!query) return [];
		return this.fuse.search(query).map(result => result.item);
	}

	public updateItems(items: ISearchItem[]) {
		this.fuse.setCollection(items);
	}
}

export default SearchService;
