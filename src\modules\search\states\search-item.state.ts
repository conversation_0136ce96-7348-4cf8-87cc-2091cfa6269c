import { atom } from "jotai";
import SearchService from "../services/manage/search.service";
import { ISearchItem } from "../types/search.type";
import { searchQueryAtom } from "./search-query.state";

export const searchItemsAtom = atom<ISearchItem[]>([]);

export const searchResultsAtom = atom(get => {
	const items = get(searchItemsAtom);
	const query = get(searchQueryAtom);
	if (!query) return [];

	const searchService = new SearchService(items);
	return searchService.search(query);
});
