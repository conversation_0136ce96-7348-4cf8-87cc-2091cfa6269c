import { buildQueryParams } from "@/shared/lib/build-query-params";

export const STOCK_ROUTES = {
	CREATE: "/stock/create",
	XML_UPLOAD: "/stock/upload-xml",
	CREATE_MOVEMENT: "/stock/create-movement",
	FIND_ALL: ({
		page,
		limit,
		invoice,
		expirationDate,
		status,
	}: {
		page: number;
		limit: number;
		invoice?: string;
		expirationDate?: string | Date;
		status?: boolean;
	}): string => {
		const baseUrl = `/stock/find-all/${page}/${limit}`;
		return buildQueryParams(baseUrl, {
			invoice,
			expirationDate,
			status,
		});
	},
	UPDATE: ({ id }: { id: number }) => `/stock/${id}/update`,
	FIND_ALL_MOVEMENTS: ({ page, limit, search, date }: { page: number; limit: number; search?: string; date?: string | Date }): string => {
		const baseUrl = `/stock/find-all-movements/${page}/${limit}`;
		return buildQueryParams(baseUrl, {
			search,
			date,
		});
	},
};
