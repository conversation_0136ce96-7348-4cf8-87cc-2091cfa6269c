import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ICreateStockMovementDto } from "../../dtos/create-movement.dto";
import { STOCK_ROUTES } from "../endpoints";

export const createStockMovementRequest = async (data: ICreateStockMovementDto): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest({
		method: "POST",
		path: STOCK_ROUTES.CREATE_MOVEMENT,
		body: data,
	});
};
