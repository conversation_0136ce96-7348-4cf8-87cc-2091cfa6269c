import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ICreateStockDto } from "../../dtos/create-stock.dto";
import { STOCK_ROUTES } from "../endpoints";

export const createStockRequest = async ({ ...items }: ICreateStockDto): Promise<ApiResponse<IGlobalMessageReturn>> => {
	return createRequest({
		method: "POST",
		path: STOCK_ROUTES.CREATE,
		body: items,
	});
};
