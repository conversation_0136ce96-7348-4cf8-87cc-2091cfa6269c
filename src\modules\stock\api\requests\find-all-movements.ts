import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IFindAllMovementsDTO } from "../../dtos/find-all-movements.dto";
import { STOCK_ROUTES } from "../endpoints";

interface FindAllMovementsParams {
	limit: number;
	page: number;
	search?: string;
	date?: string | Date;
}

export const findAllMovementsRequest = async (params: FindAllMovementsParams): Promise<ApiResponse<IFindAllMovementsDTO>> => {
	return createRequest<IFindAllMovementsDTO>({
		path: STOCK_ROUTES.FIND_ALL_MOVEMENTS(params),
		method: "GET",
	});
};
