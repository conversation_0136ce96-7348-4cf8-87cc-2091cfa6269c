import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IFindAllStockDTO } from "../../dtos/find-all.dto";
import { STOCK_ROUTES } from "../endpoints";

interface FindAllStockParams {
	limit: number;
	page: number;
	invoice?: string;
	expirationDate?: string | Date;
	status?: boolean;
}

export const findAllStockRequest = async (params: FindAllStockParams): Promise<ApiResponse<IFindAllStockDTO>> => {
	return createRequest<IFindAllStockDTO>({
		path: STOCK_ROUTES.FIND_ALL(params),
		method: "GET",
	});
};
