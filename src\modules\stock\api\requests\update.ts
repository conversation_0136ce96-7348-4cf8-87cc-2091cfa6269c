import { IUpdateStockDto } from "@/modules/stock/dtos/update.dto";
import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { STOCK_ROUTES } from "../endpoints";

export const updateStock = ({ data }: { data: IUpdateStockDto }): Promise<ApiResponse<IGlobalMessageReturn>> => {
	const { id, expirationDate, status } = data;

	return createRequest<IGlobalMessageReturn>({
		method: "PUT",
		path: STOCK_ROUTES.UPDATE({ id }),
		body: { expirationDate, status },
	});
};
