import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";

import { IXmlUploadReturnDto } from "../../dtos/xml-upload-return.dto";
import { STOCK_ROUTES } from "../endpoints";

export const xmlUploadRequest = ({ fileXML }: { fileXML: File }): Promise<ApiResponse<IXmlUploadReturnDto>> => {
	const formData = new FormData();
	formData.append("xml", fileXML);
	return createRequest({
		method: "POST",
		path: STOCK_ROUTES.XML_UPLOAD,
		body: formData,
	});
};
