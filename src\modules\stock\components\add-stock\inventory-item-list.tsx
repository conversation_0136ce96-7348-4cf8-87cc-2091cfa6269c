import React from "react";
import { UseFieldArrayReturn, UseFormReturn } from "react-hook-form";
import { FaBoxesPacking } from "react-icons/fa6";
import { ICreateStock } from "../../validators/create-stock.validator";
import { InventoryItem } from "./inventory-item";

interface InventoryItemsListProps {
	methodsForm: UseFormReturn<ICreateStock>;
	inventoryFieldArray: UseFieldArrayReturn<ICreateStock, "inventories", "id">;
}

export const InventoryItemsList: React.FC<InventoryItemsListProps> = ({ methodsForm, inventoryFieldArray }) => {
	return (
		<fieldset className="border-2 border-dashed rounded-lg border-gray-200 p-4 mb-6 bg-white shadow-sm">
			<legend className="flex items-center space-x-2 text-base font-semibold text-gray-700 px-2">
				<FaBoxesPacking className="text-gray-500" />
				<span>Itens</span>
			</legend>

			{inventoryFieldArray.fields.map((field, index) => (
				<InventoryItem
					key={field.id}
					index={index}
					removeItem={() => inventoryFieldArray.remove(index)}
					isExistingIdProduct={Boolean(field?.stockMovement?.product?.id)}
					isExistingIdPackage={Boolean(field?.stockMovement?.product?.package?.id)}
					methodsForm={methodsForm}
				/>
			))}

			<footer className="flex justify-center">
				<button
					type="button"
					onClick={() => {
						inventoryFieldArray.append({
							stockMovement: {
								description: "",
								product: {
									id: undefined,
									code: "",
									name: "",
									description: "",
									price: "",
									barcode: "",
									ncm: "",
									categories: [],
									package: {
										id: undefined,
										code: "",
										name: "",
										barcode: "",
										quantityPerPackage: 0,
									},
								},
								quantity: 0,
							},
							expirationDate: "",
						});
					}}
					className="mt-4  border bg-mainColor/80  border-mainColor text-white hover:bg-mainColor/10  py-2 px-6 rounded-lg font-semibold transition"
				>
					+ Adicionar Item
				</button>
			</footer>
		</fieldset>
	);
};
