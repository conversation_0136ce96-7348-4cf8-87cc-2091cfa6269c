import { IStockFindAllFilters } from "@/modules/stock/validators/stock-find-all-filters.validator";
import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { useNavigate } from "@tanstack/react-router";

import { Calendar, FileBox, FileText, Filter, RefreshCw, Tag } from "lucide-react";
import { Controller, FormProvider, UseFormReturn } from "react-hook-form";

export const StockFiltersSection = ({ methods }: { methods: UseFormReturn<IStockFindAllFilters> }) => {
	const navigate = useNavigate();
	const { reset } = methods;

	const handleReset = () => {
		reset({
			invoice: "",
			expirationDate: "",
			status: undefined,
		});
	};

	return (
		<CardLMPContainer
			icon={<Filter size={22} className="text-gray-500" />}
			title="Filtros"
			description="Utilize os filtros abaixo para refinar sua busca."
			actions={
				<Button
					className="
            hidden
            md:flex
            items-center
            gap-2
            bg-mainColor
            text-white
            px-4
            py-2
            rounded-[10px]
			h-[45px]
            text-sm
            font-medium
            hover:bg-mainColor/90
            transition-colors
            shadow-sm
          "
					onClick={() => {
						navigate({
							to: "/estoque/adicionar",
						});
					}}
				>
					<FileBox size={18} />
					Adicionar
				</Button>
			}
		>
			<FormProvider {...methods}>
				<form className="flex flex-col gap-3 md:flex-row md:items-end">
					<div className="w-full md:w-1/2">
						<label className="block text-sm font-medium text-gray-600 mb-1">NFe</label>
						<div className="relative">
							<Input
								placeholder="Ex.: **********..."
								className="w-full h-[45px] rounded-[10px] pl-10"
								{...methods.register("invoice")}
							/>
							<FileText className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
						</div>
					</div>

					<div className="w-full md:w-1/4">
						<Controller
							control={methods.control}
							name="expirationDate"
							render={({ field }) => (
								<div className="relative">
									<DatePickerInput
										className="w-full"
										field={field}
										label="Data de Expiração"
										labelClassName="text-sm font-medium text-gray-700"
										calendarClassName="z-[9999]"
										inputDateClassName="w-full mt-1 pl-10 pr-4 h-[45px] border border-gray-300 rounded-[10px] text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0197B2] focus:border-transparent"
										placeholder="Selecione uma data"
									/>
									<Calendar className="absolute left-3 top-[38px] h-4 w-4 text-gray-400 pointer-events-none" />
								</div>
							)}
						/>
					</div>
					<div className="w-full md:w-1/4">
						<label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
						<Controller
							control={methods.control}
							name="status"
							render={({ field: { onChange, value } }) => (
								<div className="relative">
									<Select
										value={value === undefined ? undefined : value}
										onValueChange={newValue => {
											onChange(newValue === undefined ? undefined : newValue);
										}}
									>
										<SelectTrigger className="w-full h-[45px] rounded-[10px] pl-10">
											<SelectValue placeholder="Todos" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="true">Ativo</SelectItem>
											<SelectItem value="false">Inativo</SelectItem>
										</SelectContent>
									</Select>
									<Tag className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								</div>
							)}
						/>
					</div>

					<div className="w-full md:w-auto">
						<Button
							type="button"
							variant="outline"
							onClick={handleReset}
							className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2"
						>
							<RefreshCw size={18} className="text-mainColor" />
							<span>Resetar Filtros</span>
						</Button>
					</div>
				</form>
			</FormProvider>
		</CardLMPContainer>
	);
};
