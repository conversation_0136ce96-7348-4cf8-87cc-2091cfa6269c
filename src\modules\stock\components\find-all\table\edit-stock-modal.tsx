import { useUpdateStock } from "@/modules/stock/hooks/stock/update-stock.hook";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { Button } from "@/shared/components/ui/button";
import { Ban, Calendar, Save, X } from "lucide-react";
import { Controller, FormProvider, useForm } from "react-hook-form";

interface IEditStockModalProps {
	isVisible: boolean;
	onClose: () => void;
	stockId: number;
	currentExpirationDate: string;
	product: string;
	quantity: number;
	invoice: string;
}

interface IFormData {
	expirationDate: string;
}

export const EditStockModal = ({ isVisible, onClose, stockId, currentExpirationDate, product, quantity, invoice }: IEditStockModalProps) => {
	const methods = useForm<IFormData>({
		defaultValues: {
			expirationDate: currentExpirationDate,
		},
	});

	const { updateStockMutation } = useUpdateStock();

	const handleUpdateDate = (data: IFormData) => {
		updateStockMutation.mutate({
			id: stockId,
			data: {
				id: stockId,
				expirationDate: data.expirationDate === "Não informado" ? undefined : data.expirationDate,
				status: true,
			},
		});
		onClose();
	};

	const handleFinishStock = () => {
		updateStockMutation.mutate({
			id: stockId,
			data: {
				id: stockId,
				status: false,
			},
		});
		onClose();
	};

	return (
		<OverlayContainer isVisible={isVisible} onClose={onClose}>
			<div className="bg-white rounded-[15px] w-full max-w-xl mx-4 sm:mx-0 relative shadow-lg overflow-hidden" style={{ zIndex: 100 }}>
				{/* Header */}
				<div className="px-4 sm:px-6 py-4 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
					<h2 className="text-lg sm:text-xl font-semibold text-gray-800">Editar Estoque</h2>
				</div>

				{/* Content */}
				<div className="p-4 sm:p-6">
					{/* Product Info */}
					<div className="mb-4 sm:mb-6">
						<div className="flex flex-col gap-1">
							<h3 className="font-medium text-gray-800 text-base sm:text-lg">{product}</h3>
							<div className="flex items-center gap-2 text-xs sm:text-sm text-gray-500">
								<span>NF-e:</span>
								<span className="font-medium text-gray-700">{invoice}</span>
							</div>
							<div className="mt-2">
								<span
									className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
										quantity > 100
											? "bg-mainColor/10 text-mainColor"
											: quantity > 10
												? "bg-yellow-50 text-yellow-700"
												: "bg-red-50 text-red-700"
									}`}
								>
									{quantity} unidades
								</span>
							</div>
						</div>
					</div>

					{/* Form */}
					<FormProvider {...methods}>
						<form onSubmit={methods.handleSubmit(handleUpdateDate)} className="space-y-4 sm:space-y-6">
							<div>
								<Controller
									name="expirationDate"
									control={methods.control}
									render={({ field }) => (
										<div className="relative">
											<DatePickerInput
												className="w-full"
												field={field}
												label="Nova Data de Validade"
												labelClassName="text-sm font-medium text-gray-700"
												calendarClassName="z-[9999]"
												inputDateClassName="w-full mt-1 pl-10 pr-4 h-10 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0197B2] focus:border-transparent"
												placeholder="Selecione uma data"
											/>
											<Calendar className="absolute left-3 top-[34px] h-4 w-4 text-gray-400 pointer-events-none" />
										</div>
									)}
								/>
							</div>

							{/* Actions */}
							<div className="flex flex-col sm:flex-row justify-end items-stretch sm:items-center gap-2 pt-4 border-t border-gray-200">
								<Button
									type="button"
									variant="outline"
									onClick={onClose}
									className="h-9 px-3 text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
								>
									<X className="h-4 w-4 mr-2" />
									Cancelar
								</Button>
								<Button
									type="submit"
									variant="primary"
									className="h-9 px-3 text-sm font-medium bg-[#0197B2] hover:bg-[#0197B2]/90 transition-colors"
								>
									<Save className="h-4 w-4 mr-2" />
									Atualizar Data
								</Button>
								<Button
									type="button"
									variant="destructive"
									onClick={handleFinishStock}
									className="h-9 px-3 text-sm font-medium transition-colors"
								>
									<Ban className="h-4 w-4 mr-2" />
									Finalizar Estoque
								</Button>
							</div>
						</form>
					</FormProvider>
				</div>
			</div>
		</OverlayContainer>
	);
};
