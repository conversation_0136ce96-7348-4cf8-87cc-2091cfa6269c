import { IFindAllStockDTO } from "@/modules/stock/dtos/find-all.dto";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { Button } from "@/shared/components/ui/button";
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ColumnDef, flexRender, getCoreRowModel, getSortedRowModel, SortingState, useReactTable } from "@tanstack/react-table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUpDown, Calendar, FileText, Layers2, Package2 } from "lucide-react";
import React, { useState } from "react";

import { Pagination } from "@/shared/components/custom/pagination";
import { EditStockModal } from "./edit-stock-modal";
import LoadingState from "./loading-state";
import NoRecordsState from "./no-records-state";

interface TableStockProps {
	data: ApiResponse<IFindAllStockDTO> | undefined;
	isLoading: boolean;
	page: number;
	itemsPerPage: number;
	onPageChange: (page: number) => void;
	onItemsPerPageChange: (itemsPerPage: number) => void;
}

type StockTableItem = {
	id: number;
	product: string;
	invoice: string;
	expirationDate: string;
	quantity: number;
	status: boolean;
};

const getQuantityColor = (quantity: number) => {
	if (quantity > 100) return "bg-mainColor/10 text-mainColor";
	if (quantity <= 100 && quantity > 10) return "bg-yellow-50 text-yellow-700";
	return "bg-red-50 text-red-700";
};

interface MobileCardProps {
	item: StockTableItem;

	onEdit: (stock: StockTableItem) => void;
}

export const MobileCard = ({ item, onEdit }: MobileCardProps) => (
	<motion.div
		initial={{ opacity: 0, y: 20 }}
		animate={{ opacity: 1, y: 0 }}
		exit={{ opacity: 0, y: -20 }}
		className="w-full rounded-xl border border-gray-100 bg-white px-4 py-3 shadow-sm"
	>
		{/* ---------- Cabeçalho ---------- */}
		<header className="flex items-start justify-between gap-3">
			<h3 className="line-clamp-2 max-w-[70%] text-sm font-medium text-gray-800 leading-5">{item.product}</h3>

			<motion.span
				initial={{ scale: 0.8, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				className={`inline-flex shrink-0 items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium ${
					item.status ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
				}`}
			>
				<span className={`h-1.5 w-1.5 rounded-full ${item.status ? "bg-green-500" : "bg-red-500"}`} />
				{item.status ? "Ativo" : "Inativo"}
			</motion.span>
		</header>

		{/* ---------- NF‑e ---------- */}
		<p className="mt-1 flex items-start gap-2 text-xs text-gray-500">
			<FileText size={14} className="mt-0.5 shrink-0" />
			<span className="break-all leading-relaxed">{item.invoice}</span>
		</p>

		{/* ---------- Metadados ---------- */}
		<div className="mt-3 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
			<div className="flex items-center gap-1">
				<Calendar size={14} className="shrink-0 text-gray-400" />
				<span>{item.expirationDate}</span>
			</div>

			<div className="flex items-center gap-1 justify-self-end">
				<Package2 size={14} className="shrink-0 text-gray-400" />
				<motion.span
					initial={{ scale: 0.8, opacity: 0 }}
					animate={{ scale: 1, opacity: 1 }}
					className={`inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium ${getQuantityColor(item.quantity)}`}
				>
					{item.quantity} un
				</motion.span>
			</div>
		</div>

		{/* ---------- Ações ---------- */}
		<footer className="mt-4 flex justify-end gap-6 border-t border-gray-100 pt-3">
			{item.status && (
				<CustomTooltip content="Gerenciar Validade">
					<motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
						<Button
							variant="ghost"
							size="icon"
							className="h-8 w-8 rounded-full text-mainColor/70 transition-colors hover:bg-mainColor/10 hover:text-mainColor"
							onClick={() => onEdit(item)}
						>
							<Calendar className="h-4 w-4" />
						</Button>
					</motion.div>
				</CustomTooltip>
			)}
		</footer>
	</motion.div>
);

export const TableStock: React.FC<TableStockProps> = ({ data, isLoading, page, itemsPerPage, onPageChange, onItemsPerPageChange }) => {
	const [sorting, setSorting] = React.useState<SortingState>([]);
	const [isEditModalOpen, setIsEditModalOpen] = useState(false);
	const [selectedStock, setSelectedStock] = useState<StockTableItem | null>(null);

	const handleEdit = (stock: StockTableItem) => {
		setSelectedStock(stock);
		setIsEditModalOpen(true);
	};

	const renderEditButton = (item: StockTableItem) => {
		if (!item.status) return null;

		return (
			<CustomTooltip content="Gerenciar Validade">
				<motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
					<Button
						variant="ghost"
						size="icon"
						className="h-8 w-8 rounded-full text-mainColor/70 transition-colors hover:bg-mainColor/10 hover:text-mainColor"
						onClick={() => handleEdit(item)}
					>
						<Calendar className="h-4 w-4" />
					</Button>
				</motion.div>
			</CustomTooltip>
		);
	};

	const columns = React.useMemo<ColumnDef<StockTableItem>[]>(
		() => [
			{
				accessorKey: "product",
				header: ({ column }) => {
					return (
						<div
							className="flex items-center justify-center gap-1 cursor-pointer group"
							onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
						>
							<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Produto</span>
							<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
						</div>
					);
				},
				cell: ({ row }) => <div className="text-center font-medium">{row.getValue("product")}</div>,
				size: 45,
			},
			{
				accessorKey: "invoice",
				header: ({ column }) => {
					return (
						<div
							className="flex items-center justify-center gap-1 cursor-pointer group"
							onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
						>
							<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Fatura</span>
							<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
						</div>
					);
				},
				cell: ({ row }) => <div className="text-center text-gray-600">{row.getValue("invoice")}</div>,
				size: 25,
			},
			{
				accessorKey: "expirationDate",
				header: ({ column }) => {
					return (
						<div
							className="flex items-center justify-center gap-1 cursor-pointer group"
							onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
						>
							<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Validade</span>
							<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
						</div>
					);
				},
				cell: ({ row }) => <div className="text-center text-gray-600">{row.getValue("expirationDate")}</div>,
				size: 10,
			},
			{
				accessorKey: "quantity",
				header: ({ column }) => {
					return (
						<div
							className="flex items-center justify-center gap-1 cursor-pointer group"
							onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
						>
							<span className="text-gray-700 font-semibold group-hover:text-primary transition-colors">Quantidade</span>
							<ArrowUpDown className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
						</div>
					);
				},
				cell: ({ row }) => {
					const quantity = row.getValue("quantity") as number;
					return (
						<div className="text-center">
							<motion.div
								initial={{ scale: 0.8, opacity: 0 }}
								animate={{ scale: 1, opacity: 1 }}
								className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getQuantityColor(quantity)}`}
							>
								{quantity} un
							</motion.div>
						</div>
					);
				},
				size: 10,
			},
			{
				accessorKey: "status",
				header: () => <div className="text-center text-gray-700 font-semibold">Status</div>,
				cell: ({ row }) => (
					<div className="text-center pr-8">
						<motion.div
							initial={{ scale: 0.8, opacity: 0 }}
							animate={{ scale: 1, opacity: 1 }}
							className={`inline-flex items-center gap-1.5 px-2.5 py-0.5 rounded-full text-xs font-medium ${
								row.getValue("status") ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"
							}`}
						>
							<div className={`w-1.5 h-1.5 rounded-full ${row.getValue("status") ? "bg-green-500" : "bg-red-500"}`} />
							{row.getValue("status") ? "Ativo" : "Inativo"}
						</motion.div>
					</div>
				),
				size: 100,
			},
			{
				id: "actions",
				header: () => <div className="text-right pr-5 text-gray-700 font-semibold">Ações</div>,
				cell: ({ row }) => <div className="flex items-center justify-end gap-1 pr-5">{renderEditButton(row.original)}</div>,
				size: 100,
			},
		],
		[]
	);

	const tableData = React.useMemo(() => {
		if (!data?.success || !data.data.data) return [];
		return data.data.data;
	}, [data]);

	const table = useReactTable({
		data: tableData,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		state: {
			sorting,
		},
	});

	return (
		<CardLMPContainer
			description="Acompanhe estoque, faturas e status de forma rápida."
			icon={<Layers2 size={22} className="text-gray-500" />}
			title="Visão Geral do Estoque"
		>
			{isLoading ? (
				<LoadingState />
			) : !data?.success || !data.data.data || data.data.data.length === 0 ? (
				<NoRecordsState />
			) : (
				<>
					{/* Versão Desktop */}
					<div className="hidden md:block">
						<AnimatePresence mode="wait">
							<motion.div
								key={page + itemsPerPage}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								transition={{ duration: 0.2 }}
								className="border rounded-[15px] border-gray-200 overflow-hidden max-h-[600px] overflow-y-auto"
							>
								<Table className="w-full rounded-[15px] text-sm min-w-[700px]">
									<TableHeader className="sticky top-0 bg-gradient-to-r from-gray-50 to-white z-10 border-b border-gray-200">
										{table.getHeaderGroups().map(headerGroup => (
											<TableRow key={headerGroup.id} className="hover:bg-transparent">
												{headerGroup.headers.map(header => (
													<TableHead key={header.id} className="py-4 px-2" style={{ width: `${header.column.getSize()}%` }}>
														{header.isPlaceholder
															? null
															: flexRender(header.column.columnDef.header, header.getContext())}
													</TableHead>
												))}
											</TableRow>
										))}
									</TableHeader>
									<TableBody className="[&>tr:nth-child(even)]:bg-[#f9fbfc] [&>tr:hover]:bg-[#f1faff] [&>tr:hover]:cursor-pointer">
										{table.getRowModel().rows.map(row => (
											<motion.tr
												key={row.id}
												initial={{ opacity: 0, x: -20 }}
												animate={{ opacity: 1, x: 0 }}
												exit={{ opacity: 0, x: 20 }}
												className="group"
											>
												{row.getVisibleCells().map(cell => (
													<td key={cell.id} className="py-3 px-2">
														{flexRender(cell.column.columnDef.cell, cell.getContext())}
													</td>
												))}
											</motion.tr>
										))}
									</TableBody>
								</Table>
							</motion.div>
						</AnimatePresence>
					</div>

					{/* Versão Mobile */}
					<div className="md:hidden">
						<AnimatePresence mode="wait">
							<motion.div
								key={page + itemsPerPage}
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
								className="grid grid-cols-1 gap-4"
							>
								{tableData.map(item => (
									<MobileCard key={item.id} item={item} onEdit={handleEdit} />
								))}
							</motion.div>
						</AnimatePresence>
					</div>

					<Pagination
						totalPages={data.data.total}
						itemsPerPage={itemsPerPage}
						page={page}
						onPageChange={onPageChange}
						onItemsPerPageChange={onItemsPerPageChange}
					/>

					{selectedStock && (
						<EditStockModal
							isVisible={isEditModalOpen}
							onClose={() => setIsEditModalOpen(false)}
							stockId={selectedStock.id}
							currentExpirationDate={selectedStock.expirationDate}
							product={selectedStock.product}
							quantity={selectedStock.quantity}
							invoice={selectedStock.invoice}
						/>
					)}
				</>
			)}
		</CardLMPContainer>
	);
};
