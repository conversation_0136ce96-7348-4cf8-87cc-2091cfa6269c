import React from "react";

interface InvoiceTooltipProps {
	fullText: string;
	truncatedText: string;
}

const InvoiceTooltip: React.FC<InvoiceTooltipProps> = ({ fullText, truncatedText }) => {
	const [copied, setCopied] = React.useState(false);
	function handleCopy() {
		navigator.clipboard.writeText(fullText);
		setCopied(true);
		setTimeout(() => {
			setCopied(false);
		}, 2000);
	}
	return (
		<div className="relative group cursor-pointer inline-block" onClick={handleCopy}>
			<span className="text-primary" style={{ textDecoration: "underline", textDecorationColor: "#0197B2", textDecorationStyle: "solid" }}>
				{truncatedText}
			</span>
			<div
				className="
          absolute left-1/2 -translate-x-1/2 bottom-full mb-2
          hidden group-hover:block
          bg-gray-700 text-white text-xs rounded py-1 px-2
          whitespace-nowrap z-10
          transition-opacity
        "
			>
				{copied ? "Copiado!" : fullText}
			</div>
		</div>
	);
};

export default InvoiceTooltip;
