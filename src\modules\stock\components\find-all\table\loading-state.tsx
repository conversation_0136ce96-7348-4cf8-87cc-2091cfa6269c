import { Skeleton } from "@/shared/components/ui/skeleton";
import { motion } from "framer-motion";

const LoadingState = () => {
	return (
		<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="space-y-4">
			<div className="flex items-center justify-between">
				<div className="space-y-2">
					<Skeleton className="h-8 w-[250px]" />
					<Skeleton className="h-4 w-[350px]" />
				</div>
				<Skeleton className="h-10 w-10 rounded-full" />
			</div>

			<div className="border rounded-[15px] border-gray-200 overflow-hidden">
				<div className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200 p-4">
					<div className="grid grid-cols-6 gap-4">
						{Array.from({ length: 6 }).map((_, index) => (
							<Skeleton key={index} className="h-6" />
						))}
					</div>
				</div>

				<div className="p-4 space-y-4">
					{Array.from({ length: 5 }).map((_, rowIndex) => (
						<motion.div
							key={rowIndex}
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: rowIndex * 0.1 }}
							className="grid grid-cols-6 gap-4"
						>
							{Array.from({ length: 6 }).map((_, colIndex) => (
								<Skeleton key={colIndex} className={`h-8 ${colIndex === 0 ? "w-full" : "w-full"}`} />
							))}
						</motion.div>
					))}
				</div>
			</div>

			<div className="flex items-center justify-between pt-4">
				<Skeleton className="h-10 w-[200px]" />
				<div className="flex gap-2">
					{Array.from({ length: 4 }).map((_, index) => (
						<Skeleton key={index} className="h-10 w-10 rounded-[15px]" />
					))}
				</div>
			</div>
		</motion.div>
	);
};

export default LoadingState;
