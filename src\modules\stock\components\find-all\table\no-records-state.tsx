import { motion } from "framer-motion";
import { PackageOpen } from "lucide-react";

const NoRecordsState = () => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 10 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0 }}
			className="flex flex-col items-center justify-center py-16 px-4"
		>
			<motion.div
				initial={{ scale: 0.5, opacity: 0 }}
				animate={{ scale: 1, opacity: 1 }}
				transition={{
					type: "spring",
					stiffness: 260,
					damping: 20,
				}}
				className="relative"
			>
				<div className="w-16 h-16 rounded-[20px] bg-gray-50 flex items-center justify-center mb-4">
					<PackageOpen className="w-8 h-8 text-gray-400" />
				</div>
				<div className="absolute -top-1 -right-1 w-4 h-4 bg-red-50 rounded-full flex items-center justify-center">
					<span className="w-2 h-2 bg-red-400 rounded-full" />
				</div>
			</motion.div>

			<motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }} className="text-center">
				<h3 className="text-lg font-semibold text-gray-700 mb-2">Nenhum registro encontrado</h3>
				<p className="text-gray-500 max-w-sm">
					Não encontramos nenhum item que corresponda aos critérios de busca. Tente ajustar os filtros.
				</p>
			</motion.div>
		</motion.div>
	);
};

export default NoRecordsState;
