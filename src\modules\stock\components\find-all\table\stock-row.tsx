import { But<PERSON> } from "@/shared/components/ui/button";
import { TableCell, TableRow } from "@/shared/components/ui/table";
import { Edit, Trash2 } from "lucide-react";
import React from "react";
import InvoiceTooltip from "./invoice-tooltip";

interface IStockItem {
	id: number;
	product: string;
	invoice: string;
	expirationDate: string;
	quantity: number;
	description: string;
	status: boolean;
}

interface StockRowProps {
	item: IStockItem;
}

const StockRow: React.FC<StockRowProps> = ({ item }) => {
	const truncatedInvoice = item.invoice.length > 20 ? item.invoice.slice(0, 20) + "..." : item.invoice;
	return (
		<TableRow key={item.id} className="odd:bg-gray-50 even:bg-white transition-colors hover:bg-gray-100">
			<TableCell className="py-3 px-2">{item.product}</TableCell>
			<TableCell className="py-3 px-2 text-center">
				<InvoiceTooltip fullText={item.invoice} truncatedText={truncatedInvoice} />
			</TableCell>
			<TableCell className="py-3 px-2 text-center">{item.expirationDate || "Não informado"}</TableCell>
			<TableCell className="py-3 px-2 text-center">{item.quantity}</TableCell>
			<TableCell className="py-3 px-2 text-center">
				{item.status ? (
					<span className="inline-flex items-center px-2 py-1 text-xs rounded-xl bg-green-100 text-green-700">Ativo</span>
				) : (
					<span className="inline-flex items-center px-2 py-1 text-xs rounded-xl bg-red-100 text-red-600">Inativo</span>
				)}
			</TableCell>
			<TableCell className="py-3 px-2 text-right">
				<div className="flex gap-3 justify-center">
					<Button variant="ghost" className="p-0 hover:bg-transparent text-mainColor hover:text-mainColor/80">
						<Edit size={20} />
					</Button>
					<Button variant="ghost" className="p-0 hover:bg-transparent text-red-400 hover:text-red-500">
						<Trash2 size={20} />
					</Button>
				</div>
			</TableCell>
		</TableRow>
	);
};

export default StockRow;
