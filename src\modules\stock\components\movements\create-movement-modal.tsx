import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { Hash, Package, TrendingUp } from "lucide-react";
import { useState } from "react";
import { Controller, FormProvider, UseFormReturn, useForm } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useCreateStockMovement } from "../../hooks/stock/create-movement.hook";
import type { CreateMovementForm } from "../../validators/create-movement.validator";
import { createMovementSchema } from "../../validators/create-movement.validator";
import { ProductSelect } from "../product-select";

interface CreateMovementModalProps {
	isOpen: boolean;
	onClose: () => void;
	title?: string;
	onSubmit?: (data: CreateMovementForm) => Promise<void>;
	form?: UseFormReturn<CreateMovementForm>;
	isLoading?: boolean;
}

export const CreateMovementModal = ({
	isOpen,
	onClose,
	title = "Nova Movimentação",
	onSubmit,
	form: externalForm,
	isLoading: externalLoading,
}: CreateMovementModalProps) => {
	const [loading, setLoading] = useState(false);
	const { mutateAsync, isPending } = useCreateStockMovement();

	const internalForm = useForm<CreateMovementForm>({
		resolver: zodResolver(createMovementSchema),
		defaultValues: {
			productId: undefined,
			quantity: undefined,
			description: "",
		},
	});

	const form = externalForm || internalForm;

	const { register, handleSubmit, formState, reset, control } = form;
	const { errors } = formState;

	const handleInternalSubmit = async (data: CreateMovementForm) => {
		setLoading(true);
		try {
			await mutateAsync({
				productId: data.productId,
				quantity: data.quantity,
				description: data.description,
			});
			reset();
			onClose();
		} finally {
			setLoading(false);
		}
	};

	const handleFormSubmit = onSubmit || handleInternalSubmit;
	const isLoadingState = externalLoading || loading || isPending;

	const handleReset = () => {
		reset();
		onClose();
	};

	return (
		<OverlayContainer isVisible={isOpen} onClose={handleReset}>
			<motion.div
				onClick={e => e.stopPropagation()}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				exit={{ opacity: 0, scale: 0.95 }}
				transition={{ duration: 0.2 }}
				className="bg-white rounded-[15px] w-full max-w-md px-4 sm:px-6 relative shadow-lg overflow-hidden py-6"
			>
				<div className="flex items-center gap-3 mb-4">
					<div className="bg-mainColor/10 p-2 rounded-full">
						<TrendingUp size={24} className="text-mainColor" />
					</div>
					<h2 className="text-lg font-semibold text-gray-800">{title}</h2>
				</div>

				<FormProvider {...form}>
					<form onSubmit={handleSubmit(handleFormSubmit, console.error)} className="flex flex-col gap-4">
						<div>
							<label htmlFor="productId" className="block text-sm font-medium text-gray-600 mb-1">
								Produto
							</label>
							<div className="relative">
								<Controller
									control={control}
									name="productId"
									render={({ field, fieldState }) => (
										<ProductSelect
											value={field.value ? String(field.value) : undefined}
											onChange={value => field.onChange(Number(value))}
											error={fieldState.error?.message}
											icon={<Package size={14} />}
											label=""
											required
										/>
									)}
								/>
								{errors.productId && <span className="text-xs text-red-500 mt-1 block">{errors.productId.message}</span>}
							</div>
						</div>

						<div>
							<label htmlFor="quantity" className="block text-sm font-medium text-gray-600 mb-1">
								Quantidade
							</label>
							<div className="relative">
								<Controller
									control={control}
									name="quantity"
									render={({ field }) => {
										const value = field.value ?? 0;
										const textColor = value > 0 ? "text-green-600" : value < 0 ? "text-red-600" : "text-gray-600";

										return (
											<NumericFormat
												value={field.value ?? ""}
												onValueChange={values => field.onChange(values.floatValue ?? undefined)}
												thousandSeparator="."
												decimalSeparator=","
												decimalScale={0}
												placeholder="0"
												customInput={Input}
												className={`w-full h-[45px] rounded-[10px] pl-10 ${textColor}`}
												allowNegative={true}
											/>
										);
									}}
								/>
								<Hash className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
								{errors.quantity && <span className="text-xs text-red-500 mt-1 block">{errors.quantity.message}</span>}
							</div>
						</div>

						<div>
							<label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">
								Descrição (Opcional)
							</label>
							<Input
								id="description"
								type="text"
								placeholder="Ex: Entrada de estoque por compra"
								{...register("description")}
								className="w-full h-[45px] rounded-[10px]"
							/>
							{errors.description && <span className="text-xs text-red-500 mt-1 block">{errors.description.message}</span>}
						</div>

						<div className="flex justify-end gap-3 mt-2">
							<Button type="button" onClick={handleReset} className="bg-gray-200 hover:bg-gray-300 text-gray-700">
								Cancelar
							</Button>
							<Button type="submit" className="bg-mainColor text-white hover:bg-mainColor/90" disabled={isLoadingState}>
								{isLoadingState ? "Salvando..." : "Criar Movimentação"}
							</Button>
						</div>
					</form>
				</FormProvider>
			</motion.div>
		</OverlayContainer>
	);
};
