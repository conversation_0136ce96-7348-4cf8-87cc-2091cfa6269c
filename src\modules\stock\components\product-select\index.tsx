import { useFindAllProduct } from "@/modules/product/hooks/product/find-all-product.hook";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/components/ui/popover";
import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Check, ChevronsUpDown, Package, Search } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

interface ProductSelectProps {
	value?: string;
	onChange: (value: string) => void;
	error?: string;
	required?: boolean;
	label?: string;
	icon?: React.ReactNode;
}

export const ProductSelect: React.FC<ProductSelectProps> = ({
	value,
	onChange,
	error,
	required = false,
	label = "Produto",
	icon = <Package size={16} />,
}) => {
	const [open, setOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [isSearching, setIsSearching] = useState(false);
	const { data: products, isLoading } = useFindAllProduct({
		page: 1,
		limit: 50,
		filter: searchTerm,
	});

	useEffect(() => {
		if (searchTerm) {
			setIsSearching(true);
		}
	}, [searchTerm]);

	useEffect(() => {
		if (products) {
			setIsSearching(false);
		}
	}, [products]);

	const selectedProduct = useMemo(
		() => products?.success && products.data.data.find(product => String(product.id) === String(value)),
		[value, products]
	);

	const getButtonText = () => {
		if (isLoading) return "Carregando produtos...";
		if (selectedProduct) return selectedProduct.name;
		return "Selecione um produto";
	};

	return (
		<div className="relative">
			<label className="flex items-center gap-1.5 text-sm font-semibold text-gray-700 mb-1.5">
				<span className="text-gray-500">{icon}</span>
				{label} {required && <span className="text-red-500 text-xs">*</span>}
			</label>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<motion.button
						type="button"
						whileHover={{ scale: 1.01 }}
						whileTap={{ scale: 0.99 }}
						transition={{ type: "spring", stiffness: 400, damping: 17 }}
						className={cn(
							"w-full px-4 py-2.5 text-sm transition-all duration-200",
							"border rounded-lg bg-white/50",
							"focus:outline-none focus:ring-2 focus:ring-mainColor/50",
							"flex items-center justify-between",
							error ? "border-red-300" : "border-gray-200 hover:border-gray-300"
						)}
						disabled={isLoading}
					>
						<span className="truncate">{getButtonText()}</span>
						<motion.div animate={{ rotate: open ? 180 : 0 }} transition={{ duration: 0.2 }}>
							<ChevronsUpDown size={16} className="text-gray-500" />
						</motion.div>
					</motion.button>
				</PopoverTrigger>
				<PopoverContent className="w-[--radix-popover-trigger-width] z-[10000] p-0 max-h-[300px] overflow-auto" align="start">
					<div className="flex flex-col h-full max-h-[300px]">
						<div className=" sticky top-0 z-10 bg-white">
							<span className="absolute left-2 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								<Search size={16} />
							</span>
							<input
								placeholder="Buscar produto..."
								value={searchTerm}
								onChange={e => setSearchTerm(e.target.value)}
								className="h-9 pl-8 pr-2 text-sm border-b border-gray-200 focus:outline-none focus:ring-0 w-full"
							/>
						</div>
						<div className="overflow-y-auto">
							{isLoading || isSearching ? (
								<div className="p-2 text-center text-gray-500 text-sm">Carregando...</div>
							) : (
								<ul className="py-1">
									<AnimatePresence>
										{products?.success &&
											products?.data.data?.map(product => (
												<motion.li
													key={product.id + product.name}
													initial={{ opacity: 0, y: 10 }}
													animate={{ opacity: 1, y: 0 }}
													exit={{ opacity: 0, y: -10 }}
													transition={{ duration: 0.2 }}
													className="flex items-center justify-between py-2 px-2 cursor-pointer hover:bg-mainColor/5"
													onClick={() => {
														onChange(String(product.id));
														setOpen(false);
														setSearchTerm("");
													}}
												>
													<div className="flex flex-col">
														<span className="font-medium">{product.name}</span>
														<span className="text-xs text-gray-500">
															Código: {product.id} | Barcode: {product.barcode}
														</span>
													</div>
													{String(product.id) === String(value) && <Check size={16} className="text-mainColor" />}
												</motion.li>
											))}
									</AnimatePresence>
									{products?.success && products.data.data.length === 0 && (
										<div className="p-2 text-center text-gray-500 text-sm">Nenhum produto encontrado</div>
									)}
								</ul>
							)}
						</div>
					</div>
				</PopoverContent>
			</Popover>
			{error && (
				<motion.div
					initial={{ opacity: 0, y: -10 }}
					animate={{ opacity: 1, y: 0 }}
					className="absolute left-0 mt-1 text-xs text-red-500 flex items-center gap-1"
				>
					<AlertCircle size={12} />
					{error}
				</motion.div>
			)}
		</div>
	);
};
