import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { Archive, ArrowUpDown, Layers2, PackagePlus } from "lucide-react";
import { STOCK_PATHS } from "./routes-path.data";

export const StockSubItemIds = {
	OVERVIEW: "stock-overview",
	MOVEMENTS: "stock-movements",
	ADD: "stock-add",
};

export const STOCK_CONFIG: IItemSidebar = {
	label: "Estoque",
	id: "stock",
	Icon: Archive,
	moduleActivated: true,
	accessibleDescription: "ícone representativo do estoque",
	path: STOCK_PATHS.ALL_STOCKS,
	type: "menu",
	subItems: [
		{
			label: "Visão Geral",
			id: StockSubItemIds.OVERVIEW,
			Icon: Layers2,
			accessibleDescription: "ícone de visão geral do estoque",
			path: STOCK_PATHS.ALL_STOCKS,
		},
		{
			label: "Entradas e Saídas",
			id: StockSubItemIds.MOVEMENTS,
			Icon: ArrowUpDown,
			accessibleDescription: "ícone de entradas e saídas",
			path: STOCK_PATHS.STOCK_IN_OUT,
		},
		{
			label: "Adicionar Estoque",
			id: StockSubItemIds.ADD,
			Icon: PackagePlus,
			accessibleDescription: "ícone de adicionar estoque",
			path: STOCK_PATHS.ADD_STOCK,
		},
	],
};
