export interface IMovementItem {
	id: number;
	product: string;
	quantity: number;
	createdAt: string;
	description: string;
}

export interface IFindAllMovementsDTO {
	data: IMovementItem[];
	pagination: {
		totalItems: number;
		itemsPerPage: number;
		totalPages: number;
		currentPage: number;
	};
}

export interface IFindAllMovementsFiltersDto {
	page: number;
	limit: number;
	search?: string;
	date?: string;
}
