import { IInvoiceBase, IProductBase, IProductPackage } from "./base-stock";

export interface IStockProduct extends IProductBase {
	quantity: number;
	package: IProductPackage;
}

export interface ISuplierStockXmlToForm extends IInvoiceBase {
	supplier: ISupplier;
}

export interface ISupplier {
	id: number;
	name: string;
}

export interface IXmlUploadItem {
	product: IStockProduct;
	quantityToAdd: number;
	packageQuantity: number;
}

export interface IXmlUploadReturnDto {
	invoice: ISuplierStockXmlToForm;
	items: IXmlUploadItem[];
}
