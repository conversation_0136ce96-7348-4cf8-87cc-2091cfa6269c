import { zodResolver } from "@hookform/resolvers/zod";
import { useFieldArray, useForm } from "react-hook-form";
import { createStockDtoSchema, ICreateStock } from "../../validators/create-stock.validator";

export const DEFAULT_STOCK_VALUES: ICreateStock = {
	invoice: {
		key: "",
		issueDate: "",
		supplier: {
			id: 0,
			name: "",
		},
	},
	inventories: [],
};

export function useCreateStockForm() {
	const formMethods = useForm<ICreateStock>({
		resolver: zodResolver(createStockDtoSchema),
		defaultValues: DEFAULT_STOCK_VALUES,
	});

	const inventoryFieldArray = useFieldArray<ICreateStock, "inventories">({
		control: formMethods.control,
		name: "inventories",
	});

	const resetInventoryFieldArray = () => {
		inventoryFieldArray.replace([]);
	};

	return { formMethods, inventoryFieldArray, resetInventoryFieldArray };
}
