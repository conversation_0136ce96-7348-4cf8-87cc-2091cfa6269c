import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { IStockFindAllFilters, StockFindAllFiltersValidator } from "../../validators/stock-find-all-filters.validator";

export const useStockFindAllFiltersForm = () => {
	return useForm<IStockFindAllFilters>({
		resolver: zodResolver(StockFindAllFiltersValidator),
		defaultValues: {
			expirationDate: "",
			status: "",
			invoice: "",
		},
	});
};
