import { ApiResponse } from "@/shared/types/requests/requests.type";
import { useQuery } from "@tanstack/react-query";
import { findAllMovementsRequest } from "../../api/requests/find-all-movements";
import { IFindAllMovementsDTO } from "../../dtos/find-all-movements.dto";

interface FindAllMovementsParams {
	limit: number;
	page: number;
	search?: string;
	date?: string | Date;
}

export const useFindAllMovements = (params: FindAllMovementsParams) => {
	const { page = 1, limit = 50, search, date } = params;

	const { data, isLoading } = useQuery<ApiResponse<IFindAllMovementsDTO>>({
		queryKey: ["stock-movements", { page, limit, search, date }],
		queryFn: () => findAllMovementsRequest({ page, limit, search, date }),
	});

	return {
		movements: data?.success ? data?.data : undefined,
		isLoading,
		error: data?.success === false && data?.data.message,
	};
};
