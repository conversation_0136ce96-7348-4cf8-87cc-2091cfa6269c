import { queryOptions, useQuery } from "@tanstack/react-query";
import { findAllStockRequest } from "../../api/requests/find-all";

interface StockFindAllParams {
	limit: number;
	page: number;
	invoice?: string;
	expirationDate?: string | Date;
	status?: boolean | undefined;
}

const stockFindAllOptions = (params: StockFindAllParams) =>
	queryOptions({
		queryKey: ["stock-find-all", params],
		queryFn: () => findAllStockRequest(params),
		enabled: true,
	});

export const useGetFindAllStock = (params: StockFindAllParams) => {
	const sanitizedParams = Object.fromEntries(Object.entries(params).filter(([, value]) => value !== undefined && value !== ""));

	return useQuery(stockFindAllOptions(sanitizedParams as StockFindAllParams));
};
