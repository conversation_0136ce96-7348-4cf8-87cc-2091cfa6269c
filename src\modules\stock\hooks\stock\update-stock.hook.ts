import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { updateStock } from "../../api/requests/update";
import { IUpdateStockDto } from "../../dtos/update.dto";

export const useUpdateStock = () => {
	const queryClient = useQueryClient();

	const updateStockMutation = useMutation({
		mutationKey: ["update-stock"],
		mutationFn: async ({ id, data }: { id: number; data: IUpdateStockDto }) => {
			toast.dismiss();
			toast.loading("Atualizando estoque...");
			const response = await updateStock({ data: { ...data, id } });

			if (!response.success) throw new Error(response.data.message);
			return response.data;
		},
		onSuccess: data => {
			toast.dismiss();
			toast.success(data.message);
			queryClient.invalidateQueries({
				queryKey: ["stock-find-all"],
				exact: false,
			});
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message || "Erro ao atualizar estoque");
		},
	});

	return { updateStockMutation };
};
