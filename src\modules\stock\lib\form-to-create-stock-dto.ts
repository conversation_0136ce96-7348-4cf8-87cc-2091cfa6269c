import { z } from "zod";
import { ICreateStockDto, IProductDetails, IStockMovement, ISuplierCreateStockDTO } from "../dtos/create-stock.dto";
import { createStockDtoSchema, ICreateStock } from "../validators/create-stock.validator";

export class CreateStockDtoMapper {
	public static map(form: ICreateStock): ICreateStockDto {
		return {
			invoice: CreateStockDtoMapper.mapInvoice(form.invoice),
			inventories: form.inventories.map(inv => CreateStockDtoMapper.mapInventory(inv)),
		};
	}

	private static mapInvoice(invoice: z.infer<typeof createStockDtoSchema>["invoice"]): ISuplierCreateStockDTO {
		const obj = {
			key: invoice.key,
			issueDate: invoice.issueDate,
			supplierId: invoice.supplier?.id || undefined,
		};

		return CreateStockDtoMapper.removeUndefined(obj) as ISuplierCreateStockDTO;
	}

	private static mapInventory(inventory: z.infer<typeof createStockDtoSchema>["inventories"][number]): ICreateStockDto["inventories"][number] {
		const obj = {
			expirationDate: inventory.expirationDate === "" ? undefined : inventory.expirationDate,
			id: inventory.id,
			stockMovement: CreateStockDtoMapper.mapStockMovement(inventory.stockMovement),
		};

		return CreateStockDtoMapper.removeUndefined(obj) as ICreateStockDto["inventories"][number];
	}

	private static mapStockMovement(stockMovement: z.infer<typeof createStockDtoSchema>["inventories"][number]["stockMovement"]): IStockMovement {
		const obj = {
			quantity: stockMovement.quantity !== undefined ? Number(stockMovement.quantity) : undefined,
			product: CreateStockDtoMapper.mapProduct(stockMovement.product),
		};

		return CreateStockDtoMapper.removeUndefined(obj) as IStockMovement;
	}

	private static mapProduct(product: z.infer<typeof createStockDtoSchema>["inventories"][number]["stockMovement"]["product"]): IProductDetails {
		if (product.id !== undefined && product.id !== null) {
			return { id: product.id } as IProductDetails;
		}

		let convertedPrice = 0;
		if (product.price !== undefined && product.price !== null) {
			const value = product.price.replace("R$ ", "").replace(",", ".");
			convertedPrice = Number(value);
		}

		const mappedPackage =
			product.package?.id !== undefined && product.package?.id !== null
				? { id: product.package.id, quantityPerPackage: product.package.quantityPerPackage }
				: {
						name: product.package?.name || "",
						barcode: product.package?.barcode || "",
						code: product.package?.code || "",
						quantityPerPackage: product.package?.quantityPerPackage ?? undefined,
					};

		const obj = {
			name: product.name,
			barcode: product.barcode,
			code: product.code,
			price: convertedPrice,
			ncm: product.ncm,
			description: product.description,
			categories: product.categories,
			package: mappedPackage,
			costPrice: convertedPrice,
		};

		return CreateStockDtoMapper.removeUndefined(obj) as IProductDetails;
	}

	private static removeUndefined<T extends object>(obj: T): Partial<T> {
		return Object.fromEntries(Object.entries(obj).filter(entry => entry[1] !== undefined)) as Partial<T>;
	}
}
