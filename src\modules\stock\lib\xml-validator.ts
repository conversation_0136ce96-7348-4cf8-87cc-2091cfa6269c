export const validateXml = (file: File): Promise<void> => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.onload = () => {
			const text = reader.result as string;
			const parser = new DOMParser();
			const xmlDoc = parser.parseFromString(text, "application/xml");
			if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
				reject(new Error("XML inválido"));
			} else {
				resolve();
			}
		};
		reader.onerror = () => {
			reject(new Error("Erro ao ler o arquivo"));
		};
		reader.readAsText(file);
	});
};
