import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { Home } from "lucide-react";
import { StockFormPage } from "../components/add-stock/stock-form-page";
import { STOCK_CONFIG, StockSubItemIds } from "../data/stock-config";

const AddStockPage = () => {
	return (
		<MainSkeletonContainer
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{
					href: STOCK_CONFIG?.subItems?.find(item => item.id === StockSubItemIds.OVERVIEW)?.path || "",
					label: "Estoque",
					icon: STOCK_CONFIG.Icon,
				},
			]}
			currentBreadcrumb={{
				href: STOCK_CONFIG?.subItems?.find(item => item.id === StockSubItemIds.ADD)?.path || "",
				label: "Adicionar Estoque",
				icon: STOCK_CONFIG.subItems?.find(item => item.id === StockSubItemIds.ADD)?.Icon,
			}}
			iconTitle={STOCK_CONFIG.subItems?.find(item => item.id === StockSubItemIds.ADD)?.Icon}
			pageTitle="Adicionar Estoque"
		>
			<StockFormPage />
		</MainSkeletonContainer>
	);
};

export default AddStockPage;
