import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { CardLMPContainer } from "@/shared/components/custom/card";
import { Pagination } from "@/shared/components/custom/pagination";
import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowDownUp, Home } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { StockMovementFiltersSection } from "../components/movements/filters";
import { MovementsListMobile } from "../components/movements/movements-list-mobile";
import { MovementsTable } from "../components/movements/movements-table";
import { STOCK_CONFIG, StockSubItemIds } from "../data/stock-config";
import { useFindAllMovements } from "../hooks/stock/find-all-movements.hook";

export interface MovementsFilters {
	search: string;
	date: string;
}

const StockMovementsPage = () => {
	const [tableKey] = useState<string>("movements-table");
	const methods = useForm<MovementsFilters>({
		defaultValues: {
			search: "",
			date: "",
		},
	});

	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = usePagination();
	const filters = methods.watch();
	const debouncedFilters = useDebounce<typeof filters>(filters, 300);

	const { movements, isLoading, error } = useFindAllMovements({
		limit: itemsPerPage,
		page: page,
		search: debouncedFilters.search || undefined,
		date: debouncedFilters.date || undefined,
	});

	const totalPages = movements?.pagination?.totalPages || 1;

	const renderTableContent = () => {
		if (isLoading) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-pulse">
					<div className="w-16 h-16 border-4 border-gray-200 border-t-mainColor rounded-full animate-spin mb-4"></div>
					<span className="text-gray-500 text-lg font-semibold">Carregando movimentações...</span>
				</div>
			);
		}

		if (error) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-red-500 text-lg font-semibold mb-2">Erro ao carregar movimentações</span>
					<span className="text-gray-400 text-sm">{error}</span>
				</div>
			);
		}

		if (!movements?.data?.length) {
			return (
				<div className="flex flex-col items-center justify-center py-10 animate-fade-in">
					<span className="text-gray-500 text-lg font-semibold mb-2">Nenhuma movimentação encontrada</span>
					<span className="text-gray-400 text-sm">Ajuste os filtros ou tente novamente.</span>
				</div>
			);
		}

		return (
			<>
				<div className="hidden md:block">
					<MovementsTable movements={movements.data} />
				</div>
				<MovementsListMobile movements={movements.data} />
			</>
		);
	};

	return (
		<MainSkeletonContainer
			iconTitle={STOCK_CONFIG.subItems?.find(item => item.id === StockSubItemIds.MOVEMENTS)?.Icon}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{
					href: STOCK_CONFIG?.subItems?.find(item => item.id === StockSubItemIds.OVERVIEW)?.path || "",
					label: "Estoque",
					icon: STOCK_CONFIG.Icon,
				},
			]}
			currentBreadcrumb={{
				href: STOCK_CONFIG?.subItems?.find(item => item.id === StockSubItemIds.MOVEMENTS)?.path || "",
				label: "Movimentações",
				icon: STOCK_CONFIG.subItems?.find(item => item.id === StockSubItemIds.MOVEMENTS)?.Icon,
			}}
			pageTitle="Movimentações"
		>
			<StockMovementFiltersSection methods={methods} />
			<CardLMPContainer
				title="Histórico de Movimentações"
				description="Acompanhe todas as movimentações de estoque"
				icon={<ArrowDownUp size={22} className="text-gray-500" />}
			>
				<AnimatePresence mode="wait">
					<motion.div
						key={tableKey}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2 }}
					>
						{renderTableContent()}
					</motion.div>
				</AnimatePresence>
				<Pagination
					totalPages={totalPages}
					itemsPerPage={itemsPerPage}
					page={page}
					onPageChange={handlePageChange}
					onItemsPerPageChange={handleItemsPerPageChange}
				/>
			</CardLMPContainer>
		</MainSkeletonContainer>
	);
};

export default StockMovementsPage;
