import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";

import { useDebounce } from "@/shared/hooks/utils/debounce.hook";
import { usePagination } from "@/shared/hooks/utils/use-pagination.hook";
import { Home } from "lucide-react";
import { StockFiltersSection } from "../components/find-all/filters";
import { TableStock } from "../components/find-all/table";
import { STOCK_CONFIG } from "../data/stock-config";
import { useStockFindAllFiltersForm } from "../hooks/stock/find-all-filters-form.hook";
import { useGetFindAllStock } from "../hooks/stock/get-find-all.hook";

const StockPage = () => {
	const methods = useStockFindAllFiltersForm();
	const { watch } = methods;
	const watchedFilters = watch();
	const debouncedFilters = useDebounce<typeof watchedFilters>(watchedFilters, 300);
	const { page, itemsPerPage, handlePageChange, handleItemsPerPageChange } = usePagination();

	const { data, isLoading } = useGetFindAllStock({
		limit: itemsPerPage,
		page: page,
		invoice: debouncedFilters.invoice || undefined,
		expirationDate: debouncedFilters.expirationDate || undefined,
		status: debouncedFilters.status === undefined || debouncedFilters.status === "" ? undefined : debouncedFilters.status === "true",
	});

	return (
		<MainSkeletonContainer
			iconTitle={STOCK_CONFIG.subItems?.find(item => item.id === "stock-overview")?.Icon}
			itemsBreadcrumb={[{ href: "/", label: "Página inicial", icon: Home }]}
			currentBreadcrumb={{
				href: STOCK_CONFIG?.subItems?.find(item => item.id === "stock-overview")?.path || "",
				label: "Estoque",
				icon: STOCK_CONFIG.Icon,
			}}
			pageTitle="Visão geral"
		>
			<StockFiltersSection methods={methods} />
			<TableStock
				data={data}
				isLoading={isLoading}
				page={page}
				itemsPerPage={itemsPerPage}
				onPageChange={handlePageChange}
				onItemsPerPageChange={handleItemsPerPageChange}
			/>
		</MainSkeletonContainer>
	);
};

export default StockPage;
