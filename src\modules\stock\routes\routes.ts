import { home } from "@/shared/routes/dashboard.route";
import { createModuleRoutes, ModuleSubRoute } from "@/shared/routes/module-routes.factory";
import { STOCK_CONFIG, StockSubItemIds } from "../data/stock-config";
import AddStockPage from "../pages/add-stock";
import StockPage from "../pages/stock";
import StockMovementsPage from "../pages/stock-movements";

const stockSubRoutes: ModuleSubRoute<NonNullable<typeof STOCK_CONFIG.subItems>[number]>[] = [
	{ id: StockSubItemIds.OVERVIEW, component: StockPage },
	{ id: StockSubItemIds.MOVEMENTS, component: StockMovementsPage },
	{ id: StockSubItemIds.ADD, component: AddStockPage },
];

export const stockRoutes = createModuleRoutes(
	{
		...STOCK_CONFIG,
		subItems: STOCK_CONFIG.subItems ?? [],
	},
	stockSubRoutes,
	() => home
);
