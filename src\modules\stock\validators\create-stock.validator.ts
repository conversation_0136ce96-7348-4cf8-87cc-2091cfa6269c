import { z } from "zod";

const optionalNumberPreprocess = z.preprocess(val => {
	if (typeof val === "string") {
		const parsed = parseFloat(val);
		return isNaN(parsed) ? undefined : parsed;
	}
	if (typeof val === "number") {
		return isNaN(val) ? undefined : val;
	}
	return val;
}, z.number().optional());

export const supplierSchema = z.object({
	id: z.number(),
	name: z.string(),
});

export const invoiceSchema = z.object({
	key: z.string(),
	issueDate: z.string(),
	supplier: supplierSchema,
});

export const packageSchema = z
	.object({
		name: z.string().min(1, "O nome da caixa é obrigatório"),
		barcode: z.string().min(1, "O código de barras da caixa é obrigatório"),
		code: z.string().min(1, "O código da caixa é obrigatório"),
		quantityPerPackage: z.preprocess(
			val => {
				if (typeof val === "string") {
					const parsed = parseFloat(val);
					return isNaN(parsed) ? undefined : parsed;
				}
				return val;
			},
			z.number().min(1, "A quantidade por caixa deve ser maior que 0")
		),
		id: z.number().optional(),
	})
	.refine(
		data => {
			if (data.id) return true;
			return Boolean(data.name && data.barcode && data.code && data.quantityPerPackage);
		},
		{
			message: "Os campos da caixa são obrigatórios para novos pacotes",
			path: ["name"],
		}
	);

export const productBaseSchema = z.object({
	id: z.number().optional(),
	name: z.string(),
	barcode: z.string(),
	code: z.string(),
	price: z.string({
		required_error: "O preço é obrigatório",
		invalid_type_error: "O preço é obrigatório",
	}),
	ncm: z.string(),
});

export const productDetailsSchema = productBaseSchema.extend({
	description: z.string(),
	categories: z.array(z.string()),
	package: packageSchema,
});

export const stockMovementSchema = z.object({
	description: z.string().optional(),
	quantity: optionalNumberPreprocess,
	product: productDetailsSchema,
	packageQuantity: optionalNumberPreprocess,
});

export const inventorySchema = z.object({
	id: z.number().optional(),
	expirationDate: z.string().optional(),
	stockMovement: stockMovementSchema,
});

export const createStockDtoSchema = z.object({
	invoice: invoiceSchema,
	inventories: z.array(inventorySchema),
});

export type ICreateStock = z.infer<typeof createStockDtoSchema>;
