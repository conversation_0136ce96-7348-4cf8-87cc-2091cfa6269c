/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as salesPanelPanelImport } from './routes/(sales-panel)/_panel'
import { Route as dashboardDashboardImport } from './routes/(dashboard)/_dashboard'
import { Route as dashboardDashboardIndexImport } from './routes/(dashboard)/_dashboard.index'
import { Route as salesPanelPanelTerminalImport } from './routes/(sales-panel)/_panel.terminal'
import { Route as dashboardDashboardTesteIndexImport } from './routes/(dashboard)/_dashboard.teste/index'
import { Route as dashboardDashboardProdutosIndexImport } from './routes/(dashboard)/_dashboard.produtos/index'
import { Route as dashboardDashboardPedidosIndexImport } from './routes/(dashboard)/_dashboard.pedidos/index'
import { Route as dashboardDashboardNotificacoesIndexImport } from './routes/(dashboard)/_dashboard.notificacoes/index'
import { Route as dashboardDashboardGraficosIndexImport } from './routes/(dashboard)/_dashboard.graficos/index'
import { Route as dashboardDashboardFinancasIndexImport } from './routes/(dashboard)/_dashboard.financas/index'
import { Route as dashboardDashboardEstoqueIndexImport } from './routes/(dashboard)/_dashboard.estoque/index'
import { Route as dashboardDashboardConfiguracoesIndexImport } from './routes/(dashboard)/_dashboard.configuracoes/index'
import { Route as dashboardDashboardClientesIndexImport } from './routes/(dashboard)/_dashboard.clientes/index'
import { Route as dashboardDashboardProdutosProductIdImport } from './routes/(dashboard)/_dashboard.produtos/$product-id'
import { Route as dashboardDashboardProdutosAdicionarRouteImport } from './routes/(dashboard)/_dashboard.produtos/adicionar.route'
import { Route as dashboardDashboardProdutosEstoqueIndexImport } from './routes/(dashboard)/_dashboard.produtos/estoque/index'
import { Route as dashboardDashboardProdutosCategoriasIndexImport } from './routes/(dashboard)/_dashboard.produtos/categorias/index'
import { Route as dashboardDashboardEstoqueEntradasSaidasIndexImport } from './routes/(dashboard)/_dashboard.estoque/entradas-saidas/index'
import { Route as dashboardDashboardEstoqueAdicionarXmlIndexImport } from './routes/(dashboard)/_dashboard.estoque/adicionar-xml/index'

// Create Virtual Routes

const salesPanelImport = createFileRoute('/(sales-panel)')()
const dashboardImport = createFileRoute('/(dashboard)')()

// Create/Update Routes

const salesPanelRoute = salesPanelImport.update({
  id: '/(sales-panel)',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const dashboardRoute = dashboardImport.update({
  id: '/(dashboard)',
  getParentRoute: () => rootRoute,
} as any)

const salesPanelPanelRoute = salesPanelPanelImport.update({
  id: '/_panel',
  getParentRoute: () => salesPanelRoute,
} as any)

const dashboardDashboardRoute = dashboardDashboardImport.update({
  id: '/_dashboard',
  getParentRoute: () => dashboardRoute,
} as any)

const dashboardDashboardIndexRoute = dashboardDashboardIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => dashboardDashboardRoute,
} as any)

const salesPanelPanelTerminalRoute = salesPanelPanelTerminalImport.update({
  id: '/terminal',
  path: '/terminal',
  getParentRoute: () => salesPanelPanelRoute,
} as any)

const dashboardDashboardTesteIndexRoute =
  dashboardDashboardTesteIndexImport.update({
    id: '/teste/',
    path: '/teste/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardProdutosIndexRoute =
  dashboardDashboardProdutosIndexImport.update({
    id: '/produtos/',
    path: '/produtos/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardPedidosIndexRoute =
  dashboardDashboardPedidosIndexImport.update({
    id: '/pedidos/',
    path: '/pedidos/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardNotificacoesIndexRoute =
  dashboardDashboardNotificacoesIndexImport.update({
    id: '/notificacoes/',
    path: '/notificacoes/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardGraficosIndexRoute =
  dashboardDashboardGraficosIndexImport.update({
    id: '/graficos/',
    path: '/graficos/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardFinancasIndexRoute =
  dashboardDashboardFinancasIndexImport.update({
    id: '/financas/',
    path: '/financas/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardEstoqueIndexRoute =
  dashboardDashboardEstoqueIndexImport.update({
    id: '/estoque/',
    path: '/estoque/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardConfiguracoesIndexRoute =
  dashboardDashboardConfiguracoesIndexImport.update({
    id: '/configuracoes/',
    path: '/configuracoes/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardClientesIndexRoute =
  dashboardDashboardClientesIndexImport.update({
    id: '/clientes/',
    path: '/clientes/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardProdutosProductIdRoute =
  dashboardDashboardProdutosProductIdImport.update({
    id: '/produtos/$product-id',
    path: '/produtos/$product-id',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardProdutosAdicionarRouteRoute =
  dashboardDashboardProdutosAdicionarRouteImport.update({
    id: '/produtos/adicionar',
    path: '/produtos/adicionar',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardProdutosEstoqueIndexRoute =
  dashboardDashboardProdutosEstoqueIndexImport.update({
    id: '/produtos/estoque/',
    path: '/produtos/estoque/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardProdutosCategoriasIndexRoute =
  dashboardDashboardProdutosCategoriasIndexImport.update({
    id: '/produtos/categorias/',
    path: '/produtos/categorias/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardEstoqueEntradasSaidasIndexRoute =
  dashboardDashboardEstoqueEntradasSaidasIndexImport.update({
    id: '/estoque/entradas-saidas/',
    path: '/estoque/entradas-saidas/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

const dashboardDashboardEstoqueAdicionarXmlIndexRoute =
  dashboardDashboardEstoqueAdicionarXmlIndexImport.update({
    id: '/estoque/adicionar-xml/',
    path: '/estoque/adicionar-xml/',
    getParentRoute: () => dashboardDashboardRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/(dashboard)': {
      id: '/(dashboard)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof dashboardImport
      parentRoute: typeof rootRoute
    }
    '/(dashboard)/_dashboard': {
      id: '/(dashboard)/_dashboard'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof dashboardDashboardImport
      parentRoute: typeof dashboardRoute
    }
    '/(sales-panel)': {
      id: '/(sales-panel)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof salesPanelImport
      parentRoute: typeof rootRoute
    }
    '/(sales-panel)/_panel': {
      id: '/(sales-panel)/_panel'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof salesPanelPanelImport
      parentRoute: typeof salesPanelRoute
    }
    '/(sales-panel)/_panel/terminal': {
      id: '/(sales-panel)/_panel/terminal'
      path: '/terminal'
      fullPath: '/terminal'
      preLoaderRoute: typeof salesPanelPanelTerminalImport
      parentRoute: typeof salesPanelPanelImport
    }
    '/(dashboard)/_dashboard/': {
      id: '/(dashboard)/_dashboard/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof dashboardDashboardIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/produtos/adicionar': {
      id: '/(dashboard)/_dashboard/produtos/adicionar'
      path: '/produtos/adicionar'
      fullPath: '/produtos/adicionar'
      preLoaderRoute: typeof dashboardDashboardProdutosAdicionarRouteImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/produtos/$product-id': {
      id: '/(dashboard)/_dashboard/produtos/$product-id'
      path: '/produtos/$product-id'
      fullPath: '/produtos/$product-id'
      preLoaderRoute: typeof dashboardDashboardProdutosProductIdImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/clientes/': {
      id: '/(dashboard)/_dashboard/clientes/'
      path: '/clientes'
      fullPath: '/clientes'
      preLoaderRoute: typeof dashboardDashboardClientesIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/configuracoes/': {
      id: '/(dashboard)/_dashboard/configuracoes/'
      path: '/configuracoes'
      fullPath: '/configuracoes'
      preLoaderRoute: typeof dashboardDashboardConfiguracoesIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/estoque/': {
      id: '/(dashboard)/_dashboard/estoque/'
      path: '/estoque'
      fullPath: '/estoque'
      preLoaderRoute: typeof dashboardDashboardEstoqueIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/financas/': {
      id: '/(dashboard)/_dashboard/financas/'
      path: '/financas'
      fullPath: '/financas'
      preLoaderRoute: typeof dashboardDashboardFinancasIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/graficos/': {
      id: '/(dashboard)/_dashboard/graficos/'
      path: '/graficos'
      fullPath: '/graficos'
      preLoaderRoute: typeof dashboardDashboardGraficosIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/notificacoes/': {
      id: '/(dashboard)/_dashboard/notificacoes/'
      path: '/notificacoes'
      fullPath: '/notificacoes'
      preLoaderRoute: typeof dashboardDashboardNotificacoesIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/pedidos/': {
      id: '/(dashboard)/_dashboard/pedidos/'
      path: '/pedidos'
      fullPath: '/pedidos'
      preLoaderRoute: typeof dashboardDashboardPedidosIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/produtos/': {
      id: '/(dashboard)/_dashboard/produtos/'
      path: '/produtos'
      fullPath: '/produtos'
      preLoaderRoute: typeof dashboardDashboardProdutosIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/teste/': {
      id: '/(dashboard)/_dashboard/teste/'
      path: '/teste'
      fullPath: '/teste'
      preLoaderRoute: typeof dashboardDashboardTesteIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/estoque/adicionar-xml/': {
      id: '/(dashboard)/_dashboard/estoque/adicionar-xml/'
      path: '/estoque/adicionar-xml'
      fullPath: '/estoque/adicionar-xml'
      preLoaderRoute: typeof dashboardDashboardEstoqueAdicionarXmlIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/estoque/entradas-saidas/': {
      id: '/(dashboard)/_dashboard/estoque/entradas-saidas/'
      path: '/estoque/entradas-saidas'
      fullPath: '/estoque/entradas-saidas'
      preLoaderRoute: typeof dashboardDashboardEstoqueEntradasSaidasIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/produtos/categorias/': {
      id: '/(dashboard)/_dashboard/produtos/categorias/'
      path: '/produtos/categorias'
      fullPath: '/produtos/categorias'
      preLoaderRoute: typeof dashboardDashboardProdutosCategoriasIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
    '/(dashboard)/_dashboard/produtos/estoque/': {
      id: '/(dashboard)/_dashboard/produtos/estoque/'
      path: '/produtos/estoque'
      fullPath: '/produtos/estoque'
      preLoaderRoute: typeof dashboardDashboardProdutosEstoqueIndexImport
      parentRoute: typeof dashboardDashboardImport
    }
  }
}

// Create and export the route tree

interface dashboardDashboardRouteChildren {
  dashboardDashboardIndexRoute: typeof dashboardDashboardIndexRoute
  dashboardDashboardProdutosAdicionarRouteRoute: typeof dashboardDashboardProdutosAdicionarRouteRoute
  dashboardDashboardProdutosProductIdRoute: typeof dashboardDashboardProdutosProductIdRoute
  dashboardDashboardClientesIndexRoute: typeof dashboardDashboardClientesIndexRoute
  dashboardDashboardConfiguracoesIndexRoute: typeof dashboardDashboardConfiguracoesIndexRoute
  dashboardDashboardEstoqueIndexRoute: typeof dashboardDashboardEstoqueIndexRoute
  dashboardDashboardFinancasIndexRoute: typeof dashboardDashboardFinancasIndexRoute
  dashboardDashboardGraficosIndexRoute: typeof dashboardDashboardGraficosIndexRoute
  dashboardDashboardNotificacoesIndexRoute: typeof dashboardDashboardNotificacoesIndexRoute
  dashboardDashboardPedidosIndexRoute: typeof dashboardDashboardPedidosIndexRoute
  dashboardDashboardProdutosIndexRoute: typeof dashboardDashboardProdutosIndexRoute
  dashboardDashboardTesteIndexRoute: typeof dashboardDashboardTesteIndexRoute
  dashboardDashboardEstoqueAdicionarXmlIndexRoute: typeof dashboardDashboardEstoqueAdicionarXmlIndexRoute
  dashboardDashboardEstoqueEntradasSaidasIndexRoute: typeof dashboardDashboardEstoqueEntradasSaidasIndexRoute
  dashboardDashboardProdutosCategoriasIndexRoute: typeof dashboardDashboardProdutosCategoriasIndexRoute
  dashboardDashboardProdutosEstoqueIndexRoute: typeof dashboardDashboardProdutosEstoqueIndexRoute
}

const dashboardDashboardRouteChildren: dashboardDashboardRouteChildren = {
  dashboardDashboardIndexRoute: dashboardDashboardIndexRoute,
  dashboardDashboardProdutosAdicionarRouteRoute:
    dashboardDashboardProdutosAdicionarRouteRoute,
  dashboardDashboardProdutosProductIdRoute:
    dashboardDashboardProdutosProductIdRoute,
  dashboardDashboardClientesIndexRoute: dashboardDashboardClientesIndexRoute,
  dashboardDashboardConfiguracoesIndexRoute:
    dashboardDashboardConfiguracoesIndexRoute,
  dashboardDashboardEstoqueIndexRoute: dashboardDashboardEstoqueIndexRoute,
  dashboardDashboardFinancasIndexRoute: dashboardDashboardFinancasIndexRoute,
  dashboardDashboardGraficosIndexRoute: dashboardDashboardGraficosIndexRoute,
  dashboardDashboardNotificacoesIndexRoute:
    dashboardDashboardNotificacoesIndexRoute,
  dashboardDashboardPedidosIndexRoute: dashboardDashboardPedidosIndexRoute,
  dashboardDashboardProdutosIndexRoute: dashboardDashboardProdutosIndexRoute,
  dashboardDashboardTesteIndexRoute: dashboardDashboardTesteIndexRoute,
  dashboardDashboardEstoqueAdicionarXmlIndexRoute:
    dashboardDashboardEstoqueAdicionarXmlIndexRoute,
  dashboardDashboardEstoqueEntradasSaidasIndexRoute:
    dashboardDashboardEstoqueEntradasSaidasIndexRoute,
  dashboardDashboardProdutosCategoriasIndexRoute:
    dashboardDashboardProdutosCategoriasIndexRoute,
  dashboardDashboardProdutosEstoqueIndexRoute:
    dashboardDashboardProdutosEstoqueIndexRoute,
}

const dashboardDashboardRouteWithChildren =
  dashboardDashboardRoute._addFileChildren(dashboardDashboardRouteChildren)

interface dashboardRouteChildren {
  dashboardDashboardRoute: typeof dashboardDashboardRouteWithChildren
}

const dashboardRouteChildren: dashboardRouteChildren = {
  dashboardDashboardRoute: dashboardDashboardRouteWithChildren,
}

const dashboardRouteWithChildren = dashboardRoute._addFileChildren(
  dashboardRouteChildren,
)

interface salesPanelPanelRouteChildren {
  salesPanelPanelTerminalRoute: typeof salesPanelPanelTerminalRoute
}

const salesPanelPanelRouteChildren: salesPanelPanelRouteChildren = {
  salesPanelPanelTerminalRoute: salesPanelPanelTerminalRoute,
}

const salesPanelPanelRouteWithChildren = salesPanelPanelRoute._addFileChildren(
  salesPanelPanelRouteChildren,
)

interface salesPanelRouteChildren {
  salesPanelPanelRoute: typeof salesPanelPanelRouteWithChildren
}

const salesPanelRouteChildren: salesPanelRouteChildren = {
  salesPanelPanelRoute: salesPanelPanelRouteWithChildren,
}

const salesPanelRouteWithChildren = salesPanelRoute._addFileChildren(
  salesPanelRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof dashboardDashboardIndexRoute
  '/terminal': typeof salesPanelPanelTerminalRoute
  '/produtos/adicionar': typeof dashboardDashboardProdutosAdicionarRouteRoute
  '/produtos/$product-id': typeof dashboardDashboardProdutosProductIdRoute
  '/clientes': typeof dashboardDashboardClientesIndexRoute
  '/configuracoes': typeof dashboardDashboardConfiguracoesIndexRoute
  '/estoque': typeof dashboardDashboardEstoqueIndexRoute
  '/financas': typeof dashboardDashboardFinancasIndexRoute
  '/graficos': typeof dashboardDashboardGraficosIndexRoute
  '/notificacoes': typeof dashboardDashboardNotificacoesIndexRoute
  '/pedidos': typeof dashboardDashboardPedidosIndexRoute
  '/produtos': typeof dashboardDashboardProdutosIndexRoute
  '/teste': typeof dashboardDashboardTesteIndexRoute
  '/estoque/adicionar-xml': typeof dashboardDashboardEstoqueAdicionarXmlIndexRoute
  '/estoque/entradas-saidas': typeof dashboardDashboardEstoqueEntradasSaidasIndexRoute
  '/produtos/categorias': typeof dashboardDashboardProdutosCategoriasIndexRoute
  '/produtos/estoque': typeof dashboardDashboardProdutosEstoqueIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof dashboardDashboardIndexRoute
  '/terminal': typeof salesPanelPanelTerminalRoute
  '/produtos/adicionar': typeof dashboardDashboardProdutosAdicionarRouteRoute
  '/produtos/$product-id': typeof dashboardDashboardProdutosProductIdRoute
  '/clientes': typeof dashboardDashboardClientesIndexRoute
  '/configuracoes': typeof dashboardDashboardConfiguracoesIndexRoute
  '/estoque': typeof dashboardDashboardEstoqueIndexRoute
  '/financas': typeof dashboardDashboardFinancasIndexRoute
  '/graficos': typeof dashboardDashboardGraficosIndexRoute
  '/notificacoes': typeof dashboardDashboardNotificacoesIndexRoute
  '/pedidos': typeof dashboardDashboardPedidosIndexRoute
  '/produtos': typeof dashboardDashboardProdutosIndexRoute
  '/teste': typeof dashboardDashboardTesteIndexRoute
  '/estoque/adicionar-xml': typeof dashboardDashboardEstoqueAdicionarXmlIndexRoute
  '/estoque/entradas-saidas': typeof dashboardDashboardEstoqueEntradasSaidasIndexRoute
  '/produtos/categorias': typeof dashboardDashboardProdutosCategoriasIndexRoute
  '/produtos/estoque': typeof dashboardDashboardProdutosEstoqueIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/(dashboard)': typeof dashboardRouteWithChildren
  '/(dashboard)/_dashboard': typeof dashboardDashboardRouteWithChildren
  '/(sales-panel)': typeof salesPanelRouteWithChildren
  '/(sales-panel)/_panel': typeof salesPanelPanelRouteWithChildren
  '/(sales-panel)/_panel/terminal': typeof salesPanelPanelTerminalRoute
  '/(dashboard)/_dashboard/': typeof dashboardDashboardIndexRoute
  '/(dashboard)/_dashboard/produtos/adicionar': typeof dashboardDashboardProdutosAdicionarRouteRoute
  '/(dashboard)/_dashboard/produtos/$product-id': typeof dashboardDashboardProdutosProductIdRoute
  '/(dashboard)/_dashboard/clientes/': typeof dashboardDashboardClientesIndexRoute
  '/(dashboard)/_dashboard/configuracoes/': typeof dashboardDashboardConfiguracoesIndexRoute
  '/(dashboard)/_dashboard/estoque/': typeof dashboardDashboardEstoqueIndexRoute
  '/(dashboard)/_dashboard/financas/': typeof dashboardDashboardFinancasIndexRoute
  '/(dashboard)/_dashboard/graficos/': typeof dashboardDashboardGraficosIndexRoute
  '/(dashboard)/_dashboard/notificacoes/': typeof dashboardDashboardNotificacoesIndexRoute
  '/(dashboard)/_dashboard/pedidos/': typeof dashboardDashboardPedidosIndexRoute
  '/(dashboard)/_dashboard/produtos/': typeof dashboardDashboardProdutosIndexRoute
  '/(dashboard)/_dashboard/teste/': typeof dashboardDashboardTesteIndexRoute
  '/(dashboard)/_dashboard/estoque/adicionar-xml/': typeof dashboardDashboardEstoqueAdicionarXmlIndexRoute
  '/(dashboard)/_dashboard/estoque/entradas-saidas/': typeof dashboardDashboardEstoqueEntradasSaidasIndexRoute
  '/(dashboard)/_dashboard/produtos/categorias/': typeof dashboardDashboardProdutosCategoriasIndexRoute
  '/(dashboard)/_dashboard/produtos/estoque/': typeof dashboardDashboardProdutosEstoqueIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/terminal'
    | '/produtos/adicionar'
    | '/produtos/$product-id'
    | '/clientes'
    | '/configuracoes'
    | '/estoque'
    | '/financas'
    | '/graficos'
    | '/notificacoes'
    | '/pedidos'
    | '/produtos'
    | '/teste'
    | '/estoque/adicionar-xml'
    | '/estoque/entradas-saidas'
    | '/produtos/categorias'
    | '/produtos/estoque'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/terminal'
    | '/produtos/adicionar'
    | '/produtos/$product-id'
    | '/clientes'
    | '/configuracoes'
    | '/estoque'
    | '/financas'
    | '/graficos'
    | '/notificacoes'
    | '/pedidos'
    | '/produtos'
    | '/teste'
    | '/estoque/adicionar-xml'
    | '/estoque/entradas-saidas'
    | '/produtos/categorias'
    | '/produtos/estoque'
  id:
    | '__root__'
    | '/(dashboard)'
    | '/(dashboard)/_dashboard'
    | '/(sales-panel)'
    | '/(sales-panel)/_panel'
    | '/(sales-panel)/_panel/terminal'
    | '/(dashboard)/_dashboard/'
    | '/(dashboard)/_dashboard/produtos/adicionar'
    | '/(dashboard)/_dashboard/produtos/$product-id'
    | '/(dashboard)/_dashboard/clientes/'
    | '/(dashboard)/_dashboard/configuracoes/'
    | '/(dashboard)/_dashboard/estoque/'
    | '/(dashboard)/_dashboard/financas/'
    | '/(dashboard)/_dashboard/graficos/'
    | '/(dashboard)/_dashboard/notificacoes/'
    | '/(dashboard)/_dashboard/pedidos/'
    | '/(dashboard)/_dashboard/produtos/'
    | '/(dashboard)/_dashboard/teste/'
    | '/(dashboard)/_dashboard/estoque/adicionar-xml/'
    | '/(dashboard)/_dashboard/estoque/entradas-saidas/'
    | '/(dashboard)/_dashboard/produtos/categorias/'
    | '/(dashboard)/_dashboard/produtos/estoque/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  dashboardRoute: typeof dashboardRouteWithChildren
  salesPanelRoute: typeof salesPanelRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  dashboardRoute: dashboardRouteWithChildren,
  salesPanelRoute: salesPanelRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/(dashboard)",
        "/(sales-panel)"
      ]
    },
    "/(dashboard)": {
      "filePath": "(dashboard)",
      "children": [
        "/(dashboard)/_dashboard"
      ]
    },
    "/(dashboard)/_dashboard": {
      "filePath": "(dashboard)/_dashboard.tsx",
      "parent": "/(dashboard)",
      "children": [
        "/(dashboard)/_dashboard/",
        "/(dashboard)/_dashboard/produtos/adicionar",
        "/(dashboard)/_dashboard/produtos/$product-id",
        "/(dashboard)/_dashboard/clientes/",
        "/(dashboard)/_dashboard/configuracoes/",
        "/(dashboard)/_dashboard/estoque/",
        "/(dashboard)/_dashboard/financas/",
        "/(dashboard)/_dashboard/graficos/",
        "/(dashboard)/_dashboard/notificacoes/",
        "/(dashboard)/_dashboard/pedidos/",
        "/(dashboard)/_dashboard/produtos/",
        "/(dashboard)/_dashboard/teste/",
        "/(dashboard)/_dashboard/estoque/adicionar-xml/",
        "/(dashboard)/_dashboard/estoque/entradas-saidas/",
        "/(dashboard)/_dashboard/produtos/categorias/",
        "/(dashboard)/_dashboard/produtos/estoque/"
      ]
    },
    "/(sales-panel)": {
      "filePath": "(sales-panel)",
      "children": [
        "/(sales-panel)/_panel"
      ]
    },
    "/(sales-panel)/_panel": {
      "filePath": "(sales-panel)/_panel.tsx",
      "parent": "/(sales-panel)",
      "children": [
        "/(sales-panel)/_panel/terminal"
      ]
    },
    "/(sales-panel)/_panel/terminal": {
      "filePath": "(sales-panel)/_panel.terminal.tsx",
      "parent": "/(sales-panel)/_panel"
    },
    "/(dashboard)/_dashboard/": {
      "filePath": "(dashboard)/_dashboard.index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/produtos/adicionar": {
      "filePath": "(dashboard)/_dashboard.produtos/adicionar.route.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/produtos/$product-id": {
      "filePath": "(dashboard)/_dashboard.produtos/$product-id.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/clientes/": {
      "filePath": "(dashboard)/_dashboard.clientes/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/configuracoes/": {
      "filePath": "(dashboard)/_dashboard.configuracoes/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/estoque/": {
      "filePath": "(dashboard)/_dashboard.estoque/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/financas/": {
      "filePath": "(dashboard)/_dashboard.financas/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/graficos/": {
      "filePath": "(dashboard)/_dashboard.graficos/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/notificacoes/": {
      "filePath": "(dashboard)/_dashboard.notificacoes/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/pedidos/": {
      "filePath": "(dashboard)/_dashboard.pedidos/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/produtos/": {
      "filePath": "(dashboard)/_dashboard.produtos/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/teste/": {
      "filePath": "(dashboard)/_dashboard.teste/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/estoque/adicionar-xml/": {
      "filePath": "(dashboard)/_dashboard.estoque/adicionar-xml/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/estoque/entradas-saidas/": {
      "filePath": "(dashboard)/_dashboard.estoque/entradas-saidas/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/produtos/categorias/": {
      "filePath": "(dashboard)/_dashboard.produtos/categorias/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    },
    "/(dashboard)/_dashboard/produtos/estoque/": {
      "filePath": "(dashboard)/_dashboard.produtos/estoque/index.tsx",
      "parent": "/(dashboard)/_dashboard"
    }
  }
}
ROUTE_MANIFEST_END */
