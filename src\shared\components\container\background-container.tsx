import { ReactNode } from "react";

interface BackgroundContainerProps {
	children: ReactNode;
	className?: string;
}

export const BackgroundContainer = ({ children, className = "" }: BackgroundContainerProps) => (
	<div className={`min-h-screen bg-gray-50 ${className}`}>{children}</div>
);

export const MainBackground = ({ children }: { children: ReactNode }) => {
	return (
		<main className="flex items-center justify-center w-screen min-h-screen bg-fixed bg-gradient-to-r from-gray-100 to-[#0197B2]/20">
			{children}
		</main>
	);
};
