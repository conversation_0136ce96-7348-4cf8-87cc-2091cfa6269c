import { Link } from "@tanstack/react-router";
import { ChevronRight } from "lucide-react";
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "../ui/breadcrumb";

export interface MainSkeletonContainerProps {
	children: React.ReactNode;
	itemsBreadcrumb: IBreadcrumb[];
	currentBreadcrumb: IBreadcrumb;
	pageTitle: string;
	iconTitle?: React.ElementType;
}

export interface IBreadcrumb {
	href: string;
	label: string;
	icon?: React.ElementType;
}

export const MainSkeletonContainer = ({
	children,
	itemsBreadcrumb,
	currentBreadcrumb,
	pageTitle,
	iconTitle: IconTitle,
}: MainSkeletonContainerProps) => {
	return (
		<main className="flex flex-col w-full min-h-screen p-2 sm:p-4 space-y-4 sm:space-y-6">
			<div className="bg-white shadow-sm sm:shadow rounded-[20px] p-3 sm:p-4 space-y-3">
				<h1 className="flex items-center gap-2 text-lg sm:text-2xl font-bold text-black">
					{IconTitle && <IconTitle className="h-5 w-5 sm:h-6 sm:w-6 text-mainColor flex-shrink-0" />}
					{pageTitle}
				</h1>

				<Breadcrumb>
					<BreadcrumbList className="flex flex-wrap gap-y-2 items-center text-xs sm:text-sm text-gray-500">
						{itemsBreadcrumb.map((item, index) => (
							<div key={index} className="flex items-center last:mr-2">
								<BreadcrumbItem className="flex items-center">
									<Link
										to={item.href}
										className="flex items-center gap-1.5 hover:underline transition-colors duration-200 hover:text-mainColor/80"
										role="link"
										tabIndex={0}
									>
										{item.icon && <item.icon className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />}
										<span className="truncate max-w-[100px] sm:max-w-[150px] md:max-w-none">{item.label}</span>
									</Link>
								</BreadcrumbItem>

								{index < itemsBreadcrumb.length - 1 && (
									<BreadcrumbSeparator className="text-gray-300 mx-1.5 flex-shrink-0">
										<ChevronRight className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
									</BreadcrumbSeparator>
								)}
							</div>
						))}

						{itemsBreadcrumb.length > 0 && (
							<BreadcrumbSeparator className="text-gray-300 mx-1.5 flex-shrink-0">
								<ChevronRight className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
							</BreadcrumbSeparator>
						)}
						<BreadcrumbItem>
							<BreadcrumbPage className="px-2.5 py-1 sm:py-1.5 rounded-full bg-mainColor/10 text-mainColor font-medium sm:font-semibold flex items-center gap-1.5 text-xs sm:text-sm whitespace-nowrap">
								{currentBreadcrumb.icon && <currentBreadcrumb.icon className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />}
								<span className="truncate max-w-[130px] sm:max-w-[180px] md:max-w-none">{currentBreadcrumb.label}</span>
							</BreadcrumbPage>
						</BreadcrumbItem>
					</BreadcrumbList>
				</Breadcrumb>
			</div>
			<div className="flex-1 space-y-4 sm:space-y-6">{children}</div>
		</main>
	);
};
