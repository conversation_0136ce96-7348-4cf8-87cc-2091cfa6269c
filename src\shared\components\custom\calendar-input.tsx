import { format, isValid, parse, setHours, setMinutes } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useEffect, useState } from "react";
import { ControllerRenderProps, FieldErrors, FieldPath, FieldValues } from "react-hook-form";
import { Calendar } from "../ui/calendar";
import { FormControl, FormItem, FormLabel, FormMessage } from "../ui/form";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";

export interface IDatePickerInputProps<T extends FieldValues> {
	field: ControllerRenderProps<T, FieldPath<T>>;
	errors?: FieldErrors<T>;
	label: string;
	inputDateClassName?: string;
	placeholder?: string;
	withTime?: boolean;
	dateFormat?: string;
	minDate?: Date;
	maxDate?: Date;
	disabled?: boolean;
	labelClassName?: string;
	className?: string;
	calendarClassName?: string;
}

export const DatePickerInput = <T extends FieldValues>({
	field,
	errors,
	label,
	placeholder = "Selecione uma data",
	withTime = false,
	dateFormat = withTime ? "dd/MM/yyyy HH:mm" : "dd/MM/yyyy",
	inputDateClassName = "w-full mt-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#0197B2] focus:border-transparent",
	minDate = new Date("1900-01-01"),
	maxDate = undefined,
	disabled = false,
	labelClassName = "text-sm",
	className = "",
	calendarClassName = "",
}: IDatePickerInputProps<T>) => {
	const [open, setOpen] = useState(false);
	const parseDate = (dateString: string): Date | null => {
		if (!dateString) return null;
		const formats = ["dd/MM/yyyy", "yyyy-MM-dd"];
		for (const format of formats) {
			const parsedDate = parse(dateString, format, new Date());
			if (isValid(parsedDate)) {
				return parsedDate;
			}
		}
		return null;
	};

	const [selectedDate, setSelectedDate] = useState<Date | null>(field.value ? parseDate(field.value) : null);
	const [timeString, setTimeString] = useState<string>(selectedDate ? format(selectedDate, "HH:mm") : "");

	useEffect(() => {
		const newDate = field.value ? parseDate(field.value) : null;
		setSelectedDate(newDate);
		setTimeString(newDate ? format(newDate, "HH:mm") : "");
	}, [field.value]);

	const handleSelectDate = (newDate: Date | undefined) => {
		if (!newDate) {
			setSelectedDate(null);
			field.onChange("");
			return;
		}

		let updatedDate = newDate;

		if (withTime && timeString) {
			const [hh, mm] = timeString.split(":");
			updatedDate = setMinutes(setHours(updatedDate, parseInt(hh) || 0), parseInt(mm) || 0);
		}

		setSelectedDate(updatedDate);

		field.onChange(format(updatedDate, "yyyy-MM-dd"));
		setOpen(false);
	};

	const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const newValue = e.target.value;
		setTimeString(newValue);

		const baseDate = selectedDate || new Date();
		const [hh, mm] = newValue.split(":");

		const finalDate = setMinutes(setHours(baseDate, parseInt(hh) || 0), parseInt(mm) || 0);

		setSelectedDate(finalDate);
		field.onChange(format(finalDate, "yyyy-MM-dd"));
	};

	return (
		<FormItem className={className}>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<FormControl className="w-full">
						<div>
							<FormLabel className={labelClassName}>{label}</FormLabel>
							<Input
								disabled={disabled}
								value={selectedDate ? format(selectedDate, dateFormat) : ""}
								placeholder={placeholder}
								className={`${inputDateClassName}`}
								readOnly
							/>
						</div>
					</FormControl>
				</PopoverTrigger>

				<PopoverContent className={`w-auto p-3 ${calendarClassName}`} align="start">
					<Calendar
						mode="single"
						locale={ptBR}
						selected={selectedDate || undefined}
						onSelect={handleSelectDate}
						disabled={(date: Date) => (maxDate ? date > maxDate : false) || date < minDate}
						initialFocus
					/>

					{withTime && (
						<div className="mt-2 flex items-center gap-2">
							<Label className={`${labelClassName} text-gray-600`}>Hora:</Label>
							<Input type="time" value={timeString} onChange={handleTimeChange} className="h-[35px] w-[150px]" />
						</div>
					)}
				</PopoverContent>
			</Popover>
			{errors && <FormMessage>{errors[field.name]?.message as string}</FormMessage>}
		</FormItem>
	);
};
