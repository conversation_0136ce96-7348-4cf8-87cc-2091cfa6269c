import React from "react";
import { Separator } from "../ui/separator";

interface CardLMPContainerProps {
	id?: string;
	icon?: React.ReactNode;
	title?: string;
	description?: string;
	actions?: React.ReactNode;
	children: React.ReactNode;
}

export const CardLMPContainer: React.FC<CardLMPContainerProps> = ({ id, icon, title, description, actions, children }) => {
	return (
		<section id={id} className="w-full bg-white p-5 shadow-md rounded-[20px] flex flex-col gap-4">
			<header className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
				<div className="flex items-center gap-3">
					{icon && <div className="bg-gray-200 border border-gray-200 p-2 rounded-lg">{icon}</div>}
					{(title || description) && (
						<div>
							{title && <h2 className="text-lg font-semibold text-gray-700">{title}</h2>}
							{description && <p className="text-sm text-gray-500">{description}</p>}
						</div>
					)}
				</div>
				{actions && <div className="flex items-center gap-2">{actions}</div>}
			</header>
			<Separator className="bg-gray-200 w-full" />
			<div>{children}</div>
		</section>
	);
};
