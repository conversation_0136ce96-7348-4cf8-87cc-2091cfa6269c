import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect } from "react";
import ReactDOM from "react-dom";

interface FullScreenModalProps {
	isOpen: boolean;
	onClose: () => void;
	children: React.ReactNode;
}

const FullScreenModal: React.FC<FullScreenModalProps> = ({ isOpen, onClose, children }) => {
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "Escape") {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener("keydown", handleKeyDown);
		}

		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [isOpen, onClose]);

	return ReactDOM.createPortal(
		<AnimatePresence>
			{isOpen && (
				<motion.div
					key="modal"
					className="fixed inset-0 z-50 flex flex-col"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
				>
					<motion.div
						className="absolute inset-0 bg-black/50"
						onClick={onClose}
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
					/>
					<motion.div
						className="relative flex flex-col w-full h-full bg-white overflow-hidden shadow-xl"
						initial={{ y: 40 }}
						animate={{ y: 0 }}
						exit={{ y: 40 }}
						transition={{ type: "spring", stiffness: 250, damping: 24 }}
					>
						{children}
					</motion.div>
				</motion.div>
			)}
		</AnimatePresence>,
		document.body
	);
};

export default FullScreenModal;
