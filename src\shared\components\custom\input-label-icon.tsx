import { LucideIcon } from "lucide-react";
import { Control, Controller, FieldErrors, FieldPath } from "react-hook-form";
import { Input } from "../ui/input";

export const FormInputWithIcon = <TFormValues extends Record<string, unknown>>({
	name,
	label,
	placeholder = "",
	icon: Icon,
	control,
	errors,
	type = "text",
	className = "",
}: {
	name: FieldPath<TFormValues>;
	label: string;
	placeholder?: string;
	icon?: LucideIcon;
	control: Control<TFormValues>;
	errors: FieldErrors<TFormValues>;
	type?: string;
	className?: string;
}) => {
	const getNestedError = (path: string, errors: FieldErrors<TFormValues>) => {
		const keys = path.split(".");
		let current: Record<string, unknown> = errors as unknown as Record<string, unknown>;

		for (const key of keys) {
			if (current?.[key] === undefined || current[key] === null) return undefined;
			current = (current[key] ?? {}) as Record<string, unknown>;
		}

		return current;
	};

	const fieldError = getNestedError(name, errors);

	return (
		<div className="w-full">
			<label htmlFor={name} className="block text-sm font-medium text-gray-600 mb-1">
				{label}
			</label>
			<Controller
				name={name}
				control={control}
				render={({ field }) => (
					<div className="relative">
						{Icon && (
							<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
								<Icon size={18} className="text-gray-400" />
							</div>
						)}
						<Input
							{...field}
							id={name}
							type={type}
							placeholder={placeholder}
							className={`w-full h-[45px] rounded-[10px] ${Icon ? "pl-10" : ""} ${className}`}
							value={
								typeof field.value === "string" || typeof field.value === "number" || Array.isArray(field.value)
									? field.value
									: field.value === undefined || field.value === null
										? ""
										: String(field.value)
							}
						/>
					</div>
				)}
			/>
			{fieldError && typeof (fieldError as { message?: unknown }).message === "string" && (
				<span className="text-xs text-red-500 mt-1 block">{(fieldError as { message: string }).message}</span>
			)}
		</div>
	);
};
