import { Separator } from "@/shared/components/ui/separator";
import { Sheet, SheetContent, SheetDes<PERSON>, SheetHeader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/shared/components/ui/sheet";
import React from "react";

interface SheetModalProps {
	trigger: React.ReactNode;
	title: string;
	description?: string;
	children: React.ReactNode;
	footer?: React.ReactNode;
}

export function SheetModal({ trigger, title, description, children, footer }: Readonly<SheetModalProps>) {
	return (
		<Sheet>
			<SheetTrigger asChild>{trigger}</SheetTrigger>

			<SheetContent side="right" className="rounded-l-xl md:w-96 w-full p-6 bg-white shadow-xl">
				<SheetHeader className="mb-4">
					<SheetTitle className="text-xl font-semibold text-gray-800">{title}</SheetTitle>
					{description && <SheetDescription className="text-sm text-gray-500">{description}</SheetDescription>}
				</SheetHeader>

				<Separator className="bg-gray-200 mb-4" />

				{children}
				{footer}
			</SheetContent>
		</Sheet>
	);
}
