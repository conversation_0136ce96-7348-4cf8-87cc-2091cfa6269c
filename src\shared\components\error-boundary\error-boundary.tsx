import { Component, ErrorInfo, PropsWithChildren } from "react";
import { ErrorFallback } from "./error-fallback";

interface ErrorBoundaryProps extends PropsWithChildren {
	onError?: (error: Error, errorInfo: ErrorInfo) => void;
	fallback?: React.ComponentType<{ error: Error | null }>;
}

interface ErrorBoundaryState {
	hasError: boolean;
	error: Error | null;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = { hasError: false, error: null };
	}

	static getDerivedStateFromError(error: Error): ErrorBoundaryState {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		console.error("ErrorBoundary capturou um erro:", error, errorInfo);

		if (this.props.onError) {
			this.props.onError(error, errorInfo);
		}
	}

	resetErrorBoundary = () => {
		this.setState({ hasError: false, error: null });
	};

	render() {
		if (this.state.hasError) {
			const FallbackComponent = this.props.fallback || ErrorFallback;
			return <FallbackComponent error={this.state.error} resetErrorBoundary={this.resetErrorBoundary} />;
		}

		return this.props.children;
	}
}
