import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, RefreshCw } from "lucide-react";
import { useState } from "react";

interface ErrorFallbackProps {
	readonly error: Error | null;
	readonly resetErrorBoundary: () => void;
}

export function ErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
	const [isLoading, setIsLoading] = useState(false);

	const handleReset = () => {
		setIsLoading(true);
		setTimeout(() => {
			resetErrorBoundary();
			setIsLoading(false);
		}, 800);
	};

	const handleGoHome = () => {
		window.location.href = "/";
	};

	return (
		<div className="min-h-screen relative flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-gray-50 to-indigo-50 overflow-visible">
			<motion.div
				className="absolute inset-0 opacity-10"
				initial={{ opacity: 0 }}
				animate={{ opacity: 0.15 }}
				transition={{ duration: 1 }}
				style={{ zIndex: 2 }}
			>
				<div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-transparent"></div>
				<div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-indigo-400 to-transparent"></div>
				<div className="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-l from-blue-400 to-transparent"></div>
				<div className="absolute bottom-0 left-0 w-1 h-full bg-gradient-to-t from-indigo-400 to-transparent"></div>
			</motion.div>

			<motion.div
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="bg-white/90 backdrop-blur-lg p-8 rounded-[20px] shadow-lg max-w-md w-full border border-gray-100 relative z-10"
			>
				<div className="flex items-center justify-center mb-6">
					<motion.div
						className="bg-red-100 p-4 rounded-full"
						initial={{ scale: 0 }}
						animate={{ scale: [0, 1.2, 1] }}
						transition={{ duration: 0.5, ease: "easeOut" }}
					>
						<AlertCircle className="text-red-500" size={32} />
					</motion.div>
				</div>

				<motion.h2
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 0.2 }}
					className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent text-center mb-3"
				>
					Oops! Algo deu errado
				</motion.h2>

				<p className="text-gray-600 text-center mb-6">Desculpe, ocorreu um erro inesperado. Nossa equipe foi notificada.</p>

				{error && (
					<motion.div
						initial={{ opacity: 0, height: 0 }}
						animate={{ opacity: 1, height: "auto" }}
						transition={{ delay: 0.3 }}
						className="bg-gradient-to-r from-red-50 to-orange-50 p-4 rounded-md mb-6 border border-red-100"
					>
						<p className="text-sm font-medium text-gray-700 mb-2">Detalhes do erro:</p>
						<pre className="text-xs bg-white/70 p-3 rounded text-gray-600 overflow-auto max-h-[150px] border border-red-100 shadow-inner">
							{error.message}
						</pre>
					</motion.div>
				)}

				<div className="flex flex-col sm:flex-row gap-3 justify-center">
					<motion.button
						whileHover={{ scale: 1.03, boxShadow: "0 4px 12px rgba(0,0,0,0.05)" }}
						whileTap={{ scale: 0.97 }}
						onClick={handleGoHome}
						className="px-5 py-2 bg-white text-gray-700 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors flex items-center justify-center gap-2 text-sm font-medium"
					>
						<ArrowLeft size={16} />
						Voltar ao início
					</motion.button>

					<motion.button
						whileHover={{ scale: 1.03, boxShadow: "0 4px 12px rgba(59, 130, 246, 0.2)" }}
						whileTap={{ scale: 0.97 }}
						onClick={handleReset}
						disabled={isLoading}
						className="px-5 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full hover:from-blue-600 hover:to-blue-700 transition-colors flex items-center justify-center gap-2 text-sm font-medium shadow-md hover:shadow-lg"
					>
						{isLoading ? <RefreshCw size={16} className="animate-spin" /> : <RefreshCw size={16} />}
						Tentar novamente
					</motion.button>
				</div>
			</motion.div>

			<motion.p
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ delay: 0.6 }}
				className="text-gray-500 text-xs mt-6 relative z-10"
			>
				LMP · Sistema de Gerenciamento © {new Date().getFullYear()}
			</motion.p>

			{/* Data atual */}
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 0.7 }}
				transition={{ delay: 0.8 }}
				className="absolute bottom-4 right-4 text-xs text-gray-500"
			>
				{new Date().toLocaleDateString("pt-BR", { day: "numeric", month: "long", year: "numeric" })}
			</motion.div>
		</div>
	);
}
