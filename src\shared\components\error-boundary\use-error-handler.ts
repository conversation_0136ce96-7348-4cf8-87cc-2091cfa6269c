import { useCallback, useState } from "react";

export function useErrorHandler() {
	const [error, setError] = useState<Error | null>(null);

	const handleError = useCallback((error: unknown) => {
		if (error instanceof Error) {
			setError(error);
		} else if (typeof error === "string") {
			setError(new Error(error));
		} else {
			setError(new Error("Ocorreu um erro desconhecido"));
		}
	}, []);

	const resetError = useCallback(() => {
		setError(null);
	}, []);

	return { error, handleError, resetError };
}
