import { cn } from "@/shared/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { forwardRef } from "react";
import { Input } from "./input";
import { Label } from "./label";

interface AnimatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
	label: string;
	isLoading?: boolean;
	error?: string;
}

export const AnimatedInput = forwardRef<HTMLInputElement, AnimatedInputProps>(({ label, className, isLoading, error, ...props }, ref) => {
	return (
		<div className="lg:min-w-[150px] lg:w-auto w-full relative">
			<Label htmlFor={props.id} className="absolute top-[-5px] bg-white px-3 left-3 z-10">
				{label}{" "}
				{isLoading && (
					<motion.span initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="text-gray-600">
						(buscando...)
					</motion.span>
				)}
			</Label>
			<motion.div
				initial={{ scale: 1 }}
				animate={{
					scale: props.value ? 1.02 : 1,
				}}
				transition={{ type: "spring", stiffness: 300, damping: 20 }}
			>
				<Input ref={ref} className={cn("border-2 h-[45px]  rounded-[15px] border-[#505050] w-full px-3", className)} {...props} />
			</motion.div>
			<AnimatePresence>
				{error && (
					<motion.span
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -10 }}
						className="text-red-500 text-sm absolute bottom-[-20px] left-3"
					>
						{error}
					</motion.span>
				)}
			</AnimatePresence>
		</div>
	);
});
