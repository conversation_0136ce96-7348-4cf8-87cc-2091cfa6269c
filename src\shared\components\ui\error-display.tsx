import { ErrorDisplayProps } from "@/shared/types/ui/loading.types";
import { Button } from "./button";

export const ErrorDisplay = ({ message, retry }: ErrorDisplayProps) => (
	<div className="flex flex-col items-center justify-center min-h-screen">
		<p className="text-red-600 text-xl">Ops! Algo deu errado.</p>
		<p className="text-gray-600 mt-2">{message}</p>
		{retry && (
			<Button onClick={retry} variant="outline" className="mt-4">
				Tentar novamente
			</Button>
		)}
	</div>
);
