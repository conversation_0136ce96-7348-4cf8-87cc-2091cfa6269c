import { cn } from "@/shared/lib/utils";
import * as React from "react";

const Input = React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(({ className, type = "text", ...props }, ref) => {
	return (
		<input
			type={type}
			className={cn(
				"w-full mt-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-600 placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-[#0197B2] focus:border-transparent",
				className
			)}
			ref={ref}
			{...props}
		/>
	);
});
Input.displayName = "Input";

export { Input };
