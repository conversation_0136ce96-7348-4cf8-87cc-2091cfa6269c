import { LOADING_MESSAGES } from "@/shared/data/constants/ui/spinner.constants";
import { LoadingStateProps } from "@/shared/types/ui/loading.types";
import { Spinner } from "./spinner";

export const LoadingState = ({ message = LOADING_MESSAGES.default }: LoadingStateProps) => (
	<div className="flex flex-col items-center justify-center min-h-screen">
		<Spinner size="lg" />
		<p className="mt-4 text-gray-700 text-lg font-medium">{message}</p>
	</div>
);
