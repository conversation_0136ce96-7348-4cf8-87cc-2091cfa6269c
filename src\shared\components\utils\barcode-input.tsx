// BarcodeInput.tsx
import { Input } from "@/shared/components/ui/input";
import BarcodeScanner from "@/shared/components/utils/barcode-scanner";
import { CameraIcon } from "lucide-react";
import { useEffect, useState } from "react";
import FullScreenModal from "../custom/full-screen";

interface BarcodeInputProps {
	value: string;
	onChange: (value: string) => void;
	readOnly?: boolean;
	error?: string;
}

const isMobileDevice = () => /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);

export const BarcodeInput: React.FC<BarcodeInputProps> = ({ value, onChange, readOnly = false, error }) => {
	const [scanning, setScanning] = useState(false);
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		setIsMobile(isMobileDevice());
	}, []);

	const handleScanDetected = (barcode: string) => {
		onChange(barcode);
		setScanning(false);
	};

	return (
		<div className="relative">
			<Input
				type="text"
				placeholder="Ex: 12345678912"
				readOnly={readOnly}
				value={value}
				onChange={e => onChange(e.target.value)}
				className={`text-sm ${readOnly ? "bg-gray-100 cursor-not-allowed text-gray-500" : ""} ${error ? "border-red-500" : ""}`}
			/>

			{!readOnly && isMobile && (
				<button
					type="button"
					onClick={() => setScanning(true)}
					className="absolute right-2 top-2 p-1 text-gray-500 hover:text-blue-500"
					title="Escanear Código de Barras"
				>
					<CameraIcon size={16} />
				</button>
			)}

			{error && <span className="text-red-600 text-xs">{error}</span>}

			<FullScreenModal isOpen={scanning} onClose={() => setScanning(false)}>
				<div className="flex-1 overflow-hidden">
					<BarcodeScanner onDetected={handleScanDetected} onClose={() => setScanning(false)} />
				</div>
			</FullScreenModal>
		</div>
	);
};
