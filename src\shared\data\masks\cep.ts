interface MaskFormatter {
	format(value: string): string;
}

const removeNonDigits = (value: string): string => value.replace(/\D/g, "");

class CepMaskFormatter implements MaskFormatter {
	format(value: string): string {
		let cleaned = removeNonDigits(value);
		cleaned = cleaned.substring(0, 8);
		return cleaned.replace(/^(\d{5})(\d)/, "$1-$2");
	}
}

export const cepMask = (value: string): string => {
	const formatter = new CepMaskFormatter();
	return formatter.format(value);
};
