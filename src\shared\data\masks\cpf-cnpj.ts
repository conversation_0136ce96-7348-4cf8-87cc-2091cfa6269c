interface MaskFormatter {
	format(value: string): string;
}

const removeNonDigits = (value: string): string => value.replace(/\D/g, "");

class CpfMaskFormatter implements MaskFormatter {
	format(value: string): string {
		let cleaned = removeNonDigits(value);
		cleaned = cleaned
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
		return cleaned;
	}
}

class CnpjMaskFormatter implements MaskFormatter {
	format(value: string): string {
		let cleaned = removeNonDigits(value);
		cleaned = cleaned.substring(0, 14);
		cleaned = cleaned
			.replace(/^(\d{2})(\d)/, "$1.$2")
			.replace(/^(\d{2})\.(\d{3})(\d)/, "$1.$2.$3")
			.replace(/\.(\d{3})(\d)/, ".$1/$2")
			.replace(/(\d{4})(\d)/, "$1-$2");
		return cleaned;
	}
}

class MaskFormatterFactory {
	static create(value: string): MaskFormatter {
		const cleaned = removeNonDigits(value);
		return cleaned.length <= 11 ? new CpfMaskFormatter() : new CnpjMaskFormatter();
	}
}

export const cpfCnpjMask = (value: string): string => {
	const formatter = MaskFormatterFactory.create(value);
	return formatter.format(value);
};
