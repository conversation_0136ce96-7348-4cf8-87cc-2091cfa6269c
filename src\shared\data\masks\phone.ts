interface MaskFormatter {
	format(value: string): string;
}

const removeNonDigits = (value: string): string => value.replace(/\D/g, "");

class PhoneMaskFormatter implements MaskFormatter {
	format(value: string): string {
		let cleaned = removeNonDigits(value);
		cleaned = cleaned.substring(0, 11);

		// Formato: (XX) XXXXX-XXXX para celular ou (XX) XXXX-XXXX para fixo
		if (cleaned.length === 11) {
			return cleaned.replace(/^(\d{2})/, "($1) ").replace(/(\d{5})(\d)/, "$1-$2");
		}
		return cleaned.replace(/^(\d{2})/, "($1) ").replace(/(\d{4})(\d)/, "$1-$2");
	}
}

export const phoneMask = (value: string): string => {
	const formatter = new PhoneMaskFormatter();
	return formatter.format(value);
};
