import { cpfCnpjMask } from "@/shared/data/masks/cpf-cnpj";
import { ChangeEvent } from "react";

export const useCpfField = ({ cpfCnpj, setCpfCnpj }: { cpfCnpj: string | null; setCpfCnpj: (value: string | null) => void }) => {
	const handleCpfChange = (e: ChangeEvent<HTMLInputElement>) => {
		const oldValue = cpfCnpj;
		const numericValue = e.target.value.replace(/\D/g, "");
		const cursorPosition = e.target.selectionStart ?? 0;
		const nonNumericBeforeCursor = (oldValue?.substring(0, cursorPosition).match(/\D/g) || []).length;
		const maskedValue = cpfCnpjMask(numericValue);
		setCpfCnpj(maskedValue);
		const adjustment = (maskedValue.substring(0, cursorPosition).match(/\D/g) || []).length - nonNumericBeforeCursor;
		const newCursorPosition = cursorPosition + adjustment;
		requestAnimationFrame(() => {
			e.target.setSelectionRange(newCursorPosition, newCursorPosition);
		});
	};

	return { cpfCnpj, handleCpfChange };
};
