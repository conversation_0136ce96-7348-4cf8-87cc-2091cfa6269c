import { useCallback, useState } from "react";

export type ModalOptions = {
	onOpen?: () => void;
	onClose?: () => void;
	persistent?: boolean;
	closeOnEsc?: boolean;
};

export const useModal = (defaultOptions: ModalOptions = {}) => {
	const [isOpen, setIsOpen] = useState(false);
	const [modalContent, setModalContent] = useState<React.ReactNode>(null);
	const [modalData, setModalData] = useState<any>(null);
	const [options, setOptions] = useState<ModalOptions>({
		closeOnEsc: true,
		...defaultOptions,
	});

	const openModal = useCallback(
		(content: React.ReactNode, data?: any, newOptions?: ModalOptions) => {
			setModalContent(content);
			if (data !== undefined) setModalData(data);
			if (newOptions) setOptions(prev => ({ ...prev, ...newOptions }));
			setIsOpen(true);

			if (options.onOpen) options.onOpen();
		},
		[options]
	);

	const closeModal = useCallback(() => {
		if (options.persistent) return;

		setIsOpen(false);
		if (options.onClose) options.onClose();

		setTimeout(() => {
			setModalContent(null);
			setModalData(null);
		}, 300);
	}, [options]);

	const toggleModal = useCallback(() => {
		if (isOpen) {
			closeModal();
		} else {
			openModal(modalContent);
		}
	}, [isOpen, modalContent, closeModal, openModal]);

	const updateContent = useCallback((content: React.ReactNode) => {
		setModalContent(content);
	}, []);

	return {
		isOpen,
		modalContent,
		modalData,
		openModal,
		closeModal,
		toggleModal,
		updateContent,
		options,
	};
};
