import { useCallback, useState } from "react";

export interface UsePaginationReturn {
	page: number;
	itemsPerPage: number;
	handlePageChange: (newPage: number) => void;
	handleItemsPerPageChange: (newItemsPerPage: number) => void;
	resetPagination: () => void;
}

export const usePagination = (defaultItemsPerPage = 50) => {
	const [page, setPage] = useState(1);
	const [itemsPerPage, setItemsPerPage] = useState(defaultItemsPerPage);

	const handlePageChange = (newPage: number) => setPage(newPage);
	const handleItemsPerPageChange = (newItemsPerPage: number) => setItemsPerPage(newItemsPerPage);
	const resetPagination = useCallback(() => {
		setPage(1);
		setItemsPerPage(defaultItemsPerPage);
	}, [defaultItemsPerPage]);

	return {
		page,
		itemsPerPage,
		handlePageChange,
		handleItemsPerPageChange,
		resetPagination,
	};
};
