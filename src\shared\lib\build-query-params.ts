export const buildQueryParams = (base: string, params: Record<string, unknown>) => {
	const searchParams = new URLSearchParams();
	Object.entries(params).forEach(([key, value]) => {
		if (value !== undefined && value !== null) {
			let stringValue: string;
			if (typeof value === "object" && !(value instanceof Date)) {
				stringValue = JSON.stringify(value);
			} else {
				stringValue = String(value);
			}
			searchParams.append(key, stringValue);
		}
	});
	const query = searchParams.toString();
	return query ? `${base}?${query}` : base;
};
