import CryptoJS from "crypto-js";

export interface IStorage {
	getItem(key: string): string | null;
	setItem(key: string, value: string): void;
	removeItem(key: string): void;
}

export const createEncryptedStorage = (secretKey: string): IStorage => ({
	getItem: (key: string) => {
		const data = localStorage.getItem(key);
		if (!data) return null;
		try {
			return CryptoJS.AES.decrypt(data, secretKey).toString(CryptoJS.enc.Utf8);
		} catch {
			return null;
		}
	},
	setItem: (key: string, value: string) => localStorage.setItem(key, CryptoJS.AES.encrypt(value, secretKey).toString()),
	removeItem: (key: string) => localStorage.removeItem(key),
});
