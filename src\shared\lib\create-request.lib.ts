import { AxiosRequestConfig } from "axios";
import { apiInstance } from "../services/config/api-intance.service";
import { ApiResponse, ISuccessResponse } from "../types/requests/requests.type";
import { ICreateRequest } from "./../types/requests/create-requests.type";
import { resolveGlobalErrors } from "./erros/handle-globals-errors.lib";

const validateRequestParameters = <TRequest>({ path, method }: ICreateRequest<TRequest>): void => {
	if (!path || !method) {
		throw new Error("Os parâmetros 'path' e 'method' são obrigatórios");
	}

	if (path.trim() === "") {
		throw new Error("O parâmetro 'path' não pode ser uma string vazia");
	}
};

const createSuccessResponse = <T>(data: T, status: number): ISuccessResponse<T> => {
	return {
		success: true,
		data,
		status: status || 200,
	};
};

const setupRequestConfig = <TRequest>({ path, method, body, timeout = 30000, ...config }: ICreateRequest<TRequest>): AxiosRequestConfig => {
	return {
		url: path,
		method,
		data: body,
		timeout,
		...config,
	};
};

export const createRequest = async <TSuccess, TRequest = unknown>(params: ICreateRequest<TRequest>): Promise<ApiResponse<TSuccess>> => {
	try {
		validateRequestParameters(params);
		const requestConfig = setupRequestConfig(params);
		const { data, status } = await apiInstance.request<TSuccess>(requestConfig);
		return createSuccessResponse(data, status);
	} catch (error: unknown) {
		return resolveGlobalErrors(error);
	}
};
