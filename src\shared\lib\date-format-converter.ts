import { format, isValid, parse } from "date-fns";

export const convertBrazilianToISO = (dateString: string): string => {
	if (!dateString) return "";
	if (dateString.match(/^\d{4}-\d{2}-\d{2}/)) {
		return dateString;
	}
	let parsed = parse(dateString, "dd/MM/yyyy HH:mm", new Date());
	if (isValid(parsed)) {
		return format(parsed, "yyyy-MM-dd HH:mm");
	}
	parsed = parse(dateString, "dd/MM/yyyy", new Date());
	if (isValid(parsed)) {
		return format(parsed, "yyyy-MM-dd");
	}
	return dateString;
};

export const convertISOToBrazilian = (dateString: string): string => {
	if (!dateString) return "";
	if (dateString.match(/^\d{2}\/\d{2}\/\d{4}/)) {
		return dateString;
	}
	let parsed = parse(dateString, "yyyy-MM-dd HH:mm", new Date());
	if (isValid(parsed)) {
		return format(parsed, "dd/MM/yyyy HH:mm");
	}
	parsed = parse(dateString, "yyyy-MM-dd", new Date());
	if (isValid(parsed)) {
		return format(parsed, "dd/MM/yyyy");
	}
	return dateString;
};

export const parseMultipleFormats = (dateString: string): Date | null => {
	if (!dateString) return null;
	const formats = ["yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM-dd", "dd/MM/yyyy HH:mm:ss", "dd/MM/yyyy HH:mm", "dd/MM/yyyy"];
	for (const format of formats) {
		const parsed = parse(dateString, format, new Date());
		if (isValid(parsed)) {
			return parsed;
		}
	}
	return null;
};

export const formatForDisplay = (date: Date | string, withTime: boolean = false): string => {
	if (!date) return "";
	let dateObj: Date;
	if (typeof date === "string") {
		const parsed = parseMultipleFormats(date);
		if (!parsed) return "";
		dateObj = parsed;
	} else {
		dateObj = date;
	}
	if (!isValid(dateObj)) return "";
	return format(dateObj, withTime ? "dd/MM/yyyy HH:mm" : "dd/MM/yyyy");
};

export const formatForAPI = (date: Date | string, withTime: boolean = false): string => {
	if (!date) return "";
	let dateObj: Date;
	if (typeof date === "string") {
		const parsed = parseMultipleFormats(date);
		if (!parsed) return "";
		dateObj = parsed;
	} else {
		dateObj = date;
	}
	if (!isValid(dateObj)) return "";
	return format(dateObj, withTime ? "yyyy-MM-dd HH:mm" : "yyyy-MM-dd");
};
