import axios, { AxiosError } from "axios";
import { ErrorData, IErrorResponse } from "../../types/requests/requests.type";

const createDefaultErrorResponse = (): IErrorResponse => {
	return {
		success: false,
		data: {
			message: "Ocorreu um erro desconhecido",
		},
		status: 0,
	};
};

const extractErrorData = (data: unknown): ErrorData => {
	if (!data)
		return {
			message: "Ocorreu um erro desconhecido",
		};

	if (typeof data === "string" && data.startsWith("<!DOCTYPE html>")) {
		return {
			message: "Ocorreu um erro de servidor",
		};
	}

	if (typeof data === "string") {
		return {
			message: data,
		};
	}

	if (typeof data === "object" && data !== null) {
		const objData = data as Record<string, unknown>;
		const errorData: ErrorData = { message: "Ocorreu um erro desconhecido" };

		if ("message" in objData) {
			const message = objData.message;
			if (Array.isArray(message)) {
				const filteredMessages = message.filter(Boolean);
				errorData.message = filteredMessages.join(", ");
				return errorData;
			}

			if (typeof message === "string") {
				errorData.message = message;
				return errorData;
			}

			if (typeof message === "object" && message !== null) {
				if ("message" in message && typeof (message as Record<string, unknown>).message === "string") {
					errorData.message = String((message as Record<string, unknown>).message);

					return errorData;
				}
			}
		}

		const errorFields = ["error", "erro", "errorMessage", "mensagem", "errors", "erros"];
		for (const key of errorFields) {
			if (key in objData && objData[key]) {
				if (typeof objData[key] === "string") {
					errorData.message = objData[key] as string;
					return errorData;
				}

				if (Array.isArray(objData[key])) {
					const errArray = objData[key] as unknown[];
					errorData.message = errArray
						.map(err => {
							if (typeof err === "string") return err;
							if (typeof err === "object" && err !== null && "message" in err) {
								const message = (err as Record<string, unknown>).message;
								return typeof message === "string" ? message : String(message ?? "");
							}
							return String(err);
						})
						.filter(Boolean)
						.join(", ");

					return errorData;
				}

				if (typeof objData[key] === "object" && objData[key] !== null) {
					const errObj = objData[key] as Record<string, unknown>;
					if ("message" in errObj && typeof errObj.message === "string") {
						errorData.message = errObj.message;

						return errorData;
					}

					const messages: string[] = [];
					Object.entries(errObj).forEach(([field, value]) => {
						if (typeof value === "string") {
							messages.push(`${field}: ${value}`);
						} else if (Array.isArray(value)) {
							messages.push(`${field}: ${value.join(", ")}`);
						}
					});

					if (messages.length > 0) {
						errorData.message = messages.join("; ");

						return errorData;
					}
				}
			}
		}
	}

	return { message: "Ocorreu um erro desconhecido" };
};

const parseAxiosResponseError = (error: AxiosError): IErrorResponse => {
	const { status, data, config } = error.response!;
	const errorData = extractErrorData(data);

	return {
		success: false,
		data: {
			...errorData,
			method: config?.method?.toUpperCase() ?? "UNKNOWN",
			url: config?.url ?? "unknown-url",
		},
		status: status ?? 500,
	};
};

const parseAxiosRequestError = (error: AxiosError): IErrorResponse => {
	let method = "UNKNOWN";
	let url = "unknown-url";

	if (error.request) {
		if (typeof error.request === "object") {
			const request = error.request as Record<string, unknown>;
			method = typeof request.method === "string" ? request.method.toUpperCase() : "UNKNOWN";
			url = typeof request.url === "string" ? request.url : "unknown-url";
		}
	}

	if (error.config) {
		method = error.config.method?.toUpperCase() ?? method;
		url = error.config.url ?? url;
	}

	if (error.code) {
		if (error.code === "ECONNABORTED") {
			return {
				success: false,
				data: {
					message: "A requisição excedeu o tempo limite",
					method,
					url,
				},
				status: 408,
			};
		}

		if (error.code === "ECONNREFUSED") {
			return {
				success: false,
				data: {
					message: "Conexão recusada pelo servidor",
					method,
					url,
				},
				status: 0,
			};
		}
	}

	return {
		success: false,
		data: {
			message: "Não foi possível conectar ao servidor",
			method,
			url,
		},
		status: 0,
	};
};

const parseAxiosError = (error: AxiosError): IErrorResponse => {
	if (error.response) return parseAxiosResponseError(error);
	if (error.request) return parseAxiosRequestError(error);

	return {
		success: false,
		data: {
			message: error.message ?? "Erro na configuração da requisição",
		},
		status: 0,
	};
};

/**
 * Processa erros gerais de JavaScript
 */
const parseGeneralError = (error: Error): IErrorResponse => {
	const errorData: ErrorData = {
		message: error.message ?? "Ocorreu um erro na aplicação",
	};

	return {
		success: false,
		data: errorData,
		status: 0,
	};
};

/**
 * Função principal que resolve os erros globais da aplicação
 */
export const resolveGlobalErrors = (error: unknown): IErrorResponse => {
	// Erros do Axios
	if (axios.isAxiosError(error)) return parseAxiosError(error);

	// Erros gerais do JavaScript
	if (error instanceof Error) return parseGeneralError(error);

	// Para objetos de erro serializados ou outros formatos desconhecidos
	if (error && typeof error === "object") {
		const objError = error as Record<string, unknown>;

		// Verifica campos essenciais
		const hasMessage = "message" in objError && typeof objError.message === "string";
		const hasStatus = "status" in objError && typeof objError.status === "number";

		if (hasMessage) {
			return {
				success: false,
				data: {
					message: String(objError.message),
				},
				status: hasStatus ? Number(objError.status) : 0,
			};
		}
	}

	if (typeof error === "string") {
		return {
			success: false,
			data: {
				message: error,
			},
			status: 0,
		};
	}

	return createDefaultErrorResponse();
};
