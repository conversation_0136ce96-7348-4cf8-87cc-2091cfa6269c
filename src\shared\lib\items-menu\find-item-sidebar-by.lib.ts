// import { IItemSidebar } from "@/layout/types/sidebar-active-item.type";

// export function findBreadcrumbChain(id: string, items: IItemSidebar[], chain: IItemSidebar[] = []): IItemSidebar[] | null {
// 	for (const item of items) {
// 		if (item.id === id) {
// 			return [...chain, item];
// 		}
// 		if (item.subItems && item.subItems.length > 0) {
// 			const result = findBreadcrumbChain(id, item.subItems, [...chain, item]);
// 			if (result) return result;
// 		}
// 	}
// 	return null;
// }
