import { createEncryptedStorage } from "@/shared/lib/create-encrypted-storage.lib";

const SECRET_KEY = import.meta.env.VITE_REACT_APP_SECRET_KEY;
const encryptedStorage = createEncryptedStorage(SECRET_KEY);

export const storage = {
	getItem: async (key: string): Promise<string | null> => {
		return encryptedStorage.getItem(key);
	},
	setItem: async (key: string, value: string): Promise<void> => {
		encryptedStorage.setItem(key, value);
	},
	removeItem: async (key: string): Promise<void> => {
		encryptedStorage.removeItem(key);
	},
};
