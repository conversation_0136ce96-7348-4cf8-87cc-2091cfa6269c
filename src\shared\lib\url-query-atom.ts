import { atom } from "jotai";

export function createUrlQueryAtom<T>(key: string, initialValue: T, parser: (value: string | null) => T, serializer: (value: T) => string | null) {
	const searchParams = new URLSearchParams(window.location.search);
	const initialUrlValue = searchParams.get(key);
	const initialAtomValue = initialUrlValue !== null ? parser(initialUrlValue) : initialValue;

	const baseAtom = atom<T>(initialAtomValue);

	const derivedAtom = atom(
		get => get(baseAtom),
		(_get, set, newValue: T) => {
			const searchParams = new URLSearchParams(window.location.search);
			const serializedValue = serializer(newValue);

			if (serializedValue === null) {
				searchParams.delete(key);
			} else {
				searchParams.set(key, serializedValue);
			}

			const searchParamsString = searchParams.toString();
			const newUrl = searchParamsString ? `${window.location.pathname}?${searchParamsString}` : window.location.pathname;

			window.history.replaceState({}, "", newUrl);

			set(baseAtom, newValue);
		}
	);

	return derivedAtom;
}
