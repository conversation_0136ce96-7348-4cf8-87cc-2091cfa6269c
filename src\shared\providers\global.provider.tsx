import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { createStore, Provider as <PERSON><PERSON><PERSON>rovider } from "jotai";
import { queryClient } from "./query/create";
import { QueryProvider } from "./query/provider";

export const LMPStore = createStore();

export const GlobalProvider = ({ children }: { children: React.ReactNode }) => {
	return (
		<JotaiProvider store={LMPStore}>
			<QueryProvider client={queryClient()} Devtools={ReactQueryDevtools}>
				{children}
			</QueryProvider>
		</JotaiProvider>
	);
};
