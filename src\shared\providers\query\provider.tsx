import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

interface IQueryProviderProps {
	children: React.ReactNode;
	client: QueryClient;
	Devtools: typeof ReactQueryDevtools;
}

export const QueryProvider = ({ children, client, Devtools }: IQueryProviderProps) => {
	return (
		<QueryClientProvider client={client}>
			{children}
			<Devtools initialIsOpen={false} />
		</QueryClientProvider>
	);
};
