import { CheckCircle2, Clock, PackageCheck, Truck, XCircle } from "lucide-react";

export const orderStatusConfig: Record<string, { label: string; color: string; icon: React.ElementType }> = {
	Pendente: { label: "Pendente", color: "bg-yellow-100 text-yellow-800", icon: Clock },
	Confirmado: { label: "Confirmado", color: "bg-blue-100 text-blue-800", icon: CheckCircle2 },
	Processando: { label: "Processando", color: "bg-indigo-100 text-indigo-800", icon: Truck },
	"Em processamento": { label: "Em processamento", color: "bg-indigo-100 text-indigo-800", icon: Truck },
	Enviado: { label: "Enviado", color: "bg-cyan-100 text-cyan-800", icon: PackageCheck },
	Entregue: { label: "Entregue", color: "bg-green-100 text-green-800", icon: CheckCircle2 },
	Cancelado: { label: "Cancelado", color: "bg-red-100 text-red-700", icon: XCircle },
	Aberto: { label: "Aberto", color: "bg-yellow-100 text-yellow-800", icon: Clock },
	Finalizado: { label: "Finalizado", color: "bg-green-100 text-green-800", icon: CheckCircle2 },
	Finalizada: { label: "Finalizada", color: "bg-green-100 text-green-800", icon: CheckCircle2 },
};

export function getOrderStatusConfig(status: string) {
	return orderStatusConfig[status] || { label: status, color: "bg-gray-100 text-gray-500", icon: () => null };
}
