/** @type {import('tailwindcss').Config} */
export default {
	darkMode: ["class"],
	content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			colors: {
				mainColor: "#0197B2",
			},
			boxShadow: {
				main: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
			},
			fontFamily: {
				sans: ["Poppins", "ui-sans-serif", "system-ui"],
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
