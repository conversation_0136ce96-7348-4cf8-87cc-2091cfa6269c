import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig, loadEnv } from "vite";

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd(), "");

	return {
		plugins: [react()],
		resolve: {
			alias: {
				"@": path.resolve(__dirname, "./src"),
				"@modules": path.resolve(__dirname, "./src/modules"),
			},
			dedupe: ["react", "react-dom"],
		},
		server: {
			proxy: {
				"/api": {
					target: env.VITE_API_BASE_URL,
					changeOrigin: true,
					secure: true,
					rewrite: path => path.replace(/^\/api/, ""),
				},
			},
			allowedHosts: env.VITE_ALLOWED_HOSTS ? env.VITE_ALLOWED_HOSTS.split(",") : [],
		},
		optimizeDeps: {},
		build: {
			target: "es2015",
			minify: "esbuild",
			sourcemap: false,
			rollupOptions: {
				output: {
					manualChunks(id) {
						if (id.includes("node_modules")) {
							return id.toString().split("node_modules/")[1].split("/")[0].toString();
						}
					},
				},
			},
		},
	};
});
